package com.qxyu.yucram.presentation.animation;

/**
 * 滑动方向枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/qxyu/yucram/presentation/animation/SlideDirection;", "", "(Ljava/lang/String;I)V", "LEFT_TO_RIGHT", "RIGHT_TO_LEFT", "TOP_TO_BOTTOM", "BOTTOM_TO_TOP", "app_debug"})
public enum SlideDirection {
    /*public static final*/ LEFT_TO_RIGHT /* = new LEFT_TO_RIGHT() */,
    /*public static final*/ RIGHT_TO_LEFT /* = new RIGHT_TO_LEFT() */,
    /*public static final*/ TOP_TO_BOTTOM /* = new TOP_TO_BOTTOM() */,
    /*public static final*/ BOTTOM_TO_TOP /* = new BOTTOM_TO_TOP() */;
    
    SlideDirection() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.presentation.animation.SlideDirection> getEntries() {
        return null;
    }
}