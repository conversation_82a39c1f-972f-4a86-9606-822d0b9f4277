package com.qxyu.yucram.di;

/**
 * Hilt模块 - Repository相关依赖注入
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\'J\u0010\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\'\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/di/RepositoryModule;", "", "()V", "bindCameraRepository", "Lcom/qxyu/yucram/domain/repository/CameraRepository;", "cameraRepositoryImpl", "Lcom/qxyu/yucram/data/repository/CameraRepositoryImpl;", "bindSettingsRepository", "Lcom/qxyu/yucram/domain/repository/SettingsRepository;", "settingsRepositoryImpl", "Lcom/qxyu/yucram/data/repository/SettingsRepositoryImpl;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract class RepositoryModule {
    
    public RepositoryModule() {
        super();
    }
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.qxyu.yucram.domain.repository.CameraRepository bindCameraRepository(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.repository.CameraRepositoryImpl cameraRepositoryImpl);
    
    @dagger.Binds()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public abstract com.qxyu.yucram.domain.repository.SettingsRepository bindSettingsRepository(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.repository.SettingsRepositoryImpl settingsRepositoryImpl);
}