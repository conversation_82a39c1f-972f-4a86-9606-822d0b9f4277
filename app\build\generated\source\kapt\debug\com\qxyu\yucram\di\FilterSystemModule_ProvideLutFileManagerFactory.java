// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import android.content.Context;
import com.qxyu.yucram.filter.LutFileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSystemModule_ProvideLutFileManagerFactory implements Factory<LutFileManager> {
  private final Provider<Context> contextProvider;

  public FilterSystemModule_ProvideLutFileManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LutFileManager get() {
    return provideLutFileManager(contextProvider.get());
  }

  public static FilterSystemModule_ProvideLutFileManagerFactory create(
      Provider<Context> contextProvider) {
    return new FilterSystemModule_ProvideLutFileManagerFactory(contextProvider);
  }

  public static LutFileManager provideLutFileManager(Context context) {
    return Preconditions.checkNotNullFromProvides(FilterSystemModule.INSTANCE.provideLutFileManager(context));
  }
}
