package com.qxyu.yucram.camera;

/**
 * Camera2 API管理器
 * 负责相机的初始化、预览、拍摄等核心功能
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00ca\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000e\b\u0007\u0018\u0000 [2\u00020\u0001:\u0001[B\u001b\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\u0010\u00105\u001a\u0002062\u0006\u00107\u001a\u000208H\u0002J\u0006\u00109\u001a\u000206J\u000e\u0010:\u001a\u0002062\u0006\u0010;\u001a\u00020<J\u0006\u0010=\u001a\u000206J\b\u0010>\u001a\u000206H\u0002J\f\u0010?\u001a\b\u0012\u0004\u0012\u00020!0@J\u0010\u0010A\u001a\u0004\u0018\u00010B2\u0006\u0010C\u001a\u00020!J\u0006\u0010D\u001a\u00020%J\u0006\u0010E\u001a\u00020FJ\f\u0010G\u001a\b\u0012\u0004\u0012\u00020H0@J\u0006\u0010I\u001a\u000206J\u0006\u0010*\u001a\u00020+J\u0006\u0010J\u001a\u00020+J\u0006\u0010K\u001a\u00020+J\u0006\u0010,\u001a\u00020+J\u0010\u0010L\u001a\u0002062\u0006\u0010M\u001a\u00020NH\u0016J\u0012\u0010O\u001a\u0002062\b\b\u0002\u0010C\u001a\u00020!H\u0007J\u000e\u0010P\u001a\u0002062\u0006\u0010Q\u001a\u00020%J\u000e\u0010R\u001a\u0002062\u0006\u0010S\u001a\u00020+J\u000e\u0010T\u001a\u0002062\u0006\u0010U\u001a\u000200J\u000e\u0010V\u001a\u0002062\u0006\u0010S\u001a\u00020+J\b\u0010W\u001a\u000206H\u0002J\b\u0010X\u001a\u000206H\u0002J\b\u0010Y\u001a\u000206H\u0002J\b\u0010Z\u001a\u000206H\u0002R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\n\u001a\u0004\u0018\u00010\u000bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u000e\u001a\u00020\u000f8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0012\u0010\u0013\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\t0\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u000e\u0010\u0018\u001a\u00020\u0019X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001a\u001a\u0004\u0018\u00010\u001bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\"\u001a\u0004\u0018\u00010#X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020%X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0004\u001a\u0004\u0018\u00010\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010&\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010*\u001a\u00020+X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010,\u001a\u00020+X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010-\u001a\u00020.X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010/\u001a\u0004\u0018\u000100X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00101\u001a\u00020.X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u00102\u001a\u0004\u0018\u00010\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u00103\u001a\u000204X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\\"}, d2 = {"Lcom/qxyu/yucram/camera/CameraManager;", "Landroidx/lifecycle/DefaultLifecycleObserver;", "context", "Landroid/content/Context;", "imageProcessingPipeline", "Lcom/qxyu/yucram/filter/ImageProcessingPipeline;", "(Landroid/content/Context;Lcom/qxyu/yucram/filter/ImageProcessingPipeline;)V", "_cameraState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/camera/CameraState;", "cameraDevice", "Landroid/hardware/camera2/CameraDevice;", "cameraHandler", "Landroid/os/Handler;", "cameraManager", "Landroid/hardware/camera2/CameraManager;", "getCameraManager", "()Landroid/hardware/camera2/CameraManager;", "cameraManager$delegate", "Lkotlin/Lazy;", "cameraState", "Lkotlinx/coroutines/flow/StateFlow;", "getCameraState", "()Lkotlinx/coroutines/flow/StateFlow;", "cameraStateCallback", "Landroid/hardware/camera2/CameraDevice$StateCallback;", "cameraThread", "Landroid/os/HandlerThread;", "captureCallback", "Landroid/hardware/camera2/CameraCaptureSession$CaptureCallback;", "captureSession", "Landroid/hardware/camera2/CameraCaptureSession;", "currentCameraId", "", "currentCaptureResult", "Landroid/hardware/camera2/TotalCaptureResult;", "currentFlashMode", "Lcom/qxyu/yucram/domain/model/FlashMode;", "imageReader", "Landroid/media/ImageReader;", "imageSaver", "Lcom/qxyu/yucram/utils/ImageSaver;", "isHdrEnabled", "", "isRawEnabled", "jpegImageAvailableListener", "Landroid/media/ImageReader$OnImageAvailableListener;", "previewSurface", "Landroid/view/Surface;", "rawImageAvailableListener", "rawImageReader", "sessionStateCallback", "Landroid/hardware/camera2/CameraCaptureSession$StateCallback;", "applyFlashMode", "", "requestBuilder", "Landroid/hardware/camera2/CaptureRequest$Builder;", "capturePhoto", "capturePhotoWithFilter", "filterPreset", "Lcom/qxyu/yucram/domain/model/FilterPreset;", "closeCamera", "createCaptureSession", "getAvailableCameras", "", "getCameraCharacteristics", "Landroid/hardware/camera2/CameraCharacteristics;", "cameraId", "getCurrentFlashMode", "getDeviceInfo", "Lcom/qxyu/yucram/utils/DeviceInfo;", "getOppoProprietaryFormats", "Lcom/qxyu/yucram/utils/ProprietaryFormat;", "initialize", "isHdrSupported", "isOppoFindX8Ultra", "onDestroy", "owner", "Landroidx/lifecycle/LifecycleOwner;", "openCamera", "setFlashMode", "flashMode", "setHdrEnabled", "enabled", "setPreviewSurface", "surface", "setRawEnabled", "startCameraThread", "startPreview", "stopCameraThread", "updatePreviewSettings", "Companion", "app_debug"})
public final class CameraManager implements androidx.lifecycle.DefaultLifecycleObserver {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.filter.ImageProcessingPipeline imageProcessingPipeline = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "CameraManager";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CAMERA_THREAD_NAME = "CameraThread";
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy cameraManager$delegate = null;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.camera2.CameraDevice cameraDevice;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.camera2.CameraCaptureSession captureSession;
    @org.jetbrains.annotations.Nullable()
    private android.media.ImageReader imageReader;
    @org.jetbrains.annotations.Nullable()
    private android.media.ImageReader rawImageReader;
    @org.jetbrains.annotations.Nullable()
    private android.os.HandlerThread cameraThread;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler cameraHandler;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.camera.CameraState> _cameraState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.camera.CameraState> cameraState = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String currentCameraId = "0";
    @org.jetbrains.annotations.Nullable()
    private android.view.Surface previewSurface;
    @org.jetbrains.annotations.NotNull()
    private com.qxyu.yucram.domain.model.FlashMode currentFlashMode = com.qxyu.yucram.domain.model.FlashMode.AUTO;
    private boolean isRawEnabled = false;
    private boolean isHdrEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.utils.ImageSaver imageSaver = null;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.camera2.TotalCaptureResult currentCaptureResult;
    @org.jetbrains.annotations.NotNull()
    private final android.hardware.camera2.CameraDevice.StateCallback cameraStateCallback = null;
    @org.jetbrains.annotations.NotNull()
    private final android.hardware.camera2.CameraCaptureSession.StateCallback sessionStateCallback = null;
    @org.jetbrains.annotations.NotNull()
    private final android.hardware.camera2.CameraCaptureSession.CaptureCallback captureCallback = null;
    @org.jetbrains.annotations.NotNull()
    private final android.media.ImageReader.OnImageAvailableListener jpegImageAvailableListener = null;
    @org.jetbrains.annotations.NotNull()
    private final android.media.ImageReader.OnImageAvailableListener rawImageAvailableListener = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.camera.CameraManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public CameraManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.filter.ImageProcessingPipeline imageProcessingPipeline) {
        super();
    }
    
    private final android.hardware.camera2.CameraManager getCameraManager() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.camera.CameraState> getCameraState() {
        return null;
    }
    
    /**
     * 初始化相机
     */
    public final void initialize() {
    }
    
    /**
     * 打开相机
     */
    @android.annotation.SuppressLint(value = {"MissingPermission"})
    public final void openCamera(@org.jetbrains.annotations.NotNull()
    java.lang.String cameraId) {
    }
    
    /**
     * 关闭相机
     */
    public final void closeCamera() {
    }
    
    /**
     * 设置预览Surface
     */
    public final void setPreviewSurface(@org.jetbrains.annotations.NotNull()
    android.view.Surface surface) {
    }
    
    /**
     * 拍摄照片
     */
    public final void capturePhoto() {
    }
    
    /**
     * 专业RAW拍摄（带滤镜处理）
     */
    public final void capturePhotoWithFilter(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.FilterPreset filterPreset) {
    }
    
    /**
     * 获取可用相机列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAvailableCameras() {
        return null;
    }
    
    /**
     * 获取相机特性
     */
    @org.jetbrains.annotations.Nullable()
    public final android.hardware.camera2.CameraCharacteristics getCameraCharacteristics(@org.jetbrains.annotations.NotNull()
    java.lang.String cameraId) {
        return null;
    }
    
    /**
     * 设置闪光灯模式
     */
    public final void setFlashMode(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.FlashMode flashMode) {
    }
    
    /**
     * 获取当前闪光灯模式
     */
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.FlashMode getCurrentFlashMode() {
        return null;
    }
    
    /**
     * 设置RAW格式
     */
    public final void setRawEnabled(boolean enabled) {
    }
    
    /**
     * 获取RAW格式状态
     */
    public final boolean isRawEnabled() {
        return false;
    }
    
    /**
     * 设置HDR模式
     */
    public final void setHdrEnabled(boolean enabled) {
    }
    
    /**
     * 获取HDR状态
     */
    public final boolean isHdrEnabled() {
        return false;
    }
    
    /**
     * 检查设备是否支持HDR
     */
    public final boolean isHdrSupported() {
        return false;
    }
    
    /**
     * 检查设备是否支持OPPO专有格式
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.utils.ProprietaryFormat> getOppoProprietaryFormats() {
        return null;
    }
    
    /**
     * 检查是否为OPPO Find X8 Ultra
     */
    public final boolean isOppoFindX8Ultra() {
        return false;
    }
    
    /**
     * 获取设备信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.utils.DeviceInfo getDeviceInfo() {
        return null;
    }
    
    private final void startCameraThread() {
    }
    
    private final void stopCameraThread() {
    }
    
    private final void createCaptureSession() {
    }
    
    private final void startPreview() {
    }
    
    /**
     * 更新预览设置
     */
    private final void updatePreviewSettings() {
    }
    
    /**
     * 应用闪光灯模式到请求构建器
     */
    private final void applyFlashMode(android.hardware.camera2.CaptureRequest.Builder requestBuilder) {
    }
    
    @java.lang.Override()
    public void onDestroy(@org.jetbrains.annotations.NotNull()
    androidx.lifecycle.LifecycleOwner owner) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/qxyu/yucram/camera/CameraManager$Companion;", "", "()V", "CAMERA_THREAD_NAME", "", "TAG", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}