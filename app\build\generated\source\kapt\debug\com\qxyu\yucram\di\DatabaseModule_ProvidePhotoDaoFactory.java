// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import com.qxyu.yucram.data.local.YucramDatabase;
import com.qxyu.yucram.data.local.dao.PhotoDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvidePhotoDaoFactory implements Factory<PhotoDao> {
  private final Provider<YucramDatabase> databaseProvider;

  public DatabaseModule_ProvidePhotoDaoFactory(Provider<YucramDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PhotoDao get() {
    return providePhotoDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvidePhotoDaoFactory create(
      Provider<YucramDatabase> databaseProvider) {
    return new DatabaseModule_ProvidePhotoDaoFactory(databaseProvider);
  }

  public static PhotoDao providePhotoDao(YucramDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePhotoDao(database));
  }
}
