package com.qxyu.yucram.camera

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.ImageFormat
import android.hardware.camera2.*
import android.hardware.camera2.params.OutputConfiguration
import android.hardware.camera2.params.SessionConfiguration
import android.media.ImageReader
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.util.Size
import android.view.Surface
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.Executor
import javax.inject.Inject
import javax.inject.Singleton
import com.qxyu.yucram.domain.model.FlashMode
import com.qxyu.yucram.domain.model.FilterPreset
import com.qxyu.yucram.domain.model.ImageSourceFormat
import com.qxyu.yucram.utils.ImageSaver
import com.qxyu.yucram.utils.DeviceAdapter
import com.qxyu.yucram.filter.ImageProcessingPipeline
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Camera2 API管理器
 * 负责相机的初始化、预览、拍摄等核心功能
 */
@Singleton
class CameraManager @Inject constructor(
    private val context: Context,
    private val imageProcessingPipeline: ImageProcessingPipeline? = null
) : DefaultLifecycleObserver {
    
    companion object {
        private const val TAG = "CameraManager"
        private const val CAMERA_THREAD_NAME = "CameraThread"
    }
    
    private val cameraManager: android.hardware.camera2.CameraManager by lazy {
        context.getSystemService(Context.CAMERA_SERVICE) as android.hardware.camera2.CameraManager
    }
    
    private var cameraDevice: CameraDevice? = null
    private var captureSession: CameraCaptureSession? = null
    private var imageReader: ImageReader? = null
    private var rawImageReader: ImageReader? = null
    
    // 相机线程
    private var cameraThread: HandlerThread? = null
    private var cameraHandler: Handler? = null
    
    // 相机状态
    private val _cameraState = MutableStateFlow(CameraState.CLOSED)
    val cameraState: StateFlow<CameraState> = _cameraState.asStateFlow()
    
    // 当前相机ID
    private var currentCameraId: String = "0"
    
    // 预览Surface
    private var previewSurface: Surface? = null

    // 当前闪光灯模式
    private var currentFlashMode: FlashMode = FlashMode.AUTO

    // RAW格式设置
    private var isRawEnabled: Boolean = false

    // HDR设置
    private var isHdrEnabled: Boolean = false

    // 图像保存器
    private val imageSaver = ImageSaver(context)

    // 当前拍摄结果（用于RAW保存）
    private var currentCaptureResult: TotalCaptureResult? = null
    
    /**
     * 初始化相机
     */
    fun initialize() {
        startCameraThread()
        _cameraState.value = CameraState.INITIALIZING
    }
    
    /**
     * 打开相机
     */
    @SuppressLint("MissingPermission")
    fun openCamera(cameraId: String = "0") {
        try {
            currentCameraId = cameraId
            _cameraState.value = CameraState.OPENING
            
            cameraManager.openCamera(cameraId, cameraStateCallback, cameraHandler)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open camera", e)
            _cameraState.value = CameraState.ERROR
        }
    }
    
    /**
     * 关闭相机
     */
    fun closeCamera() {
        captureSession?.close()
        captureSession = null
        
        cameraDevice?.close()
        cameraDevice = null
        
        imageReader?.close()
        imageReader = null
        
        _cameraState.value = CameraState.CLOSED
    }
    
    /**
     * 设置预览Surface
     */
    fun setPreviewSurface(surface: Surface) {
        previewSurface = surface
        if (cameraDevice != null) {
            createCaptureSession()
        }
    }
    
    /**
     * 拍摄照片
     */
    fun capturePhoto() {
        if (_cameraState.value != CameraState.PREVIEW) {
            Log.w(TAG, "Camera not ready for capture")
            return
        }

        try {
            _cameraState.value = CameraState.CAPTURING

            // 创建拍摄请求
            val captureBuilder = cameraDevice?.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE)

            // 添加JPEG目标
            captureBuilder?.addTarget(imageReader?.surface!!)

            // 如果启用RAW，添加RAW目标
            if (isRawEnabled && rawImageReader != null) {
                captureBuilder?.addTarget(rawImageReader!!.surface)
            }

            // 设置自动对焦和自动曝光
            captureBuilder?.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE)
            captureBuilder?.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)

            // 应用闪光灯设置
            captureBuilder?.let { applyFlashMode(it) }

            // 设置最高质量
            captureBuilder?.set(CaptureRequest.JPEG_QUALITY, 100.toByte())

            val captureRequest = captureBuilder?.build()
            captureSession?.capture(captureRequest!!, captureCallback, cameraHandler)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to capture photo", e)
            _cameraState.value = CameraState.PREVIEW
        }
    }

    /**
     * 专业RAW拍摄（带滤镜处理）
     */
    fun capturePhotoWithFilter(filterPreset: FilterPreset) {
        if (_cameraState.value != CameraState.PREVIEW) {
            Log.w(TAG, "Camera not ready for professional capture")
            return
        }

        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "开始专业RAW拍摄，滤镜: ${filterPreset.name}")
                _cameraState.value = CameraState.CAPTURING

                // TODO: 实现真正的RAW捕获和处理
                // 1. 强制启用RAW模式
                val wasRawEnabled = isRawEnabled
                if (!wasRawEnabled) {
                    setRawEnabled(true)
                }

                // 2. 捕获RAW+JPEG
                capturePhoto()

                // 3. 等待捕获完成，然后进行专业处理
                kotlinx.coroutines.delay(1000) // 等待捕获完成

                // 4. 这里将来会调用RAW到LOG处理管道
                // val processedImage = imageProcessingPipeline.processImage(
                //     rawData, ImageSourceFormat.RAW, filterPreset, captureResult
                // )

                // 模拟专业处理时间
                kotlinx.coroutines.delay(2000)

                // 恢复原始RAW设置
                if (!wasRawEnabled) {
                    setRawEnabled(false)
                }

                CoroutineScope(Dispatchers.Main).launch {
                    _cameraState.value = CameraState.PREVIEW
                    Log.d(TAG, "专业RAW拍摄完成，滤镜: ${filterPreset.name}")
                }

            } catch (e: Exception) {
                Log.e(TAG, "专业RAW拍摄失败", e)
                CoroutineScope(Dispatchers.Main).launch {
                    _cameraState.value = CameraState.PREVIEW
                }
            }
        }
    }

    /**
     * 获取可用相机列表
     */
    fun getAvailableCameras(): List<String> {
        return try {
            cameraManager.cameraIdList.toList()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get camera list", e)
            emptyList()
        }
    }
    
    /**
     * 获取相机特性
     */
    fun getCameraCharacteristics(cameraId: String): CameraCharacteristics? {
        return try {
            cameraManager.getCameraCharacteristics(cameraId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to get camera characteristics", e)
            null
        }
    }

    /**
     * 设置闪光灯模式
     */
    fun setFlashMode(flashMode: FlashMode) {
        currentFlashMode = flashMode
        updatePreviewSettings()
    }

    /**
     * 获取当前闪光灯模式
     */
    fun getCurrentFlashMode(): FlashMode = currentFlashMode

    /**
     * 设置RAW格式
     */
    fun setRawEnabled(enabled: Boolean) {
        isRawEnabled = enabled
        // 重新创建capture session以支持RAW
        if (cameraDevice != null && previewSurface != null) {
            createCaptureSession()
        }
    }

    /**
     * 获取RAW格式状态
     */
    fun isRawEnabled(): Boolean = isRawEnabled

    /**
     * 设置HDR模式
     */
    fun setHdrEnabled(enabled: Boolean) {
        isHdrEnabled = enabled
        updatePreviewSettings()
    }

    /**
     * 获取HDR状态
     */
    fun isHdrEnabled(): Boolean = isHdrEnabled

    /**
     * 检查设备是否支持HDR
     */
    fun isHdrSupported(): Boolean {
        val characteristics = getCameraCharacteristics(currentCameraId)
        return characteristics?.let { imageSaver.isHdrSupported(it) } ?: false
    }

    /**
     * 检查设备是否支持OPPO专有格式
     */
    fun getOppoProprietaryFormats(): List<com.qxyu.yucram.utils.ProprietaryFormat> {
        return DeviceAdapter.getSupportedProprietaryFormats(context)
    }

    /**
     * 检查是否为OPPO Find X8 Ultra
     */
    fun isOppoFindX8Ultra(): Boolean {
        return DeviceAdapter.isOppoFindX8Ultra()
    }

    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): com.qxyu.yucram.utils.DeviceInfo {
        return DeviceAdapter.getDeviceInfo()
    }
    
    private fun startCameraThread() {
        cameraThread = HandlerThread(CAMERA_THREAD_NAME).apply {
            start()
            cameraHandler = Handler(looper)
        }
    }
    
    private fun stopCameraThread() {
        cameraThread?.quitSafely()
        try {
            cameraThread?.join()
            cameraThread = null
            cameraHandler = null
        } catch (e: InterruptedException) {
            Log.e(TAG, "Camera thread interrupted", e)
        }
    }
    
    private fun createCaptureSession() {
        val surface = previewSurface ?: return
        val device = cameraDevice ?: return

        try {
            // 获取相机特性以确定最大分辨率
            val characteristics = getCameraCharacteristics(currentCameraId)
            val map = characteristics?.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)

            // 获取JPEG和RAW的最大尺寸
            val jpegSizes = map?.getOutputSizes(ImageFormat.JPEG)
            val rawSizes = map?.getOutputSizes(ImageFormat.RAW_SENSOR)

            val jpegSize = jpegSizes?.maxByOrNull { it.width * it.height } ?: Size(1920, 1080)

            // 创建JPEG ImageReader
            imageReader?.close()
            imageReader = ImageReader.newInstance(
                jpegSize.width,
                jpegSize.height,
                ImageFormat.JPEG,
                1
            ).apply {
                setOnImageAvailableListener(jpegImageAvailableListener, cameraHandler)
            }

            val outputs = mutableListOf<OutputConfiguration>().apply {
                add(OutputConfiguration(surface))
                add(OutputConfiguration(imageReader!!.surface))
            }

            // 如果启用RAW且设备支持，添加RAW ImageReader
            if (isRawEnabled && rawSizes != null && rawSizes.isNotEmpty()) {
                val rawSize = rawSizes.maxByOrNull { it.width * it.height } ?: rawSizes[0]

                rawImageReader?.close()
                rawImageReader = ImageReader.newInstance(
                    rawSize.width,
                    rawSize.height,
                    ImageFormat.RAW_SENSOR,
                    1
                ).apply {
                    setOnImageAvailableListener(rawImageAvailableListener, cameraHandler)
                }

                outputs.add(OutputConfiguration(rawImageReader!!.surface))
            }

            val sessionConfig = SessionConfiguration(
                SessionConfiguration.SESSION_REGULAR,
                outputs,
                Executor { it.run() },
                sessionStateCallback
            )

            device.createCaptureSession(sessionConfig)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to create capture session", e)
            _cameraState.value = CameraState.ERROR
        }
    }
    
    private fun startPreview() {
        val surface = previewSurface ?: return
        val device = cameraDevice ?: return
        val session = captureSession ?: return

        try {
            val previewBuilder = device.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
            previewBuilder.addTarget(surface)

            // 设置连续自动对焦
            previewBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE)
            previewBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)

            // 设置闪光灯模式
            applyFlashMode(previewBuilder)

            val previewRequest = previewBuilder.build()
            session.setRepeatingRequest(previewRequest, null, cameraHandler)

            _cameraState.value = CameraState.PREVIEW

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start preview", e)
            _cameraState.value = CameraState.ERROR
        }
    }

    /**
     * 更新预览设置
     */
    private fun updatePreviewSettings() {
        val surface = previewSurface ?: return
        val device = cameraDevice ?: return
        val session = captureSession ?: return

        try {
            val previewBuilder = device.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW)
            previewBuilder.addTarget(surface)

            previewBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE)
            previewBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)

            // 应用闪光灯设置
            applyFlashMode(previewBuilder)

            val previewRequest = previewBuilder.build()
            session.setRepeatingRequest(previewRequest, null, cameraHandler)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to update preview settings", e)
        }
    }

    /**
     * 应用闪光灯模式到请求构建器
     */
    private fun applyFlashMode(requestBuilder: CaptureRequest.Builder) {
        when (currentFlashMode) {
            FlashMode.OFF -> {
                requestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)
                requestBuilder.set(CaptureRequest.FLASH_MODE, CaptureRequest.FLASH_MODE_OFF)
            }
            FlashMode.AUTO -> {
                requestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH)
                requestBuilder.set(CaptureRequest.FLASH_MODE, CaptureRequest.FLASH_MODE_OFF)
            }
            FlashMode.ON -> {
                requestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON_ALWAYS_FLASH)
                requestBuilder.set(CaptureRequest.FLASH_MODE, CaptureRequest.FLASH_MODE_OFF)
            }
            FlashMode.TORCH -> {
                requestBuilder.set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)
                requestBuilder.set(CaptureRequest.FLASH_MODE, CaptureRequest.FLASH_MODE_TORCH)
            }
        }

        // 应用HDR设置
        if (isHdrEnabled) {
            requestBuilder.set(CaptureRequest.CONTROL_SCENE_MODE, CaptureRequest.CONTROL_SCENE_MODE_HDR)
            requestBuilder.set(CaptureRequest.CONTROL_MODE, CaptureRequest.CONTROL_MODE_USE_SCENE_MODE)
        } else {
            requestBuilder.set(CaptureRequest.CONTROL_MODE, CaptureRequest.CONTROL_MODE_AUTO)
        }
    }
    
    // 相机设备状态回调
    private val cameraStateCallback = object : CameraDevice.StateCallback() {
        override fun onOpened(camera: CameraDevice) {
            cameraDevice = camera
            _cameraState.value = CameraState.OPENED
            
            if (previewSurface != null) {
                createCaptureSession()
            }
        }
        
        override fun onDisconnected(camera: CameraDevice) {
            camera.close()
            cameraDevice = null
            _cameraState.value = CameraState.DISCONNECTED
        }
        
        override fun onError(camera: CameraDevice, error: Int) {
            camera.close()
            cameraDevice = null
            _cameraState.value = CameraState.ERROR
            Log.e(TAG, "Camera error: $error")
        }
    }
    
    // 拍摄会话状态回调
    private val sessionStateCallback = object : CameraCaptureSession.StateCallback() {
        override fun onConfigured(session: CameraCaptureSession) {
            captureSession = session
            startPreview()
        }
        
        override fun onConfigureFailed(session: CameraCaptureSession) {
            Log.e(TAG, "Capture session configuration failed")
            _cameraState.value = CameraState.ERROR
        }
    }
    
    // 拍摄回调
    private val captureCallback = object : CameraCaptureSession.CaptureCallback() {
        override fun onCaptureCompleted(
            session: CameraCaptureSession,
            request: CaptureRequest,
            result: TotalCaptureResult
        ) {
            // 保存拍摄结果用于RAW保存
            currentCaptureResult = result
            Log.d(TAG, "Photo captured successfully")
        }

        override fun onCaptureFailed(
            session: CameraCaptureSession,
            request: CaptureRequest,
            failure: CaptureFailure
        ) {
            Log.e(TAG, "Photo capture failed: ${failure.reason}")
            _cameraState.value = CameraState.PREVIEW
        }
    }
    
    // JPEG图像保存监听器
    private val jpegImageAvailableListener = ImageReader.OnImageAvailableListener { reader ->
        val image = reader.acquireLatestImage()
        try {
            val buffer = image.planes[0].buffer
            val bytes = ByteArray(buffer.remaining())
            buffer.get(bytes)

            // 保存JPEG图像
            CoroutineScope(Dispatchers.IO).launch {
                val result = if (isHdrEnabled) {
                    imageSaver.saveHdrImage(bytes)
                } else {
                    imageSaver.saveJpegImage(bytes)
                }

                result.onSuccess { uri ->
                    Log.d(TAG, "JPEG image saved successfully: $uri")
                }.onFailure { error ->
                    Log.e(TAG, "Failed to save JPEG image", error)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to process JPEG image", e)
        } finally {
            image.close()
            _cameraState.value = CameraState.PREVIEW
        }
    }

    // RAW图像保存监听器
    private val rawImageAvailableListener = ImageReader.OnImageAvailableListener { reader ->
        val image = reader.acquireLatestImage()
        try {
            // 保存RAW图像为DNG格式
            val characteristics = getCameraCharacteristics(currentCameraId)
            val captureResult = currentCaptureResult

            if (characteristics != null && captureResult != null) {
                CoroutineScope(Dispatchers.IO).launch {
                    val result = imageSaver.saveRawImage(image, characteristics, captureResult)

                    result.onSuccess { uri ->
                        Log.d(TAG, "RAW DNG image saved successfully: $uri")
                    }.onFailure { error ->
                        Log.e(TAG, "Failed to save RAW image", error)
                    }
                }
            } else {
                Log.e(TAG, "Missing characteristics or capture result for RAW save")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to process RAW image", e)
        } finally {
            image.close()
        }
    }



    override fun onDestroy(owner: LifecycleOwner) {
        closeCamera()
        stopCameraThread()
    }
}

/**
 * 相机状态枚举
 */
enum class CameraState {
    CLOSED,
    INITIALIZING,
    OPENING,
    OPENED,
    PREVIEW,
    CAPTURING,
    DISCONNECTED,
    ERROR
}
