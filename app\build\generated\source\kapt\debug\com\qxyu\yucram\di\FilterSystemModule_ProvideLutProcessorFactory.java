// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import android.content.Context;
import com.qxyu.yucram.film.LutProcessor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSystemModule_ProvideLutProcessorFactory implements Factory<LutProcessor> {
  private final Provider<Context> contextProvider;

  public FilterSystemModule_ProvideLutProcessorFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public LutProcessor get() {
    return provideLutProcessor(contextProvider.get());
  }

  public static FilterSystemModule_ProvideLutProcessorFactory create(
      Provider<Context> contextProvider) {
    return new FilterSystemModule_ProvideLutProcessorFactory(contextProvider);
  }

  public static LutProcessor provideLutProcessor(Context context) {
    return Preconditions.checkNotNullFromProvides(FilterSystemModule.INSTANCE.provideLutProcessor(context));
  }
}
