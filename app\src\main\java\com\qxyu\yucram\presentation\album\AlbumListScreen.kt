package com.qxyu.yucram.presentation.album

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*

/**
 * 相册列表界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlbumListScreen(
    onNavigateToAlbum: (String) -> Unit = {},
    onNavigateToCreateAlbum: () -> Unit = {},
    onNavigateBack: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: AlbumListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val albums by viewModel.albums.collectAsStateWithLifecycle()
    val systemAlbums by viewModel.systemAlbums.collectAsStateWithLifecycle()
    val userAlbums by viewModel.userAlbums.collectAsStateWithLifecycle()
    
    var showCreateDialog by remember { mutableStateOf(false) }
    var viewMode by remember { mutableStateOf(AlbumViewMode.GRID) }
    
    LaunchedEffect(Unit) {
        viewModel.loadAlbums()
    }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        AlbumListTopBar(
            viewMode = viewMode,
            onViewModeChanged = { viewMode = it },
            onCreateAlbum = { showCreateDialog = true },
            onNavigateBack = onNavigateBack
        )
        
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.error != null -> {
                AlbumListError(
                    error = uiState.error!!,
                    onRetry = { viewModel.loadAlbums() },
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            else -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(24.dp)
                ) {
                    // 系统相册
                    if (systemAlbums.isNotEmpty()) {
                        item {
                            AlbumSection(
                                title = "系统相册",
                                albums = systemAlbums,
                                viewMode = viewMode,
                                onAlbumClick = onNavigateToAlbum
                            )
                        }
                    }
                    
                    // 用户相册
                    item {
                        AlbumSection(
                            title = "我的相册",
                            albums = userAlbums,
                            viewMode = viewMode,
                            onAlbumClick = onNavigateToAlbum,
                            showCreateButton = true,
                            onCreateClick = { showCreateDialog = true }
                        )
                    }
                }
            }
        }
    }
    
    // 创建相册对话框
    if (showCreateDialog) {
        CreateAlbumDialog(
            onConfirm = { name, description ->
                viewModel.createAlbum(name, description)
                showCreateDialog = false
            },
            onDismiss = { showCreateDialog = false }
        )
    }
}

/**
 * 相册列表顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlbumListTopBar(
    viewMode: AlbumViewMode,
    onViewModeChanged: (AlbumViewMode) -> Unit,
    onCreateAlbum: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Text(
                text = "相册",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回"
                )
            }
        },
        actions = {
            // 视图模式切换
            IconButton(
                onClick = {
                    onViewModeChanged(
                        if (viewMode == AlbumViewMode.GRID) AlbumViewMode.LIST else AlbumViewMode.GRID
                    )
                }
            ) {
                Icon(
                    imageVector = if (viewMode == AlbumViewMode.GRID) Icons.Filled.ViewList else Icons.Filled.GridView,
                    contentDescription = "切换视图"
                )
            }
            
            // 创建相册
            IconButton(onClick = onCreateAlbum) {
                Icon(
                    imageVector = Icons.Filled.Add,
                    contentDescription = "创建相册"
                )
            }
        },
        modifier = modifier
    )
}

/**
 * 相册分组
 */
@Composable
fun AlbumSection(
    title: String,
    albums: List<Album>,
    viewMode: AlbumViewMode,
    onAlbumClick: (String) -> Unit,
    modifier: Modifier = Modifier,
    showCreateButton: Boolean = false,
    onCreateClick: () -> Unit = {}
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 分组标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
            
            if (showCreateButton) {
                TextButton(onClick = onCreateClick) {
                    Icon(
                        imageVector = Icons.Filled.Add,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("新建")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 相册列表
        when (viewMode) {
            AlbumViewMode.GRID -> {
                LazyVerticalGrid(
                    columns = GridCells.Fixed(2),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.height(((albums.size + 1) / 2 * 200).dp)
                ) {
                    items(albums) { album ->
                        AlbumGridItem(
                            album = album,
                            onClick = { onAlbumClick(album.id) }
                        )
                    }
                }
            }
            
            AlbumViewMode.LIST -> {
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    albums.forEach { album ->
                        AlbumListItem(
                            album = album,
                            onClick = { onAlbumClick(album.id) }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 相册网格项
 */
@Composable
fun AlbumGridItem(
    album: Album,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .height(180.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box {
            // 封面图片
            if (album.coverPhotoId != null) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data("") // TODO: 获取封面图片路径
                        .crossfade(true)
                        .build(),
                    contentDescription = album.name,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            album.color?.color ?: MaterialTheme.colorScheme.surfaceVariant
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Filled.PhotoLibrary,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 渐变遮罩
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        androidx.compose.ui.graphics.Brush.verticalGradient(
                            colors = listOf(
                                Color.Transparent,
                                Color.Black.copy(alpha = 0.7f)
                            ),
                            startY = 0f,
                            endY = Float.POSITIVE_INFINITY
                        )
                    )
            )
            
            // 相册信息
            Column(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(12.dp)
            ) {
                Text(
                    text = album.name,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Text(
                    text = "${album.photoCount} 张照片",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.8f)
                )
            }
            
            // 相册类型标识
            if (album.isSystemAlbum) {
                Icon(
                    imageVector = Icons.Filled.Star,
                    contentDescription = "系统相册",
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(8.dp)
                        .size(16.dp),
                    tint = Color.Yellow
                )
            }
        }
    }
}

/**
 * 相册列表项
 */
@Composable
fun AlbumListItem(
    album: Album,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 封面缩略图
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(
                        album.color?.color ?: MaterialTheme.colorScheme.surfaceVariant
                    ),
                contentAlignment = Alignment.Center
            ) {
                if (album.coverPhotoId != null) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data("") // TODO: 获取封面图片路径
                            .crossfade(true)
                            .build(),
                        contentDescription = album.name,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize()
                    )
                } else {
                    Icon(
                        imageVector = Icons.Filled.PhotoLibrary,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 相册信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = album.name,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f)
                    )
                    
                    if (album.isSystemAlbum) {
                        Icon(
                            imageVector = Icons.Filled.Star,
                            contentDescription = "系统相册",
                            modifier = Modifier.size(16.dp),
                            tint = Color.Yellow
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "${album.photoCount} 张照片",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                album.description?.let { description ->
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 箭头图标
            Icon(
                imageVector = Icons.Filled.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 创建相册对话框
 */
@Composable
fun CreateAlbumDialog(
    onConfirm: (String, String) -> Unit,
    onDismiss: () -> Unit
) {
    var albumName by remember { mutableStateOf("") }
    var albumDescription by remember { mutableStateOf("") }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("创建相册")
        },
        text = {
            Column {
                OutlinedTextField(
                    value = albumName,
                    onValueChange = { albumName = it },
                    label = { Text("相册名称") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = albumDescription,
                    onValueChange = { albumDescription = it },
                    label = { Text("描述（可选）") },
                    maxLines = 3,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (albumName.isNotBlank()) {
                        onConfirm(albumName.trim(), albumDescription.trim())
                    }
                },
                enabled = albumName.isNotBlank()
            ) {
                Text("创建")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 相册列表错误状态
 */
@Composable
fun AlbumListError(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Filled.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "加载失败",
            style = MaterialTheme.typography.headlineSmall
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(onClick = onRetry) {
            Text("重试")
        }
    }
}

/**
 * 相册视图模式
 */
enum class AlbumViewMode {
    GRID,   // 网格视图
    LIST    // 列表视图
}
