Dapp/src/main/java/com/qxyu/yucram/filter/DeviceCapabilityDetector.kt1app/src/main/java/com/qxyu/yucram/di/AppModule.kt:app/src/main/java/com/qxyu/yucram/utils/PermissionUtils.kt3app/src/main/java/com/qxyu/yucram/ui/theme/Theme.kt7app/src/main/java/com/qxyu/yucram/domain/model/Photo.kt9app/src/main/java/com/qxyu/yucram/camera/CameraManager.ktPapp/src/main/java/com/qxyu/yucram/presentation/permission/PermissionViewModel.kt.app/src/main/java/com/qxyu/yucram/YucramApp.ktGapp/src/main/java/com/qxyu/yucram/domain/repository/CameraRepository.kt@app/src/main/java/com/qxyu/yucram/navigation/YucramNavigation.kt@app/src/main/java/com/qxyu/yucram/domain/model/FilmSimulation.kt@app/src/main/java/com/qxyu/yucram/domain/model/CameraSettings.ktKapp/src/main/java/com/qxyu/yucram/presentation/components/NavigationRail.ktPapp/src/main/java/com/qxyu/yucram/presentation/components/BottomNavigationBar.kt;app/src/main/java/com/qxyu/yucram/presentation/YucramApp.ktPapp/src/main/java/com/qxyu/yucram/presentation/animation/NavigationAnimations.ktKapp/src/main/java/com/qxyu/yucram/data/repository/SettingsRepositoryImpl.kt>app/src/main/java/com/qxyu/yucram/data/local/YucramDatabase.ktIapp/src/main/java/com/qxyu/yucram/data/repository/CameraRepositoryImpl.kt<app/src/main/java/com/qxyu/yucram/data/local/dao/PhotoDao.ktBapp/src/main/java/com/qxyu/yucram/data/local/entity/PhotoEntity.kt2app/src/main/java/com/qxyu/yucram/ui/theme/Type.ktQapp/src/main/java/com/qxyu/yucram/presentation/filter/FilterSelectionViewModel.ktEapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraScreen.kt8app/src/main/java/com/qxyu/yucram/di/RepositoryModule.ktEapp/src/main/java/com/qxyu/yucram/data/local/entity/SettingsEntity.kt6app/src/main/java/com/qxyu/yucram/film/LutProcessor.kt8app/src/main/java/com/qxyu/yucram/utils/DeviceAdapter.ktMapp/src/main/java/com/qxyu/yucram/presentation/permission/PermissionScreen.ktGapp/src/main/java/com/qxyu/yucram/presentation/gallery/GalleryScreen.kt3app/src/main/java/com/qxyu/yucram/ui/theme/Color.kt8app/src/main/java/com/qxyu/yucram/domain/model/Filter.ktIapp/src/main/java/com/qxyu/yucram/presentation/settings/SettingsScreen.ktFapp/src/main/java/com/qxyu/yucram/domain/repository/PhotoRepository.kt6app/src/main/java/com/qxyu/yucram/di/DatabaseModule.ktBapp/src/main/java/com/qxyu/yucram/navigation/YucramDestinations.ktHapp/src/main/java/com/qxyu/yucram/data/repository/PhotoRepositoryImpl.ktNapp/src/main/java/com/qxyu/yucram/presentation/filter/FilterSelectionScreen.ktDapp/src/main/java/com/qxyu/yucram/presentation/camera/GridOverlay.kt?app/src/main/java/com/qxyu/yucram/data/local/dao/SettingsDao.ktCapp/src/main/java/com/qxyu/yucram/filter/ImageProcessingPipeline.ktKapp/src/main/java/com/qxyu/yucram/presentation/camera/OppoAdaptationInfo.kt:app/src/main/java/com/qxyu/yucram/filter/LutFileManager.kt<app/src/main/java/com/qxyu/yucram/di/FilmSimulationModule.ktHapp/src/main/java/com/qxyu/yucram/presentation/utils/ResponsiveLayout.kt5app/src/main/java/com/qxyu/yucram/utils/ImageSaver.ktHapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraViewModel.kt6app/src/main/java/com/qxyu/yucram/YucramApplication.ktIapp/src/main/java/com/qxyu/yucram/domain/repository/SettingsRepository.kt1app/src/main/java/com/qxyu/yucram/MainActivity.ktFapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraPreview.kt=app/src/main/java/com/qxyu/yucram/camera/RawCaptureManager.ktAapp/src/main/java/com/qxyu/yucram/processing/RawToLogProcessor.ktHapp/src/main/java/com/qxyu/yucram/data/repository/AlbumRepositoryImpl.ktRapp/src/main/java/com/qxyu/yucram/presentation/gallery/components/GalleryTopBar.ktNapp/src/main/java/com/qxyu/yucram/presentation/gallery/components/PhotoGrid.ktJapp/src/main/java/com/qxyu/yucram/presentation/gallery/GalleryViewModel.kt7app/src/main/java/com/qxyu/yucram/domain/model/Album.kt<app/src/main/java/com/qxyu/yucram/data/local/dao/AlbumDao.ktFapp/src/main/java/com/qxyu/yucram/domain/repository/AlbumRepository.ktDapp/src/main/java/com/qxyu/yucram/data/local/converter/Converters.ktHapp/src/main/java/com/qxyu/yucram/presentation/album/SmartAlbumScreen.ktKapp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditScreen.ktIapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumDetailScreen.ktKapp/src/main/java/com/qxyu/yucram/presentation/album/SmartAlbumViewModel.kt\app/src/main/java/com/qxyu/yucram/presentation/photodetail/components/ExifInfoBottomSheet.ktRapp/src/main/java/com/qxyu/yucram/presentation/photodetail/PhotoDetailViewModel.ktTapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/EditToolPanel.kt[app/src/main/java/com/qxyu/yucram/presentation/photoedit/components/CurveAdjustmentPanel.ktJapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumListViewModel.kt[app/src/main/java/com/qxyu/yucram/presentation/photoedit/components/BasicAdjustmentPanel.ktSapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/SliderPanels.ktWapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/PhotoEditPreview.ktGapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumListScreen.ktNapp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditViewModel.kt;app/src/main/java/com/qxyu/yucram/domain/model/PhotoEdit.ktYapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/HSLAdjustmentPanel.ktOapp/src/main/java/com/qxyu/yucram/presentation/photodetail/PhotoDetailScreen.ktDapp/src/main/java/com/qxyu/yucram/domain/model/BorderAndWatermark.kt_app/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderWatermarkPreview.kt[app/src/main/java/com/qxyu/yucram/presentation/borderwater/components/WatermarkEditPanel.ktSapp/src/main/java/com/qxyu/yucram/presentation/borderwater/BorderWatermarkScreen.ktXapp/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderEditPanel.ktaapp/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderWatermarkEditPanel.ktVapp/src/main/java/com/qxyu/yucram/presentation/borderwater/BorderWatermarkViewModel.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     