package com.qxyu.yucram.presentation.album

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.gallery.components.PhotoGrid
import java.time.format.DateTimeFormatter

/**
 * 相册详情界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlbumDetailScreen(
    albumId: String,
    onNavigateBack: () -> Unit = {},
    onNavigateToPhoto: (String) -> Unit = {},
    onNavigateToEdit: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: AlbumDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val album by viewModel.album.collectAsStateWithLifecycle()
    val photos by viewModel.photos.collectAsStateWithLifecycle()
    val selectionState by viewModel.selectionState.collectAsStateWithLifecycle()
    
    var showEditDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var showAddPhotosDialog by remember { mutableStateOf(false) }
    
    LaunchedEffect(albumId) {
        viewModel.loadAlbum(albumId)
    }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        AlbumDetailTopBar(
            album = album,
            isSelectionMode = selectionState.isSelectionMode,
            selectedCount = selectionState.selectedPhotos.size,
            onNavigateBack = onNavigateBack,
            onEdit = { showEditDialog = true },
            onDelete = { showDeleteDialog = true },
            onAddPhotos = { showAddPhotosDialog = true },
            onSelectAll = { viewModel.selectAllPhotos() },
            onClearSelection = { viewModel.clearSelection() },
            onDeleteSelected = { viewModel.deleteSelectedPhotos() }
        )
        
        when {
            uiState.isLoading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            
            uiState.error != null -> {
                AlbumDetailError(
                    error = uiState.error!!,
                    onRetry = { viewModel.loadAlbum(albumId) },
                    onNavigateBack = onNavigateBack,
                    modifier = Modifier.fillMaxSize()
                )
            }
            
            album != null -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // 相册信息
                    item {
                        AlbumInfoSection(
                            album = album!!,
                            modifier = Modifier.padding(16.dp)
                        )
                    }
                    
                    // 照片网格
                    item {
                        if (photos.isNotEmpty()) {
                            // TODO: 实现PhotoGrid组件
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp)
                                    .padding(horizontal = 16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = "${photos.size} 张照片",
                                    style = MaterialTheme.typography.bodyLarge
                                )
                            }
                        } else {
                            EmptyAlbumState(
                                onAddPhotos = { showAddPhotosDialog = true },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(32.dp)
                            )
                        }
                    }
                }
            }
        }
    }
    
    // 编辑相册对话框
    if (showEditDialog && album != null) {
        EditAlbumDialog(
            album = album!!,
            onConfirm = { name, description, color ->
                viewModel.updateAlbum(name, description, color)
                showEditDialog = false
            },
            onDismiss = { showEditDialog = false }
        )
    }
    
    // 删除相册确认对话框
    if (showDeleteDialog) {
        DeleteAlbumDialog(
            onConfirm = {
                viewModel.deleteAlbum()
                showDeleteDialog = false
                onNavigateBack()
            },
            onDismiss = { showDeleteDialog = false }
        )
    }
    
    // 添加照片对话框
    if (showAddPhotosDialog) {
        AddPhotosDialog(
            onConfirm = { photoIds ->
                viewModel.addPhotosToAlbum(photoIds)
                showAddPhotosDialog = false
            },
            onDismiss = { showAddPhotosDialog = false }
        )
    }
}

/**
 * 相册详情顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AlbumDetailTopBar(
    album: Album?,
    isSelectionMode: Boolean,
    selectedCount: Int,
    onNavigateBack: () -> Unit,
    onEdit: () -> Unit,
    onDelete: () -> Unit,
    onAddPhotos: () -> Unit,
    onSelectAll: () -> Unit,
    onClearSelection: () -> Unit,
    onDeleteSelected: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            if (isSelectionMode) {
                Text("已选择 $selectedCount 张")
            } else {
                Text(
                    text = album?.name ?: "相册",
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        },
        navigationIcon = {
            IconButton(onClick = if (isSelectionMode) onClearSelection else onNavigateBack) {
                Icon(
                    imageVector = if (isSelectionMode) Icons.Filled.Close else Icons.Filled.ArrowBack,
                    contentDescription = if (isSelectionMode) "取消选择" else "返回"
                )
            }
        },
        actions = {
            if (isSelectionMode) {
                // 选择模式操作
                IconButton(onClick = onSelectAll) {
                    Icon(
                        imageVector = Icons.Filled.SelectAll,
                        contentDescription = "全选"
                    )
                }
                
                IconButton(onClick = onDeleteSelected) {
                    Icon(
                        imageVector = Icons.Filled.Delete,
                        contentDescription = "删除选中"
                    )
                }
            } else {
                // 普通模式操作
                IconButton(onClick = onAddPhotos) {
                    Icon(
                        imageVector = Icons.Filled.Add,
                        contentDescription = "添加照片"
                    )
                }
                
                if (album?.isSystemAlbum == false) {
                    IconButton(onClick = onEdit) {
                        Icon(
                            imageVector = Icons.Filled.Edit,
                            contentDescription = "编辑相册"
                        )
                    }
                    
                    IconButton(onClick = onDelete) {
                        Icon(
                            imageVector = Icons.Filled.Delete,
                            contentDescription = "删除相册"
                        )
                    }
                }
            }
        },
        modifier = modifier
    )
}

/**
 * 相册信息区域
 */
@Composable
fun AlbumInfoSection(
    album: Album,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 相册图标或封面
                Box(
                    modifier = Modifier
                        .size(60.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            album.color?.color ?: MaterialTheme.colorScheme.surfaceVariant
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    if (album.coverPhotoId != null) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data("") // TODO: 获取封面图片路径
                                .crossfade(true)
                                .build(),
                            contentDescription = album.name,
                            contentScale = ContentScale.Crop,
                            modifier = Modifier.fillMaxSize()
                        )
                    } else {
                        Icon(
                            imageVector = when (album.albumType) {
                                AlbumType.FAVORITES -> Icons.Filled.Favorite
                                AlbumType.YUCRAM_PHOTOS -> Icons.Filled.CameraAlt
                                AlbumType.SMART_ALBUM -> Icons.Filled.AutoAwesome
                                else -> Icons.Filled.PhotoLibrary
                            },
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // 相册信息
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = album.name,
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.weight(1f)
                        )
                        
                        if (album.isSystemAlbum) {
                            Icon(
                                imageVector = Icons.Filled.Star,
                                contentDescription = "系统相册",
                                modifier = Modifier.size(20.dp),
                                tint = Color.Yellow
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = "${album.photoCount} 张照片",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = "创建于 ${album.dateCreated.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 相册描述
            album.description?.let { description ->
                if (description.isNotBlank()) {
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 相册标签
            if (album.tags.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                LazyVerticalGrid(
                    columns = GridCells.Adaptive(minSize = 80.dp),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.height(40.dp)
                ) {
                    items(album.tags) { tag ->
                        AssistChip(
                            onClick = { },
                            label = {
                                Text(
                                    text = tag,
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                        )
                    }
                }
            }
        }
    }
}

/**
 * 空相册状态
 */
@Composable
fun EmptyAlbumState(
    onAddPhotos: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Filled.PhotoLibrary,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "相册为空",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "添加一些照片来开始吧",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Button(onClick = onAddPhotos) {
            Icon(
                imageVector = Icons.Filled.Add,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("添加照片")
        }
    }
}

/**
 * 编辑相册对话框
 */
@Composable
fun EditAlbumDialog(
    album: Album,
    onConfirm: (String, String, AlbumColor?) -> Unit,
    onDismiss: () -> Unit
) {
    var albumName by remember { mutableStateOf(album.name) }
    var albumDescription by remember { mutableStateOf(album.description ?: "") }
    var selectedColor by remember { mutableStateOf(album.color) }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("编辑相册")
        },
        text = {
            Column {
                OutlinedTextField(
                    value = albumName,
                    onValueChange = { albumName = it },
                    label = { Text("相册名称") },
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = albumDescription,
                    onValueChange = { albumDescription = it },
                    label = { Text("描述") },
                    maxLines = 3,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "相册颜色",
                    style = MaterialTheme.typography.labelMedium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // TODO: 添加颜色选择器
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    if (albumName.isNotBlank()) {
                        onConfirm(albumName.trim(), albumDescription.trim(), selectedColor)
                    }
                },
                enabled = albumName.isNotBlank()
            ) {
                Text("保存")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 删除相册对话框
 */
@Composable
fun DeleteAlbumDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("删除相册")
        },
        text = {
            Text("确定要删除这个相册吗？此操作无法撤销。")
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text("删除")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 添加照片对话框
 */
@Composable
fun AddPhotosDialog(
    onConfirm: (List<String>) -> Unit,
    onDismiss: () -> Unit
) {
    // TODO: 实现照片选择界面
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text("添加照片")
        },
        text = {
            Text("照片选择功能开发中...")
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 相册详情错误状态
 */
@Composable
fun AlbumDetailError(
    error: String,
    onRetry: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Filled.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "加载失败",
            style = MaterialTheme.typography.headlineSmall
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(onClick = onNavigateBack) {
                Text("返回")
            }
            
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}
