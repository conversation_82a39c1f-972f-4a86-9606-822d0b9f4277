package com.qxyu.yucram.domain.model;

/**
 * LUT滤镜分类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007j\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017\u00a8\u0006\u0018"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTCategory;", "", "displayName", "", "description", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getDisplayName", "CINEMATIC", "VINTAGE", "PORTRAIT", "LANDSCAPE", "STREET", "FASHION", "MOODY", "BRIGHT", "DARK", "WARM", "COOL", "MONOCHROME", "CREATIVE", "PROFESSIONAL", "USER_CUSTOM", "app_debug"})
public enum LUTCategory {
    /*public static final*/ CINEMATIC /* = new CINEMATIC(null, null) */,
    /*public static final*/ VINTAGE /* = new VINTAGE(null, null) */,
    /*public static final*/ PORTRAIT /* = new PORTRAIT(null, null) */,
    /*public static final*/ LANDSCAPE /* = new LANDSCAPE(null, null) */,
    /*public static final*/ STREET /* = new STREET(null, null) */,
    /*public static final*/ FASHION /* = new FASHION(null, null) */,
    /*public static final*/ MOODY /* = new MOODY(null, null) */,
    /*public static final*/ BRIGHT /* = new BRIGHT(null, null) */,
    /*public static final*/ DARK /* = new DARK(null, null) */,
    /*public static final*/ WARM /* = new WARM(null, null) */,
    /*public static final*/ COOL /* = new COOL(null, null) */,
    /*public static final*/ MONOCHROME /* = new MONOCHROME(null, null) */,
    /*public static final*/ CREATIVE /* = new CREATIVE(null, null) */,
    /*public static final*/ PROFESSIONAL /* = new PROFESSIONAL(null, null) */,
    /*public static final*/ USER_CUSTOM /* = new USER_CUSTOM(null, null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    
    LUTCategory(java.lang.String displayName, java.lang.String description) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.LUTCategory> getEntries() {
        return null;
    }
}