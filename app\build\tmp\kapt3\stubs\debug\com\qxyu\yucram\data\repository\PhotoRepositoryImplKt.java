package com.qxyu.yucram.data.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\f\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0002\u001a\f\u0010\u0003\u001a\u00020\u0002*\u00020\u0001H\u0002\u00a8\u0006\u0004"}, d2 = {"toDomainModel", "Lcom/qxyu/yucram/domain/model/Photo;", "Lcom/qxyu/yucram/data/local/entity/PhotoEntity;", "toEntity", "app_debug"})
public final class PhotoRepositoryImplKt {
    
    /**
     * PhotoEntity转换为Photo领域模型
     */
    private static final com.qxyu.yucram.domain.model.Photo toDomainModel(com.qxyu.yucram.data.local.entity.PhotoEntity $this$toDomainModel) {
        return null;
    }
    
    /**
     * Photo领域模型转换为PhotoEntity
     */
    private static final com.qxyu.yucram.data.local.entity.PhotoEntity toEntity(com.qxyu.yucram.domain.model.Photo $this$toEntity) {
        return null;
    }
}