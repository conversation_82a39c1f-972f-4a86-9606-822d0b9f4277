// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.album;

import com.qxyu.yucram.domain.repository.AlbumRepository;
import com.qxyu.yucram.domain.repository.PhotoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SmartAlbumViewModel_Factory implements Factory<SmartAlbumViewModel> {
  private final Provider<AlbumRepository> albumRepositoryProvider;

  private final Provider<PhotoRepository> photoRepositoryProvider;

  public SmartAlbumViewModel_Factory(Provider<AlbumRepository> albumRepositoryProvider,
      Provider<PhotoRepository> photoRepositoryProvider) {
    this.albumRepositoryProvider = albumRepositoryProvider;
    this.photoRepositoryProvider = photoRepositoryProvider;
  }

  @Override
  public SmartAlbumViewModel get() {
    return newInstance(albumRepositoryProvider.get(), photoRepositoryProvider.get());
  }

  public static SmartAlbumViewModel_Factory create(
      Provider<AlbumRepository> albumRepositoryProvider,
      Provider<PhotoRepository> photoRepositoryProvider) {
    return new SmartAlbumViewModel_Factory(albumRepositoryProvider, photoRepositoryProvider);
  }

  public static SmartAlbumViewModel newInstance(AlbumRepository albumRepository,
      PhotoRepository photoRepository) {
    return new SmartAlbumViewModel(albumRepository, photoRepository);
  }
}
