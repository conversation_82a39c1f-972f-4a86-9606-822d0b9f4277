package com.qxyu.yucram.filter;

/**
 * 设备信息数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0011\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\bH\u00c6\u0003J\u000f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00c6\u0003JA\u0010\u001b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u000e\b\u0002\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u00c6\u0001J\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00d6\u0001J\t\u0010 \u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006!"}, d2 = {"Lcom/qxyu/yucram/filter/DeviceInfo;", "", "brand", "", "model", "androidVersion", "", "logCapability", "Lcom/qxyu/yucram/domain/model/LogCapability;", "supportedFormats", "", "Lcom/qxyu/yucram/domain/model/ImageSourceFormat;", "(Ljava/lang/String;Ljava/lang/String;ILcom/qxyu/yucram/domain/model/LogCapability;Ljava/util/List;)V", "getAndroidVersion", "()I", "getBrand", "()Ljava/lang/String;", "getLogCapability", "()Lcom/qxyu/yucram/domain/model/LogCapability;", "getModel", "getSupportedFormats", "()Ljava/util/List;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class DeviceInfo {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String brand = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String model = null;
    private final int androidVersion = 0;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LogCapability logCapability = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.qxyu.yucram.domain.model.ImageSourceFormat> supportedFormats = null;
    
    public DeviceInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String brand, @org.jetbrains.annotations.NotNull()
    java.lang.String model, int androidVersion, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LogCapability logCapability, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.ImageSourceFormat> supportedFormats) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBrand() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getModel() {
        return null;
    }
    
    public final int getAndroidVersion() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LogCapability getLogCapability() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.ImageSourceFormat> getSupportedFormats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LogCapability component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.ImageSourceFormat> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.filter.DeviceInfo copy(@org.jetbrains.annotations.NotNull()
    java.lang.String brand, @org.jetbrains.annotations.NotNull()
    java.lang.String model, int androidVersion, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LogCapability logCapability, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.ImageSourceFormat> supportedFormats) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}