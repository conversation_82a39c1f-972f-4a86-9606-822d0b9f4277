package com.qxyu.yucram.presentation.borderwater.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\u001a6\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a.\u0010\n\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a.\u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a.\u0010\f\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a6\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0012\u0010\u0012\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006\u0013"}, d2 = {"BorderWatermarkEditPanel", "", "currentTab", "Lcom/qxyu/yucram/presentation/borderwater/BorderWatermarkTab;", "settings", "Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;", "onSettingsChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "CombinedEditPanel", "ExportSettingsSection", "QuickPresetSection", "QuickToggleCard", "title", "", "isEnabled", "", "onToggle", "app_debug"})
public final class BorderWatermarkEditPanelKt {
    
    /**
     * 边框和水印编辑面板组合
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkEditPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab currentTab, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderWatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 组合编辑面板
     */
    @androidx.compose.runtime.Composable()
    public static final void CombinedEditPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderWatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 快速切换卡片
     */
    @androidx.compose.runtime.Composable()
    public static final void QuickToggleCard(@org.jetbrains.annotations.NotNull()
    java.lang.String title, boolean isEnabled, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onToggle, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 快速预设区域
     */
    @androidx.compose.runtime.Composable()
    public static final void QuickPresetSection(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderWatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 导出设置区域
     */
    @androidx.compose.runtime.Composable()
    public static final void ExportSettingsSection(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderWatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}