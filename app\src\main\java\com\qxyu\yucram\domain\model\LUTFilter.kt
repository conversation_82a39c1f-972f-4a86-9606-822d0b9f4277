package com.qxyu.yucram.domain.model

import androidx.compose.ui.graphics.Color

/**
 * LUT滤镜数据模型
 */
data class LutFilter(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val category: LutCategory,
    val intensity: Float = 1.0f,                    // 滤镜强度 (0.0 to 1.0)
    val lutFilePath: String,                        // LUT文件路径
    val previewImagePath: String? = null,           // 预览图片路径
    val thumbnailPath: String? = null,              // 缩略图路径
    val isBuiltIn: Boolean = true,                  // 是否为内置滤镜
    val isPremium: Boolean = false,                 // 是否为付费滤镜
    val downloadUrl: String? = null,                // 下载链接（在线滤镜）
    val fileSize: Long = 0,                         // 文件大小（字节）
    val version: String = "1.0",                    // 滤镜版本
    val author: String? = null,                     // 作者信息
    val tags: List<String> = emptyList(),           // 标签
    val colorProfile: ColorProfile = ColorProfile.SRGB,
    val lutSize: LutSize = LutSize.SIZE_32,         // LUT表大小
    val lutFormat: LutFormat = LutFormat.CUBE,      // LUT文件格式
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * LUT滤镜分类
 */
enum class LutCategory(val displayName: String, val description: String) {
    CINEMATIC("电影级", "专业电影色彩分级效果"),
    VINTAGE("复古胶片", "经典胶片和复古风格"),
    PORTRAIT("人像美化", "专为人像摄影优化"),
    LANDSCAPE("风景增强", "自然风景色彩增强"),
    STREET("街拍风格", "都市街拍色调"),
    FASHION("时尚大片", "时尚摄影色彩"),
    MOODY("情绪色调", "情绪化色彩表达"),
    BRIGHT("明亮清新", "明亮清新色调"),
    DARK("暗黑风格", "低调暗黑色彩"),
    WARM("暖色调", "温暖色彩倾向"),
    COOL("冷色调", "冷静色彩倾向"),
    MONOCHROME("黑白单色", "黑白和单色效果"),
    CREATIVE("创意特效", "创意和艺术效果"),
    PROFESSIONAL("专业调色", "专业摄影师调色"),
    USER_CUSTOM("用户自定义", "用户导入的自定义LUT")
}

/**
 * LUT文件格式
 */
enum class LutFormat(val extension: String, val description: String) {
    CUBE(".cube", "Adobe Cube格式"),
    LUT_3D(".3dl", "3D LUT格式"),
    ICC(".icc", "ICC配置文件"),
    CSP(".csp", "Cinespace格式"),
    LOOK(".look", "ACES Look格式"),
    CDL(".cdl", "Color Decision List"),
    CUSTOM(".lut", "自定义LUT格式")
}

/**
 * LUT表大小
 */
enum class LutSize(val size: Int, val description: String) {
    SIZE_16(16, "16x16x16 - 快速处理"),
    SIZE_32(32, "32x32x32 - 标准质量"),
    SIZE_64(64, "64x64x64 - 高质量"),
    SIZE_128(128, "128x128x128 - 专业级")
}

/**
 * 颜色配置文件
 */
enum class ColorProfile(val displayName: String) {
    SRGB("sRGB"),
    ADOBE_RGB("Adobe RGB"),
    PROPHOTO_RGB("ProPhoto RGB"),
    REC709("Rec. 709"),
    REC2020("Rec. 2020"),
    DCI_P3("DCI-P3"),
    ACES("ACES"),
    LOG("Log")
}

/**
 * LUT滤镜设置
 */
data class LutFilterSettings(
    val selectedFilter: LutFilter? = null,
    val intensity: Float = 1.0f,                   // 滤镜强度
    val blendMode: LutBlendMode = LutBlendMode.NORMAL,
    val maskEnabled: Boolean = false,              // 是否启用遮罩
    val maskPath: String? = null,                  // 遮罩路径
    val maskInvert: Boolean = false,               // 遮罩反转
    val beforeAfterMode: Boolean = false,          // 前后对比模式
    val previewEnabled: Boolean = true,            // 预览开关
    val realTimePreview: Boolean = true,           // 实时预览
    val highQualityMode: Boolean = false           // 高质量模式
)

/**
 * LUT混合模式
 */
enum class LutBlendMode(val displayName: String) {
    NORMAL("正常"),
    MULTIPLY("正片叠底"),
    SCREEN("滤色"),
    OVERLAY("叠加"),
    SOFT_LIGHT("柔光"),
    HARD_LIGHT("强光"),
    COLOR_DODGE("颜色减淡"),
    COLOR_BURN("颜色加深"),
    DARKEN("变暗"),
    LIGHTEN("变亮"),
    DIFFERENCE("差值"),
    EXCLUSION("排除"),
    HUE("色相"),
    SATURATION("饱和度"),
    COLOR("颜色"),
    LUMINOSITY("明度")
}

/**
 * LUT滤镜包
 */
data class LutFilterPack(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val category: LutCategory,
    val filters: List<LutFilter>,
    val thumbnailPath: String? = null,
    val isPremium: Boolean = false,
    val price: Float = 0f,
    val currency: String = "CNY",
    val downloadUrl: String? = null,
    val totalSize: Long = 0,
    val version: String = "1.0",
    val author: String? = null,
    val rating: Float = 0f,
    val downloadCount: Int = 0,
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * LUT处理参数
 */
data class LutProcessingParams(
    val inputColorSpace: ColorProfile = ColorProfile.SRGB,
    val outputColorSpace: ColorProfile = ColorProfile.SRGB,
    val interpolationMethod: InterpolationMethod = InterpolationMethod.TRILINEAR,
    val ditherEnabled: Boolean = true,             // 抖动处理
    val gammaCorrection: Float = 1.0f,             // 伽马校正
    val exposureAdjustment: Float = 0f,            // 曝光调整
    val contrastAdjustment: Float = 0f,            // 对比度调整
    val saturationAdjustment: Float = 0f,          // 饱和度调整
    val temperatureAdjustment: Float = 0f,         // 色温调整
    val tintAdjustment: Float = 0f,                // 色调调整
    val highlightProtection: Boolean = false,      // 高光保护
    val shadowRecovery: Boolean = false            // 阴影恢复
)

/**
 * 插值方法
 */
enum class InterpolationMethod(val displayName: String, val description: String) {
    NEAREST("最近邻", "最快速度，质量较低"),
    LINEAR("线性", "平衡速度和质量"),
    TRILINEAR("三线性", "高质量，速度适中"),
    CUBIC("立方", "最高质量，速度较慢")
}

/**
 * LUT滤镜预设
 */
data class LutFilterPreset(
    val id: String,
    val name: String,
    val description: String,
    val filters: List<LutFilterLayer>,             // 多层LUT组合
    val thumbnailPath: String? = null,
    val isBuiltIn: Boolean = true,
    val category: LutCategory,
    val tags: List<String> = emptyList(),
    val createdAt: Long = System.currentTimeMillis()
)

/**
 * LUT滤镜层
 */
data class LutFilterLayer(
    val filter: LutFilter,
    val intensity: Float = 1.0f,
    val blendMode: LutBlendMode = LutBlendMode.NORMAL,
    val opacity: Float = 1.0f,
    val enabled: Boolean = true,
    val order: Int = 0                             // 层级顺序
)

/**
 * LUT分析结果
 */
data class LutAnalysisResult(
    val isValid: Boolean,
    val lutSize: LutSize,
    val format: LutFormat,
    val colorProfile: ColorProfile,
    val hasMetadata: Boolean,
    val metadata: Map<String, String> = emptyMap(),
    val colorRange: ColorRange,
    val dominantColors: List<Color> = emptyList(),
    val averageBrightness: Float = 0f,
    val averageContrast: Float = 0f,
    val averageSaturation: Float = 0f,
    val temperatureBias: Float = 0f,               // 色温倾向
    val tintBias: Float = 0f,                      // 色调倾向
    val errorMessage: String? = null
)

/**
 * 颜色范围
 */
data class ColorRange(
    val minRed: Float,
    val maxRed: Float,
    val minGreen: Float,
    val maxGreen: Float,
    val minBlue: Float,
    val maxBlue: Float,
    val minLuminance: Float,
    val maxLuminance: Float
)

/**
 * LUT滤镜历史记录
 */
data class LutFilterHistory(
    val id: String,
    val photoId: String,
    val filterId: String,
    val settings: LutFilterSettings,
    val processingParams: LutProcessingParams,
    val appliedAt: Long = System.currentTimeMillis(),
    val processingTime: Long = 0,                  // 处理耗时（毫秒）
    val resultQuality: Float = 1.0f,               // 结果质量评分
    val userRating: Int? = null                    // 用户评分 (1-5)
)

/**
 * LUT滤镜统计
 */
data class LutFilterStats(
    val filterId: String,
    val usageCount: Int = 0,
    val averageRating: Float = 0f,
    val totalRatings: Int = 0,
    val averageProcessingTime: Long = 0,
    val lastUsed: Long? = null,
    val popularityScore: Float = 0f,               // 流行度评分
    val effectivenessScore: Float = 0f             // 效果评分
)

/**
 * LUT滤镜搜索条件
 */
data class LutFilterSearchCriteria(
    val query: String? = null,
    val categories: List<LutCategory> = emptyList(),
    val tags: List<String> = emptyList(),
    val isPremium: Boolean? = null,
    val isBuiltIn: Boolean? = null,
    val minRating: Float? = null,
    val maxFileSize: Long? = null,
    val colorProfiles: List<ColorProfile> = emptyList(),
    val lutSizes: List<LutSize> = emptyList(),
    val sortBy: LutSortBy = LutSortBy.NAME,
    val sortOrder: SortOrder = SortOrder.ASC
)

/**
 * LUT排序方式
 */
enum class LutSortBy {
    NAME,           // 名称
    CATEGORY,       // 分类
    RATING,         // 评分
    USAGE_COUNT,    // 使用次数
    FILE_SIZE,      // 文件大小
    CREATED_AT,     // 创建时间
    UPDATED_AT,     // 更新时间
    POPULARITY      // 流行度
}

/**
 * 排序顺序
 */
enum class SortOrder {
    ASC,            // 升序
    DESC            // 降序
}
