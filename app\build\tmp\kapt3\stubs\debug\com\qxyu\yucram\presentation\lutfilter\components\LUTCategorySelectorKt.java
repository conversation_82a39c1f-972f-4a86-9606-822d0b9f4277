package com.qxyu.yucram.presentation.lutfilter.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000D\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0005\u001aB\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a\u001c\u0010\u000e\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a:\u0010\u000f\u001a\u00020\u00012\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00030\u00112\u0018\u0010\u0012\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00030\u0011\u0012\u0004\u0012\u00020\u00010\u00132\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a<\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00052\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\u00132\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a$\u0010\u0018\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0019\u001a\u00020\u001a2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a2\u0010\u001b\u001a\u00020\u00012\b\u0010\u001c\u001a\u0004\u0018\u00010\u00032\u0014\u0010\u001d\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0003\u0012\u0004\u0012\u00020\u00010\u00132\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a2\u0010\u001e\u001a\u00020\u00012\b\u0010\u001c\u001a\u0004\u0018\u00010\u00032\u0014\u0010\u001d\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0003\u0012\u0004\u0012\u00020\u00010\u00132\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u00a8\u0006\u001f"}, d2 = {"CategoryChip", "", "category", "Lcom/qxyu/yucram/domain/model/LUTCategory;", "name", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "isSelected", "", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "CategoryDescription", "CategoryFilter", "selectedCategories", "", "onCategoriesChanged", "Lkotlin/Function1;", "CategorySearch", "searchQuery", "onSearchQueryChanged", "onClearSearch", "CategoryStats", "filterCount", "", "LUTCategorySelector", "currentCategory", "onCategorySelected", "QuickCategorySwitch", "app_debug"})
public final class LUTCategorySelectorKt {
    
    /**
     * LUT滤镜分类选择器
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTCategorySelector(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTCategory currentCategory, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTCategory, kotlin.Unit> onCategorySelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 分类芯片
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void CategoryChip(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTCategory category, @org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.graphics.vector.ImageVector icon, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 分类统计信息
     */
    @androidx.compose.runtime.Composable()
    public static final void CategoryStats(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTCategory category, int filterCount, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 分类描述
     */
    @androidx.compose.runtime.Composable()
    public static final void CategoryDescription(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTCategory category, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 快速分类切换
     */
    @androidx.compose.runtime.Composable()
    public static final void QuickCategorySwitch(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTCategory currentCategory, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTCategory, kotlin.Unit> onCategorySelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 分类搜索
     */
    @androidx.compose.runtime.Composable()
    public static final void CategorySearch(@org.jetbrains.annotations.NotNull()
    java.lang.String searchQuery, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onSearchQueryChanged, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClearSearch, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 分类过滤器
     */
    @androidx.compose.runtime.Composable()
    public static final void CategoryFilter(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.LUTCategory> selectedCategories, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<? extends com.qxyu.yucram.domain.model.LUTCategory>, kotlin.Unit> onCategoriesChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}