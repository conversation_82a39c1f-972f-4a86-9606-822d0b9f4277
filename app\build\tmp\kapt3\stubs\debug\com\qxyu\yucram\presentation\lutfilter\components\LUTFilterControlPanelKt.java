package com.qxyu.yucram.presentation.lutfilter.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000P\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0002\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a0\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\n2\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00142\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a6\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u001e\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00182\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u001a0\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u000b\u001a\u00020\f2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a6\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u001f\u001a\u00020\u00032\u0012\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u0016\u0010!\u001a\b\u0012\u0004\u0012\u00020\u001d0\"2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u001a\u0018\u0010#\u001a\u00020\f2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001f\u001a\u00020\u0003H\u0002\u00a8\u0006$"}, d2 = {"AdvancedSettings", "", "settings", "Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "onSettingsChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "BlendModeChip", "mode", "Lcom/qxyu/yucram/domain/model/LUTBlendMode;", "isSelected", "", "onClick", "Lkotlin/Function0;", "BlendModeSelector", "selectedMode", "onModeSelected", "FilterIntensityControl", "intensity", "", "onIntensityChanged", "LUTFilterControlPanel", "filter", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "LUTFilterInfoDialog", "onDismiss", "QuickPresetChip", "preset", "Lcom/qxyu/yucram/presentation/lutfilter/components/LUTQuickPreset;", "QuickPresets", "currentSettings", "onPresetSelected", "getQuickPresets", "", "isPresetSelected", "app_debug"})
public final class LUTFilterControlPanelKt {
    
    /**
     * LUT滤镜控制面板
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTFilterControlPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTFilterSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 滤镜强度控制
     */
    @androidx.compose.runtime.Composable()
    public static final void FilterIntensityControl(float intensity, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onIntensityChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 混合模式选择器
     */
    @androidx.compose.runtime.Composable()
    public static final void BlendModeSelector(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTBlendMode selectedMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTBlendMode, kotlin.Unit> onModeSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 混合模式芯片
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void BlendModeChip(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTBlendMode mode, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 高级设置
     */
    @androidx.compose.runtime.Composable()
    public static final void AdvancedSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTFilterSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 快速预设
     */
    @androidx.compose.runtime.Composable()
    public static final void QuickPresets(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings currentSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTFilterSettings, kotlin.Unit> onPresetSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 快速预设芯片
     */
    @androidx.compose.runtime.Composable()
    public static final void QuickPresetChip(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.lutfilter.components.LUTQuickPreset preset, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 滤镜信息对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTFilterInfoDialog(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    /**
     * 获取快速预设
     */
    private static final java.util.List<com.qxyu.yucram.presentation.lutfilter.components.LUTQuickPreset> getQuickPresets(com.qxyu.yucram.domain.model.LUTFilter filter) {
        return null;
    }
    
    /**
     * 检查预设是否被选中
     */
    private static final boolean isPresetSelected(com.qxyu.yucram.presentation.lutfilter.components.LUTQuickPreset preset, com.qxyu.yucram.domain.model.LUTFilterSettings currentSettings) {
        return false;
    }
}