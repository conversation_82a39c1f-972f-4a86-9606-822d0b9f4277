package com.qxyu.yucram.presentation.album;

/**
 * 智能相册ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\t\n\u0002\u0010\b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0017\u001a\u00020\u0018J\"\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\n0\t2\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\f0\tH\u0082@\u00a2\u0006\u0002\u0010\u001aJ\u0006\u0010\u001b\u001a\u00020\u0018J\u0006\u0010\u001c\u001a\u00020\u0018J$\u0010\u001d\u001a\u00020\u00182\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001f2\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\f0\tJ\b\u0010!\u001a\u00020\u001fH\u0002J\b\u0010\"\u001a\u00020\u001fH\u0002J\u0018\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\u001fH\u0002J\u0018\u0010(\u001a\u00020$2\u0006\u0010)\u001a\u00020\n2\u0006\u0010*\u001a\u00020\fH\u0002J\u0010\u0010+\u001a\u00020&2\u0006\u0010,\u001a\u00020\u001fH\u0002J\u0006\u0010-\u001a\u00020\u0018J\u000e\u0010.\u001a\u00020\u00182\u0006\u0010/\u001a\u000200J\u0016\u00101\u001a\u00020\u00182\u0006\u0010/\u001a\u0002002\u0006\u0010*\u001a\u00020\fR\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u001d\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012\u00a8\u00062"}, d2 = {"Lcom/qxyu/yucram/presentation/album/SmartAlbumViewModel;", "Landroidx/lifecycle/ViewModel;", "albumRepository", "Lcom/qxyu/yucram/domain/repository/AlbumRepository;", "photoRepository", "Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "(Lcom/qxyu/yucram/domain/repository/AlbumRepository;Lcom/qxyu/yucram/domain/repository/PhotoRepository;)V", "_previewPhotos", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/qxyu/yucram/domain/model/Photo;", "_rules", "Lcom/qxyu/yucram/domain/model/SmartAlbumRule;", "_uiState", "Lcom/qxyu/yucram/presentation/album/SmartAlbumUiState;", "previewPhotos", "Lkotlinx/coroutines/flow/StateFlow;", "getPreviewPhotos", "()Lkotlinx/coroutines/flow/StateFlow;", "rules", "getRules", "uiState", "getUiState", "addRule", "", "applySmartAlbumRules", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearError", "clearMessage", "createSmartAlbum", "name", "", "description", "generateAlbumId", "generateRuleId", "matchesFileSizeRule", "", "fileSize", "", "ruleValue", "matchesRule", "photo", "rule", "parseSizeString", "sizeStr", "refreshPreview", "removeRule", "index", "", "updateRule", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SmartAlbumViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.AlbumRepository albumRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.PhotoRepository photoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.album.SmartAlbumUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.album.SmartAlbumUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule>> _rules = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule>> rules = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> _previewPhotos = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> previewPhotos = null;
    
    @javax.inject.Inject()
    public SmartAlbumViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.AlbumRepository albumRepository, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.PhotoRepository photoRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.album.SmartAlbumUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule>> getRules() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPreviewPhotos() {
        return null;
    }
    
    public final void addRule() {
    }
    
    public final void updateRule(int index, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SmartAlbumRule rule) {
    }
    
    public final void removeRule(int index) {
    }
    
    public final void refreshPreview() {
    }
    
    private final java.lang.Object applySmartAlbumRules(java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule> rules, kotlin.coroutines.Continuation<? super java.util.List<com.qxyu.yucram.domain.model.Photo>> $completion) {
        return null;
    }
    
    private final boolean matchesRule(com.qxyu.yucram.domain.model.Photo photo, com.qxyu.yucram.domain.model.SmartAlbumRule rule) {
        return false;
    }
    
    private final boolean matchesFileSizeRule(long fileSize, java.lang.String ruleValue) {
        return false;
    }
    
    private final long parseSizeString(java.lang.String sizeStr) {
        return 0L;
    }
    
    public final void createSmartAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule> rules) {
    }
    
    private final java.lang.String generateRuleId() {
        return null;
    }
    
    private final java.lang.String generateAlbumId() {
        return null;
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}