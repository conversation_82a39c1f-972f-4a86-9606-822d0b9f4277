package com.qxyu.yucram.presentation.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0003\u001a+\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\u0017\u0010\u0004\u001a\u0013\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u0005\u00a2\u0006\u0002\b\u0007H\u0007\u001a\b\u0010\b\u001a\u00020\u0006H\u0007\u001a\b\u0010\t\u001a\u00020\nH\u0007\u001a\b\u0010\u000b\u001a\u00020\fH\u0007\u001a\r\u0010\r\u001a\u00020\u000eH\u0007\u00a2\u0006\u0002\u0010\u000f\u001a/\u0010\u0010\u001a\u0002H\u0011\"\u0004\b\u0000\u0010\u00112\u0006\u0010\u0012\u001a\u0002H\u00112\b\b\u0002\u0010\u0013\u001a\u0002H\u00112\b\b\u0002\u0010\u0014\u001a\u0002H\u0011H\u0007\u00a2\u0006\u0002\u0010\u0015\u001a\b\u0010\u0016\u001a\u00020\u0017H\u0007\u001a\b\u0010\u0018\u001a\u00020\u0017H\u0007\u001a\b\u0010\u0019\u001a\u00020\u0017H\u0007\u00a8\u0006\u001a"}, d2 = {"ResponsiveLayout", "", "modifier", "Landroidx/compose/ui/Modifier;", "content", "Lkotlin/Function1;", "Lcom/qxyu/yucram/presentation/utils/ResponsiveLayoutInfo;", "Landroidx/compose/runtime/Composable;", "rememberResponsiveLayoutInfo", "responsiveFontScale", "", "responsiveGridColumns", "", "responsivePadding", "Landroidx/compose/ui/unit/Dp;", "()F", "responsiveValue", "T", "compact", "medium", "expanded", "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;", "shouldUseBottomNavigation", "", "shouldUseNavigationDrawer", "shouldUseNavigationRail", "app_debug"})
public final class ResponsiveLayoutKt {
    
    /**
     * 响应式布局组件
     */
    @androidx.compose.runtime.Composable()
    public static final void ResponsiveLayout(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.utils.ResponsiveLayoutInfo, kotlin.Unit> content) {
    }
    
    /**
     * 获取响应式布局信息的Composable函数
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.presentation.utils.ResponsiveLayoutInfo rememberResponsiveLayoutInfo() {
        return null;
    }
    
    /**
     * 响应式值选择器
     */
    @androidx.compose.runtime.Composable()
    public static final <T extends java.lang.Object>T responsiveValue(T compact, T medium, T expanded) {
        return null;
    }
    
    /**
     * 响应式Padding值
     */
    @androidx.compose.runtime.Composable()
    public static final float responsivePadding() {
        return 0.0F;
    }
    
    /**
     * 响应式网格列数
     */
    @androidx.compose.runtime.Composable()
    public static final int responsiveGridColumns() {
        return 0;
    }
    
    /**
     * 响应式字体缩放
     */
    @androidx.compose.runtime.Composable()
    public static final float responsiveFontScale() {
        return 0.0F;
    }
    
    /**
     * 判断是否应该使用导航抽屉
     */
    @androidx.compose.runtime.Composable()
    public static final boolean shouldUseNavigationDrawer() {
        return false;
    }
    
    /**
     * 判断是否应该使用导航栏
     */
    @androidx.compose.runtime.Composable()
    public static final boolean shouldUseNavigationRail() {
        return false;
    }
    
    /**
     * 判断是否应该使用底部导航
     */
    @androidx.compose.runtime.Composable()
    public static final boolean shouldUseBottomNavigation() {
        return false;
    }
}