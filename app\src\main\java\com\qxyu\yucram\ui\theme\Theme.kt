package com.qxyu.yucram.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    primary = PrimaryOrange,
    onPrimary = TextPrimary,
    primaryContainer = PrimaryOrangeLight,
    onPrimaryContainer = TextPrimary,
    secondary = SecondaryGray,
    onSecondary = TextPrimary,
    secondaryContainer = SecondaryLightGray,
    onSecondaryContainer = TextPrimary,
    tertiary = AccentBlue,
    onTertiary = TextPrimary,
    background = BackgroundDark,
    onBackground = TextPrimary,
    surface = SurfaceDark,
    onSurface = TextPrimary,
    surfaceVariant = SurfaceMedium,
    onSurfaceVariant = TextSecondary,
    error = AccentRed,
    onError = TextPrimary,
    outline = SecondaryLightGray,
    outlineVariant = SecondaryGray
)

private val LightColorScheme = lightColorScheme(
    primary = PrimaryOrange,
    onPrimary = TextPrimary,
    primaryContainer = PrimaryOrangeLight,
    onPrimaryContainer = BackgroundDark,
    secondary = SecondaryGray,
    onSecondary = TextPrimary,
    secondaryContainer = SecondaryLightGray,
    onSecondaryContainer = TextPrimary,
    tertiary = AccentBlue,
    onTertiary = TextPrimary,
    background = TextPrimary,
    onBackground = BackgroundDark,
    surface = TextPrimary,
    onSurface = BackgroundDark,
    surfaceVariant = SurfaceLight,
    onSurfaceVariant = SecondaryGray,
    error = AccentRed,
    onError = TextPrimary,
    outline = SecondaryLightGray,
    outlineVariant = SecondaryGray
)

@Composable
fun YucramTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            // 设置透明状态栏和导航栏
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT

            // 设置系统栏图标颜色
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = !darkTheme

            // 启用边到边显示
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
