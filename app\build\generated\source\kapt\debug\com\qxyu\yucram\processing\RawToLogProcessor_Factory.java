// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.processing;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RawToLogProcessor_Factory implements Factory<RawToLogProcessor> {
  private final Provider<Context> contextProvider;

  public RawToLogProcessor_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public RawToLogProcessor get() {
    return newInstance(contextProvider.get());
  }

  public static RawToLogProcessor_Factory create(Provider<Context> contextProvider) {
    return new RawToLogProcessor_Factory(contextProvider);
  }

  public static RawToLogProcessor newInstance(Context context) {
    return new RawToLogProcessor(context);
  }
}
