package com.qxyu.yucram.presentation.photoedit

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.PhotoRepository
import com.qxyu.yucram.presentation.photoedit.components.ToolInteraction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 照片编辑ViewModel
 */
@HiltViewModel
class PhotoEditViewModel @Inject constructor(
    private val photoRepository: PhotoRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(PhotoEditUiState())
    val uiState: StateFlow<PhotoEditUiState> = _uiState.asStateFlow()
    
    private val _photo = MutableStateFlow<Photo?>(null)
    val photo: StateFlow<Photo?> = _photo.asStateFlow()
    
    private val _editParams = MutableStateFlow(PhotoEditParams())
    val editParams: StateFlow<PhotoEditParams> = _editParams.asStateFlow()
    
    private val _currentTool = MutableStateFlow<EditTool?>(null)
    val currentTool: StateFlow<EditTool?> = _currentTool.asStateFlow()
    
    // ========== 编辑历史 ==========
    
    private val _editHistory = MutableStateFlow<List<EditHistory>>(emptyList())
    private val _historyIndex = MutableStateFlow(-1)
    
    private val originalParams = PhotoEditParams()
    
    // ========== 照片加载 ==========
    
    fun loadPhoto(photoId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                val photo = photoRepository.getPhotoById(photoId)
                if (photo != null) {
                    _photo.value = photo
                    
                    // 加载现有的编辑参数（如果有）
                    val existingParams = loadExistingEditParams(photo)
                    _editParams.value = existingParams
                    
                    _uiState.update { it.copy(isLoading = false) }
                } else {
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = "照片不存在"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载照片失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 工具选择 ==========
    
    fun selectTool(tool: EditTool) {
        _currentTool.value = tool
    }
    
    // ========== 参数更新 ==========
    
    fun updateEditParams(newParams: PhotoEditParams) {
        val oldParams = _editParams.value
        _editParams.value = newParams
        
        // 添加到历史记录
        addToHistory(oldParams, newParams, _currentTool.value ?: EditTool.EXPOSURE)
    }
    
    // ========== 工具交互 ==========
    
    fun handleToolInteraction(interaction: ToolInteraction) {
        when (interaction) {
            is ToolInteraction.CropChanged -> {
                val newParams = _editParams.value.copy(
                    cropRatio = interaction.cropRatio,
                    rotation = interaction.rotation
                )
                updateEditParams(newParams)
            }
            
            is ToolInteraction.LocalAdjustmentAdded -> {
                val newParams = _editParams.value.copy(
                    localAdjustments = _editParams.value.localAdjustments + interaction.adjustment
                )
                updateEditParams(newParams)
            }
            
            is ToolInteraction.LocalAdjustmentModified -> {
                val newAdjustments = _editParams.value.localAdjustments.map { adjustment ->
                    if (adjustment.id == interaction.id) {
                        interaction.adjustment
                    } else {
                        adjustment
                    }
                }
                val newParams = _editParams.value.copy(localAdjustments = newAdjustments)
                updateEditParams(newParams)
            }
            
            is ToolInteraction.LocalAdjustmentRemoved -> {
                val newAdjustments = _editParams.value.localAdjustments.filter { 
                    it.id != interaction.id 
                }
                val newParams = _editParams.value.copy(localAdjustments = newAdjustments)
                updateEditParams(newParams)
            }
        }
    }
    
    // ========== 历史记录管理 ==========
    
    private fun addToHistory(oldParams: PhotoEditParams, newParams: PhotoEditParams, tool: EditTool) {
        val history = EditHistory(
            id = generateHistoryId(),
            timestamp = System.currentTimeMillis(),
            tool = tool,
            beforeParams = oldParams,
            afterParams = newParams,
            description = generateHistoryDescription(tool, oldParams, newParams)
        )
        
        val currentHistory = _editHistory.value
        val currentIndex = _historyIndex.value
        
        // 移除当前索引之后的历史记录
        val newHistory = if (currentIndex >= 0) {
            currentHistory.take(currentIndex + 1) + history
        } else {
            listOf(history)
        }
        
        _editHistory.value = newHistory
        _historyIndex.value = newHistory.size - 1
    }
    
    fun undo() {
        val currentIndex = _historyIndex.value
        if (currentIndex > 0) {
            val history = _editHistory.value[currentIndex - 1]
            _editParams.value = history.beforeParams
            _historyIndex.value = currentIndex - 1
        } else if (currentIndex == 0) {
            _editParams.value = originalParams
            _historyIndex.value = -1
        }
    }
    
    fun redo() {
        val currentIndex = _historyIndex.value
        val history = _editHistory.value
        
        if (currentIndex < history.size - 1) {
            val nextHistory = history[currentIndex + 1]
            _editParams.value = nextHistory.afterParams
            _historyIndex.value = currentIndex + 1
        }
    }
    
    fun canUndo(): Boolean {
        return _historyIndex.value >= 0
    }
    
    fun canRedo(): Boolean {
        return _historyIndex.value < _editHistory.value.size - 1
    }
    
    // ========== 编辑操作 ==========
    
    fun resetEdit() {
        _editParams.value = originalParams
        _editHistory.value = emptyList()
        _historyIndex.value = -1
    }
    
    fun hasChanges(): Boolean {
        return _editParams.value != originalParams
    }
    
    fun saveEdit() {
        val currentPhoto = _photo.value ?: return
        val currentParams = _editParams.value
        
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isSaving = true) }
                
                // TODO: 实现实际的图像处理和保存
                // 这里应该调用图像处理引擎应用编辑参数
                val processedPhoto = applyEditParams(currentPhoto, currentParams)
                
                val result = photoRepository.updatePhoto(processedPhoto)
                
                result.fold(
                    onSuccess = {
                        _uiState.update { 
                            it.copy(
                                isSaving = false,
                                isSaved = true,
                                message = "保存成功"
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(
                                isSaving = false,
                                error = "保存失败: ${error.message}"
                            )
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isSaving = false,
                        error = "保存失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 预设管理 ==========
    
    fun applyPreset(preset: EditPreset) {
        updateEditParams(preset.params)
    }
    
    fun saveAsPreset(name: String, description: String): EditPreset {
        return EditPreset(
            id = generatePresetId(),
            name = name,
            description = description,
            params = _editParams.value,
            category = PresetCategory.USER
        )
    }
    
    // ========== 辅助方法 ==========
    
    private fun loadExistingEditParams(photo: Photo): PhotoEditParams {
        // TODO: 从照片的处理信息中加载现有的编辑参数
        return PhotoEditParams()
    }
    
    private suspend fun applyEditParams(photo: Photo, params: PhotoEditParams): Photo {
        // TODO: 实现实际的图像处理
        // 这里应该调用图像处理引擎应用编辑参数
        return photo.copy(
            processingInfo = photo.processingInfo?.copy(
                adjustments = PhotoAdjustments(
                    exposure = params.exposure,
                    highlights = params.highlights,
                    shadows = params.shadows,
                    contrast = params.contrast,
                    brightness = params.brightness,
                    saturation = params.saturation,
                    vibrance = params.vibrance,
                    temperature = params.temperature,
                    tint = params.tint,
                    sharpness = params.sharpness,
                    noise = params.noiseReduction,
                    vignette = params.vignette,
                    grain = params.grain
                )
            )
        )
    }
    
    private fun generateHistoryId(): String {
        return "history_${System.currentTimeMillis()}_${(0..999).random()}"
    }
    
    private fun generatePresetId(): String {
        return "preset_${System.currentTimeMillis()}_${(0..999).random()}"
    }
    
    private fun generateHistoryDescription(
        tool: EditTool, 
        oldParams: PhotoEditParams, 
        newParams: PhotoEditParams
    ): String {
        return when (tool) {
            EditTool.EXPOSURE -> "调整曝光"
            EditTool.HIGHLIGHTS_SHADOWS -> "调整高光阴影"
            EditTool.VIBRANCE_SATURATION -> "调整饱和度"
            EditTool.TEMPERATURE_TINT -> "调整色温色调"
            EditTool.HSL -> "调整HSL"
            EditTool.CURVES -> "调整曲线"
            EditTool.SHARPNESS -> "调整锐度"
            EditTool.VIGNETTE -> "调整暗角"
            EditTool.CROP_ROTATE -> "裁剪旋转"
            else -> "编辑调整"
        }
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
}

/**
 * 照片编辑UI状态
 */
data class PhotoEditUiState(
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isSaved: Boolean = false,
    val error: String? = null,
    val message: String? = null
)
