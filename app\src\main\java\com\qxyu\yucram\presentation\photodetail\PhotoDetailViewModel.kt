package com.qxyu.yucram.presentation.photodetail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.domain.model.Photo
import com.qxyu.yucram.domain.repository.PhotoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 照片详情页ViewModel
 */
@HiltViewModel
class PhotoDetailViewModel @Inject constructor(
    private val photoRepository: PhotoRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(PhotoDetailUiState())
    val uiState: StateFlow<PhotoDetailUiState> = _uiState.asStateFlow()
    
    private val _photo = MutableStateFlow<Photo?>(null)
    val photo: StateFlow<Photo?> = _photo.asStateFlow()
    
    // ========== 照片加载 ==========
    
    fun loadPhoto(photoId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                val photo = photoRepository.getPhotoById(photoId)
                if (photo != null) {
                    _photo.value = photo
                    _uiState.update { it.copy(isLoading = false) }
                } else {
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = "照片不存在"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载照片失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 照片操作 ==========
    
    fun toggleFavorite() {
        val currentPhoto = _photo.value ?: return
        
        viewModelScope.launch {
            try {
                val result = photoRepository.setFavorite(
                    currentPhoto.id, 
                    !currentPhoto.isFavorite
                )
                
                result.fold(
                    onSuccess = {
                        _photo.update { 
                            it?.copy(isFavorite = !currentPhoto.isFavorite)
                        }
                        
                        _uiState.update { 
                            it.copy(
                                message = if (!currentPhoto.isFavorite) "已添加到收藏" else "已取消收藏"
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(error = "操作失败: ${error.message}")
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "操作失败: ${e.message}")
                }
            }
        }
    }
    
    fun deletePhoto() {
        val currentPhoto = _photo.value ?: return
        
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }
                
                val result = photoRepository.deletePhoto(currentPhoto.id)
                
                result.fold(
                    onSuccess = {
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                message = "照片已删除",
                                isDeleted = true
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                error = "删除失败: ${error.message}"
                            )
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "删除失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    fun sharePhoto() {
        val currentPhoto = _photo.value ?: return
        
        viewModelScope.launch {
            try {
                // TODO: 实现分享功能
                _uiState.update { 
                    it.copy(message = "分享功能开发中")
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "分享失败: ${e.message}")
                }
            }
        }
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
}

/**
 * 照片详情UI状态
 */
data class PhotoDetailUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val isDeleted: Boolean = false
)
