package com.qxyu.yucram.data.repository

import android.graphics.Bitmap
import com.qxyu.yucram.data.crop.ImageTransformProcessor
import com.qxyu.yucram.data.crop.SmartCropAnalyzer
import com.qxyu.yucram.data.database.dao.CropHistoryDao
import com.qxyu.yucram.data.database.dao.CropPresetDao
import com.qxyu.yucram.data.database.entity.CropHistoryEntity
import com.qxyu.yucram.data.database.entity.CropPresetEntity
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.CropRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 裁剪和旋转Repository实现
 */
@Singleton
class CropRepositoryImpl @Inject constructor(
    private val imageTransformProcessor: ImageTransformProcessor,
    private val smartCropAnalyzer: SmartCropAnalyzer,
    private val cropHistoryDao: CropHistoryDao,
    private val cropPresetDao: CropPresetDao
) : CropRepository {
    
    // ========== 裁剪操作 ==========
    
    override suspend fun applyCrop(
        imagePath: String,
        cropSettings: CropSettings,
        outputPath: String,
        onProgress: (Float) -> Unit
    ): Result<String> {
        return imageTransformProcessor.applyCrop(imagePath, cropSettings, outputPath, onProgress)
    }
    
    override suspend fun generateCropPreview(
        imagePath: String,
        cropSettings: CropSettings,
        previewSize: Int
    ): Result<Bitmap> {
        return imageTransformProcessor.generatePreview(
            imagePath, cropSettings, RotationSettings(), null, previewSize
        )
    }
    
    override suspend fun applyCropBatch(
        imagePaths: List<String>,
        cropSettings: CropSettings,
        outputDir: String,
        onProgress: (Float) -> Unit
    ): Result<List<String>> {
        return try {
            val results = mutableListOf<String>()
            val totalImages = imagePaths.size
            
            imagePaths.forEachIndexed { index, imagePath ->
                val fileName = imagePath.substringAfterLast("/")
                val outputPath = "$outputDir/$fileName"
                
                val result = applyCrop(imagePath, cropSettings, outputPath) { progress ->
                    val overallProgress = (index + progress) / totalImages
                    onProgress(overallProgress)
                }
                
                if (result.isSuccess) {
                    results.add(result.getOrThrow())
                } else {
                    return@try Result.failure(result.exceptionOrNull() ?: Exception("Batch crop failed"))
                }
            }
            
            Result.success(results)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getSmartCropSuggestions(
        imagePath: String,
        targetAspectRatio: AspectRatio?,
        maxSuggestions: Int
    ): Result<List<SmartCropSuggestion>> {
        return smartCropAnalyzer.generateSmartCropSuggestions(
            imagePath, targetAspectRatio, maxSuggestions
        )
    }
    
    override suspend fun validateCropSettings(
        imagePath: String,
        cropSettings: CropSettings
    ): Result<Boolean> {
        return try {
            val imageInfo = getImageInfo(imagePath).getOrThrow()
            val cropRect = cropSettings.cropRect
            
            val isValid = cropRect.left >= 0f &&
                    cropRect.top >= 0f &&
                    cropRect.right <= 1f &&
                    cropRect.bottom <= 1f &&
                    cropRect.width() > 0f &&
                    cropRect.height() > 0f
            
            Result.success(isValid)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // ========== 旋转操作 ==========
    
    override suspend fun applyRotation(
        imagePath: String,
        rotationSettings: RotationSettings,
        outputPath: String,
        onProgress: (Float) -> Unit
    ): Result<String> {
        return imageTransformProcessor.applyRotation(imagePath, rotationSettings, outputPath, onProgress)
    }
    
    override suspend fun generateRotationPreview(
        imagePath: String,
        rotationSettings: RotationSettings,
        previewSize: Int
    ): Result<Bitmap> {
        return imageTransformProcessor.generatePreview(
            imagePath, CropSettings(), rotationSettings, null, previewSize
        )
    }
    
    override suspend fun detectAutoStraighten(
        imagePath: String,
        method: StraightenMethod
    ): Result<AutoStraightenResult> {
        return smartCropAnalyzer.detectAutoStraighten(imagePath, method)
    }
    
    override suspend fun applyPerspectiveCorrection(
        imagePath: String,
        perspectiveSettings: PerspectiveSettings,
        outputPath: String,
        onProgress: (Float) -> Unit
    ): Result<String> {
        return imageTransformProcessor.applyPerspectiveCorrection(
            imagePath, perspectiveSettings, outputPath, onProgress
        )
    }
    
    override suspend fun generatePerspectivePreview(
        imagePath: String,
        perspectiveSettings: PerspectiveSettings,
        previewSize: Int
    ): Result<Bitmap> {
        return imageTransformProcessor.generatePreview(
            imagePath, CropSettings(), RotationSettings(), perspectiveSettings, previewSize
        )
    }
    
    // ========== 组合操作 ==========
    
    override suspend fun applyCropAndRotation(
        imagePath: String,
        cropSettings: CropSettings,
        rotationSettings: RotationSettings,
        perspectiveSettings: PerspectiveSettings?,
        outputPath: String,
        onProgress: (Float) -> Unit
    ): Result<String> {
        return imageTransformProcessor.applyCombinedTransform(
            imagePath, cropSettings, rotationSettings, perspectiveSettings, outputPath, onProgress
        )
    }
    
    override suspend fun generateCombinedPreview(
        imagePath: String,
        cropSettings: CropSettings,
        rotationSettings: RotationSettings,
        perspectiveSettings: PerspectiveSettings?,
        previewSize: Int
    ): Result<Bitmap> {
        return imageTransformProcessor.generatePreview(
            imagePath, cropSettings, rotationSettings, perspectiveSettings, previewSize
        )
    }
    
    // ========== 预设管理 ==========
    
    override suspend fun getAllCropPresets(): Flow<List<CropPreset>> {
        return cropPresetDao.getAllPresets().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun getCropPresetsByCategory(
        category: CropPresetCategory
    ): Flow<List<CropPreset>> {
        return cropPresetDao.getPresetsByCategory(category.name).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun createCropPreset(preset: CropPreset): Result<String> {
        return try {
            val entity = CropPresetEntity.fromDomainModel(preset)
            cropPresetDao.insertPreset(entity)
            Result.success(preset.id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateCropPreset(preset: CropPreset): Result<Unit> {
        return try {
            val entity = CropPresetEntity.fromDomainModel(preset)
            cropPresetDao.updatePreset(entity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteCropPreset(presetId: String): Result<Unit> {
        return try {
            cropPresetDao.deletePreset(presetId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getAllRotationPresets(): Flow<List<RotationPreset>> {
        // 返回内置的旋转预设
        return kotlinx.coroutines.flow.flowOf(getBuiltInRotationPresets())
    }
    
    override suspend fun createRotationPreset(preset: RotationPreset): Result<String> {
        // 旋转预设通常是内置的，这里可以扩展为支持用户自定义
        return Result.success(preset.id)
    }
    
    override suspend fun updateRotationPreset(preset: RotationPreset): Result<Unit> {
        return Result.success(Unit)
    }
    
    override suspend fun deleteRotationPreset(presetId: String): Result<Unit> {
        return Result.success(Unit)
    }
    
    // ========== 历史记录 ==========
    
    override suspend fun getCropHistory(photoId: String): Flow<List<CropHistory>> {
        return cropHistoryDao.getHistoryByPhotoId(photoId).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun addCropHistory(history: CropHistory): Result<Unit> {
        return try {
            val entity = CropHistoryEntity.fromDomainModel(history)
            cropHistoryDao.insertHistory(entity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteCropHistory(historyId: String): Result<Unit> {
        return try {
            cropHistoryDao.deleteHistory(historyId)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun clearCropHistory(): Result<Unit> {
        return try {
            cropHistoryDao.clearAllHistory()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getRotationHistory(photoId: String): Flow<List<RotationHistory>> {
        // 简化实现，实际可以创建专门的RotationHistoryDao
        return kotlinx.coroutines.flow.flowOf(emptyList())
    }
    
    override suspend fun addRotationHistory(history: RotationHistory): Result<Unit> {
        return Result.success(Unit)
    }
    
    override suspend fun deleteRotationHistory(historyId: String): Result<Unit> {
        return Result.success(Unit)
    }
    
    override suspend fun clearRotationHistory(): Result<Unit> {
        return Result.success(Unit)
    }
    
    // ========== 工具方法 ==========
    
    override suspend fun getImageInfo(imagePath: String): Result<ImageInfo> {
        return imageTransformProcessor.getImageInfo(imagePath)
    }
    
    override suspend fun calculateOptimalCropArea(
        imagePath: String,
        targetAspectRatio: Float
    ): Result<CropRect> {
        return try {
            val imageInfo = getImageInfo(imagePath).getOrThrow()
            val currentAspectRatio = imageInfo.aspectRatio
            
            val cropRect = if (currentAspectRatio > targetAspectRatio) {
                // 图像太宽，需要裁剪宽度
                val newWidth = imageInfo.height * targetAspectRatio / imageInfo.width
                val left = (1f - newWidth) / 2f
                CropRect(left, 0f, left + newWidth, 1f)
            } else {
                // 图像太高，需要裁剪高度
                val newHeight = imageInfo.width / targetAspectRatio / imageInfo.height
                val top = (1f - newHeight) / 2f
                CropRect(0f, top, 1f, top + newHeight)
            }
            
            Result.success(cropRect)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun detectLines(
        imagePath: String,
        lineType: LineType
    ): Result<List<DetectedLine>> {
        return smartCropAnalyzer.detectLines(imagePath, lineType)
    }
    
    override suspend fun analyzeComposition(imagePath: String): Result<CompositionAnalysis> {
        return smartCropAnalyzer.analyzeComposition(imagePath)
    }
    
    override suspend fun generateGridLines(
        gridType: GridType,
        width: Float,
        height: Float
    ): List<GridLine> {
        return when (gridType) {
            GridType.RULE_OF_THIRDS -> generateRuleOfThirdsGrid(width, height)
            GridType.GOLDEN_RATIO -> generateGoldenRatioGrid(width, height)
            GridType.DIAGONAL -> generateDiagonalGrid(width, height)
            GridType.TRIANGLE -> generateTriangleGrid(width, height)
            GridType.GOLDEN_SPIRAL -> generateGoldenSpiralGrid(width, height)
            GridType.CENTER -> generateCenterGrid(width, height)
            GridType.NONE -> emptyList()
        }
    }
    
    // ========== 私有方法 ==========
    
    private fun getBuiltInRotationPresets(): List<RotationPreset> {
        return listOf(
            RotationPreset("rotate_90_cw", "顺时针90°", "向右旋转90度", 90f, category = RotationPresetCategory.BASIC),
            RotationPreset("rotate_90_ccw", "逆时针90°", "向左旋转90度", -90f, category = RotationPresetCategory.BASIC),
            RotationPreset("rotate_180", "旋转180°", "旋转180度", 180f, category = RotationPresetCategory.BASIC),
            RotationPreset("flip_horizontal", "水平翻转", "水平镜像翻转", 0f, flipHorizontal = true, category = RotationPresetCategory.BASIC),
            RotationPreset("flip_vertical", "垂直翻转", "垂直镜像翻转", 0f, flipVertical = true, category = RotationPresetCategory.BASIC)
        )
    }
    
    private fun generateRuleOfThirdsGrid(width: Float, height: Float): List<GridLine> {
        return listOf(
            // 垂直线
            GridLine(width / 3f, 0f, width / 3f, height, GridLineType.VERTICAL),
            GridLine(width * 2f / 3f, 0f, width * 2f / 3f, height, GridLineType.VERTICAL),
            // 水平线
            GridLine(0f, height / 3f, width, height / 3f, GridLineType.HORIZONTAL),
            GridLine(0f, height * 2f / 3f, width, height * 2f / 3f, GridLineType.HORIZONTAL)
        )
    }
    
    private fun generateGoldenRatioGrid(width: Float, height: Float): List<GridLine> {
        val goldenRatio = 1.618f
        return listOf(
            GridLine(width / goldenRatio, 0f, width / goldenRatio, height, GridLineType.VERTICAL),
            GridLine(width - width / goldenRatio, 0f, width - width / goldenRatio, height, GridLineType.VERTICAL),
            GridLine(0f, height / goldenRatio, width, height / goldenRatio, GridLineType.HORIZONTAL),
            GridLine(0f, height - height / goldenRatio, width, height - height / goldenRatio, GridLineType.HORIZONTAL)
        )
    }
    
    private fun generateDiagonalGrid(width: Float, height: Float): List<GridLine> {
        return listOf(
            GridLine(0f, 0f, width, height, GridLineType.DIAGONAL),
            GridLine(0f, height, width, 0f, GridLineType.DIAGONAL)
        )
    }
    
    private fun generateTriangleGrid(width: Float, height: Float): List<GridLine> {
        return listOf(
            GridLine(0f, height, width / 2f, 0f, GridLineType.DIAGONAL),
            GridLine(width / 2f, 0f, width, height, GridLineType.DIAGONAL),
            GridLine(0f, height, width, height, GridLineType.HORIZONTAL)
        )
    }
    
    private fun generateGoldenSpiralGrid(width: Float, height: Float): List<GridLine> {
        // 简化的黄金螺旋网格
        return generateGoldenRatioGrid(width, height)
    }
    
    private fun generateCenterGrid(width: Float, height: Float): List<GridLine> {
        return listOf(
            GridLine(width / 2f, 0f, width / 2f, height, GridLineType.VERTICAL),
            GridLine(0f, height / 2f, width, height / 2f, GridLineType.HORIZONTAL)
        )
    }
}
