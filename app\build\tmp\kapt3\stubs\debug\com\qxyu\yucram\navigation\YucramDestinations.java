package com.qxyu.yucram.navigation;

/**
 * Yucram应用导航目的地定义
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0010\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/qxyu/yucram/navigation/YucramDestinations;", "", "()V", "ABOUT_ROUTE", "", "ALBUM_ID_KEY", "ALBUM_ROUTE", "ALBUM_WITH_ID", "CAMERA_ROUTE", "CAMERA_SETTINGS_ROUTE", "FILTER_SELECTION_ROUTE", "GALLERY_ROUTE", "PERMISSION_ROUTE", "PHOTO_DETAIL_ROUTE", "PHOTO_DETAIL_WITH_ID", "PHOTO_EDIT_ROUTE", "PHOTO_EDIT_WITH_ID", "PHOTO_ID_KEY", "SETTINGS_ROUTE", "THEME_SETTINGS_ROUTE", "app_debug"})
public final class YucramDestinations {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CAMERA_ROUTE = "camera";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String GALLERY_ROUTE = "gallery";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SETTINGS_ROUTE = "settings";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PERMISSION_ROUTE = "permission";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CAMERA_SETTINGS_ROUTE = "camera_settings";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String FILTER_SELECTION_ROUTE = "filter_selection";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PHOTO_DETAIL_ROUTE = "photo_detail";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PHOTO_EDIT_ROUTE = "photo_edit";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ALBUM_ROUTE = "album";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String THEME_SETTINGS_ROUTE = "theme_settings";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ABOUT_ROUTE = "about";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PHOTO_DETAIL_WITH_ID = "photo_detail/{photoId}";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PHOTO_EDIT_WITH_ID = "photo_edit/{photoId}";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ALBUM_WITH_ID = "album/{albumId}";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String PHOTO_ID_KEY = "photoId";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ALBUM_ID_KEY = "albumId";
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.navigation.YucramDestinations INSTANCE = null;
    
    private YucramDestinations() {
        super();
    }
}