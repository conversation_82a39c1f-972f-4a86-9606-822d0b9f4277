package com.qxyu.yucram.presentation.gallery

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.PhotoRepository
import com.qxyu.yucram.domain.repository.AlbumRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 图库页面ViewModel
 */
@HiltViewModel
class GalleryViewModel @Inject constructor(
    private val photoRepository: PhotoRepository,
    private val albumRepository: AlbumRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(GalleryUiState())
    val uiState: StateFlow<GalleryUiState> = _uiState.asStateFlow()
    
    private val _selectionState = MutableStateFlow(PhotoSelectionState())
    val selectionState: StateFlow<PhotoSelectionState> = _selectionState.asStateFlow()
    
    // ========== 数据流 ==========
    
    val photos: StateFlow<List<Photo>> = photoRepository.getAllPhotos()
        .combine(_uiState) { photos, state ->
            when {
                state.searchQuery.isNotBlank() -> {
                    photos.filter { photo ->
                        photo.fileName.contains(state.searchQuery, ignoreCase = true) ||
                        photo.tags.any { it.contains(state.searchQuery, ignoreCase = true) }
                    }
                }
                state.currentFilter != null -> {
                    applyFilter(photos, state.currentFilter)
                }
                else -> photos
            }.let { filteredPhotos ->
                sortPhotos(filteredPhotos, state.sortOrder)
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    val albums: StateFlow<List<Album>> = albumRepository.getAllAlbums()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    // ========== 初始化 ==========
    
    init {
        loadInitialData()
    }
    
    private fun loadInitialData() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true) }
                
                // 扫描设备照片
                photoRepository.scanDevicePhotos()
                
                // 初始化系统相册
                albumRepository.initializeSystemAlbums()
                
                _uiState.update { it.copy(isLoading = false) }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载图库失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 搜索功能 ==========
    
    fun toggleSearchMode() {
        _uiState.update { 
            it.copy(
                isSearchMode = !it.isSearchMode,
                searchQuery = if (!it.isSearchMode) "" else it.searchQuery
            )
        }
    }
    
    fun updateSearchQuery(query: String) {
        _uiState.update { it.copy(searchQuery = query) }
    }
    
    fun clearSearch() {
        _uiState.update { 
            it.copy(
                searchQuery = "",
                isSearchMode = false
            )
        }
    }
    
    // ========== 排序功能 ==========
    
    fun updateSortOrder(sortOrder: PhotoSortOrder) {
        _uiState.update { it.copy(sortOrder = sortOrder) }
    }
    
    private fun sortPhotos(photos: List<Photo>, sortOrder: PhotoSortOrder): List<Photo> {
        return when (sortOrder) {
            PhotoSortOrder.DATE_TAKEN_DESC -> photos.sortedByDescending { it.dateTaken }
            PhotoSortOrder.DATE_TAKEN_ASC -> photos.sortedBy { it.dateTaken }
            PhotoSortOrder.DATE_ADDED_DESC -> photos.sortedByDescending { it.dateAdded }
            PhotoSortOrder.DATE_ADDED_ASC -> photos.sortedBy { it.dateAdded }
            PhotoSortOrder.NAME_ASC -> photos.sortedBy { it.fileName }
            PhotoSortOrder.NAME_DESC -> photos.sortedByDescending { it.fileName }
            PhotoSortOrder.SIZE_DESC -> photos.sortedByDescending { it.fileSize }
            PhotoSortOrder.SIZE_ASC -> photos.sortedBy { it.fileSize }
        }
    }
    
    // ========== 筛选功能 ==========
    
    fun updateFilter(filter: PhotoFilter?) {
        _uiState.update { it.copy(currentFilter = filter) }
    }
    
    private fun applyFilter(photos: List<Photo>, filter: PhotoFilter): List<Photo> {
        return photos.filter { photo ->
            // 日期范围筛选
            if (filter.dateRange != null) {
                val dateTaken = photo.dateTaken ?: photo.dateAdded
                if (dateTaken < filter.dateRange.startDate || dateTaken > filter.dateRange.endDate) {
                    return@filter false
                }
            }
            
            // 相机品牌筛选
            if (filter.cameraMake != null && photo.exifData?.cameraMake != filter.cameraMake) {
                return@filter false
            }
            
            // 滤镜筛选
            if (filter.filterUsed != null && photo.filterUsed != filter.filterUsed) {
                return@filter false
            }
            
            // Yucram照片筛选
            if (filter.isYucramPhoto != null && photo.isYucramPhoto != filter.isYucramPhoto) {
                return@filter false
            }
            
            // 收藏筛选
            if (filter.isFavorite != null && photo.isFavorite != filter.isFavorite) {
                return@filter false
            }
            
            // 标签筛选
            if (filter.tags.isNotEmpty() && !photo.tags.any { it in filter.tags }) {
                return@filter false
            }
            
            true
        }
    }
    
    // ========== 选择功能 ==========
    
    fun togglePhotoSelection(photoId: String) {
        _selectionState.update { it.toggleSelection(photoId) }
    }
    
    fun enterSelectionMode(photoId: String) {
        _selectionState.update { it.enterSelectionMode(photoId) }
    }
    
    fun selectAllPhotos() {
        val currentPhotos = photos.value
        _selectionState.update { 
            it.selectAll(currentPhotos.map { photo -> photo.id })
        }
    }
    
    fun clearSelection() {
        _selectionState.update { it.clearSelection() }
    }
    
    // ========== 照片操作 ==========
    
    fun deleteSelectedPhotos() {
        viewModelScope.launch {
            try {
                val selectedIds = _selectionState.value.selectedPhotos.toList()
                photoRepository.deletePhotos(selectedIds)
                clearSelection()
                
                _uiState.update { 
                    it.copy(message = "已删除 ${selectedIds.size} 张照片")
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "删除照片失败: ${e.message}")
                }
            }
        }
    }
    
    fun toggleFavorite(photoId: String) {
        viewModelScope.launch {
            try {
                val photo = photoRepository.getPhotoById(photoId)
                if (photo != null) {
                    photoRepository.setFavorite(photoId, !photo.isFavorite)
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "操作失败: ${e.message}")
                }
            }
        }
    }
    
    // ========== 显示模式 ==========
    
    fun updateDisplayMode(displayMode: PhotoDisplayMode) {
        _uiState.update { it.copy(displayMode = displayMode) }
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
}

/**
 * 图库UI状态
 */
data class GalleryUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val isSearchMode: Boolean = false,
    val searchQuery: String = "",
    val sortOrder: PhotoSortOrder = PhotoSortOrder.DATE_TAKEN_DESC,
    val displayMode: PhotoDisplayMode = PhotoDisplayMode.GRID_MEDIUM,
    val currentFilter: PhotoFilter? = null,
    val showAlbums: Boolean = false
)
