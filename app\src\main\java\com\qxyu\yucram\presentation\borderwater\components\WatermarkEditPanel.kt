package com.qxyu.yucram.presentation.borderwater.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qxyu.yucram.domain.model.*

/**
 * 水印编辑面板
 */
@Composable
fun WatermarkEditPanel(
    watermarkSettings: WatermarkSettings,
    onSettingsChanged: (WatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 水印开关
        item {
            WatermarkToggleSection(
                isEnabled = watermarkSettings.isEnabled,
                onToggle = { enabled ->
                    onSettingsChanged(watermarkSettings.copy(isEnabled = enabled))
                }
            )
        }
        
        if (watermarkSettings.isEnabled) {
            // 水印类型选择
            item {
                WatermarkTypeSection(
                    selectedType = watermarkSettings.watermarkType,
                    onTypeSelected = { type ->
                        onSettingsChanged(watermarkSettings.copy(watermarkType = type))
                    }
                )
            }
            
            // 水印内容设置
            item {
                WatermarkContentSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            // 位置设置
            item {
                WatermarkPositionSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            // 样式设置
            item {
                WatermarkStyleSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            // 高级设置
            item {
                WatermarkAdvancedSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
        }
    }
}

/**
 * 水印开关区域
 */
@Composable
fun WatermarkToggleSection(
    isEnabled: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = "水印",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            Text(
                text = if (isEnabled) "已启用" else "已禁用",
                style = MaterialTheme.typography.bodySmall,
                color = if (isEnabled) MaterialTheme.colorScheme.primary else Color.White.copy(alpha = 0.7f)
            )
        }
        
        Switch(
            checked = isEnabled,
            onCheckedChange = onToggle,
            colors = SwitchDefaults.colors(
                checkedThumbColor = MaterialTheme.colorScheme.primary,
                checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
            )
        )
    }
}

/**
 * 水印类型选择
 */
@Composable
fun WatermarkTypeSection(
    selectedType: WatermarkType,
    onTypeSelected: (WatermarkType) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "水印类型",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(WatermarkType.values()) { type ->
                WatermarkTypeItem(
                    type = type,
                    isSelected = selectedType == type,
                    onClick = { onTypeSelected(type) }
                )
            }
        }
    }
}

/**
 * 水印类型项
 */
@Composable
fun WatermarkTypeItem(
    type: WatermarkType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(
                    if (isSelected) MaterialTheme.colorScheme.primary
                    else Color.White.copy(alpha = 0.1f)
                )
                .border(
                    width = if (isSelected) 2.dp else 1.dp,
                    color = if (isSelected) MaterialTheme.colorScheme.primary
                    else Color.White.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(8.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = when (type) {
                    WatermarkType.TEXT -> Icons.Filled.TextFields
                    WatermarkType.IMAGE -> Icons.Filled.Image
                    WatermarkType.LOGO -> Icons.Filled.Business
                    WatermarkType.SIGNATURE -> Icons.Filled.Draw
                    WatermarkType.TIMESTAMP -> Icons.Filled.Schedule
                    WatermarkType.LOCATION -> Icons.Filled.LocationOn
                    WatermarkType.CAMERA_INFO -> Icons.Filled.CameraAlt
                    WatermarkType.CUSTOM -> Icons.Filled.Edit
                },
                contentDescription = null,
                tint = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = when (type) {
                WatermarkType.TEXT -> "文字"
                WatermarkType.IMAGE -> "图片"
                WatermarkType.LOGO -> "Logo"
                WatermarkType.SIGNATURE -> "签名"
                WatermarkType.TIMESTAMP -> "时间"
                WatermarkType.LOCATION -> "位置"
                WatermarkType.CAMERA_INFO -> "相机"
                WatermarkType.CUSTOM -> "自定义"
            },
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * 水印内容设置
 */
@Composable
fun WatermarkContentSettings(
    watermarkSettings: WatermarkSettings,
    onSettingsChanged: (WatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "内容设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        when (watermarkSettings.watermarkType) {
            WatermarkType.TEXT -> {
                WatermarkTextSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            WatermarkType.LOGO -> {
                WatermarkLogoSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            WatermarkType.TIMESTAMP -> {
                WatermarkTimestampSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            WatermarkType.CAMERA_INFO -> {
                WatermarkCameraInfoSettings(
                    watermarkSettings = watermarkSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            else -> {
                Text(
                    text = "该水印类型的设置开发中...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 文字水印设置
 */
@Composable
fun WatermarkTextSettings(
    watermarkSettings: WatermarkSettings,
    onSettingsChanged: (WatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 文字输入
        OutlinedTextField(
            value = watermarkSettings.text,
            onValueChange = { text ->
                onSettingsChanged(watermarkSettings.copy(text = text))
            },
            label = { Text("水印文字") },
            placeholder = { Text("输入水印文字") },
            colors = OutlinedTextFieldDefaults.colors(
                focusedTextColor = Color.White,
                unfocusedTextColor = Color.White,
                focusedLabelColor = MaterialTheme.colorScheme.primary,
                unfocusedLabelColor = Color.White.copy(alpha = 0.7f)
            ),
            modifier = Modifier.fillMaxWidth()
        )
        
        // 字体大小
        WatermarkSliderSetting(
            label = "字体大小",
            value = watermarkSettings.fontSize.value,
            valueRange = 8f..48f,
            onValueChanged = { size ->
                onSettingsChanged(watermarkSettings.copy(fontSize = size.sp))
            },
            formatValue = { "${it.toInt()}sp" }
        )
        
        // 字体粗细
        WatermarkFontWeightSelector(
            selectedWeight = watermarkSettings.fontWeight,
            onWeightSelected = { weight ->
                onSettingsChanged(watermarkSettings.copy(fontWeight = weight))
            }
        )
    }
}

/**
 * Logo水印设置
 */
@Composable
fun WatermarkLogoSettings(
    watermarkSettings: WatermarkSettings,
    onSettingsChanged: (WatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Logo类型选择
        Text(
            text = "Logo类型",
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(LogoType.values()) { logoType ->
                @OptIn(ExperimentalMaterial3Api::class)
                FilterChip(
                    onClick = {
                        onSettingsChanged(watermarkSettings.copy(logoType = logoType))
                    },
                    label = {
                        Text(
                            text = when (logoType) {
                                LogoType.YUCRAM -> "Yucram"
                                LogoType.CAMERA -> "相机"
                                LogoType.LENS -> "镜头"
                                LogoType.APERTURE -> "光圈"
                                LogoType.CUSTOM -> "自定义"
                            },
                            style = MaterialTheme.typography.bodySmall
                        )
                    },
                    selected = watermarkSettings.logoType == logoType
                )
            }
        }
        
        // Logo大小
        WatermarkSliderSetting(
            label = "Logo大小",
            value = watermarkSettings.logoSize,
            valueRange = 20f..200f,
            onValueChanged = { size ->
                onSettingsChanged(watermarkSettings.copy(logoSize = size))
            },
            formatValue = { "${it.toInt()}dp" }
        )
    }
}

/**
 * 水印位置设置
 */
@Composable
fun WatermarkPositionSettings(
    watermarkSettings: WatermarkSettings,
    onSettingsChanged: (WatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "位置设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        // 位置选择网格
        WatermarkPositionGrid(
            selectedPosition = watermarkSettings.position,
            onPositionSelected = { position ->
                onSettingsChanged(watermarkSettings.copy(position = position))
            }
        )
        
        // 边距设置
        WatermarkSliderSetting(
            label = "边距",
            value = watermarkSettings.margin,
            valueRange = 0f..50f,
            onValueChanged = { margin ->
                onSettingsChanged(watermarkSettings.copy(margin = margin))
            },
            formatValue = { "${it.toInt()}dp" }
        )
    }
}

/**
 * 水印位置网格
 */
@Composable
fun WatermarkPositionGrid(
    selectedPosition: WatermarkPosition,
    onPositionSelected: (WatermarkPosition) -> Unit,
    modifier: Modifier = Modifier
) {
    val positions = listOf(
        listOf(WatermarkPosition.TOP_LEFT, WatermarkPosition.TOP_CENTER, WatermarkPosition.TOP_RIGHT),
        listOf(WatermarkPosition.CENTER_LEFT, WatermarkPosition.CENTER, WatermarkPosition.CENTER_RIGHT),
        listOf(WatermarkPosition.BOTTOM_LEFT, WatermarkPosition.BOTTOM_CENTER, WatermarkPosition.BOTTOM_RIGHT)
    )
    
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        positions.forEach { row ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                row.forEach { position ->
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .background(
                                if (selectedPosition == position) MaterialTheme.colorScheme.primary
                                else Color.White.copy(alpha = 0.1f)
                            )
                            .border(
                                width = 1.dp,
                                color = Color.White.copy(alpha = 0.3f),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .clickable { onPositionSelected(position) },
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .size(8.dp)
                                .background(
                                    if (selectedPosition == position) Color.White
                                    else Color.White.copy(alpha = 0.7f),
                                    CircleShape
                                )
                        )
                    }
                }
            }
        }
    }
}

/**
 * 水印样式设置
 */
@Composable
fun WatermarkStyleSettings(
    watermarkSettings: WatermarkSettings,
    onSettingsChanged: (WatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "样式设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        // 颜色选择
        WatermarkColorPicker(
            selectedColor = when (watermarkSettings.watermarkType) {
                WatermarkType.TEXT -> watermarkSettings.textColor
                WatermarkType.LOGO -> watermarkSettings.logoColor
                else -> Color.White
            },
            onColorSelected = { color ->
                when (watermarkSettings.watermarkType) {
                    WatermarkType.TEXT -> {
                        onSettingsChanged(watermarkSettings.copy(textColor = color))
                    }
                    WatermarkType.LOGO -> {
                        onSettingsChanged(watermarkSettings.copy(logoColor = color))
                    }
                    else -> {}
                }
            }
        )
        
        // 不透明度
        WatermarkSliderSetting(
            label = "不透明度",
            value = watermarkSettings.opacity,
            valueRange = 0f..1f,
            onValueChanged = { opacity ->
                onSettingsChanged(watermarkSettings.copy(opacity = opacity))
            },
            formatValue = { "${(it * 100).toInt()}%" }
        )
    }
}

/**
 * 水印高级设置
 */
@Composable
fun WatermarkAdvancedSettings(
    watermarkSettings: WatermarkSettings,
    onSettingsChanged: (WatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "高级设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        // 旋转角度
        WatermarkSliderSetting(
            label = "旋转角度",
            value = watermarkSettings.rotation,
            valueRange = -45f..45f,
            onValueChanged = { rotation ->
                onSettingsChanged(watermarkSettings.copy(rotation = rotation))
            },
            formatValue = { "${it.toInt()}°" }
        )
        
        // 缩放比例
        WatermarkSliderSetting(
            label = "缩放比例",
            value = watermarkSettings.scale,
            valueRange = 0.5f..2f,
            onValueChanged = { scale ->
                onSettingsChanged(watermarkSettings.copy(scale = scale))
            },
            formatValue = { "${(it * 100).toInt()}%" }
        )
        
        // 文字阴影开关（仅文字水印）
        if (watermarkSettings.watermarkType == WatermarkType.TEXT) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "文字阴影",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White
                )
                
                Switch(
                    checked = watermarkSettings.textShadowEnabled,
                    onCheckedChange = { enabled ->
                        onSettingsChanged(watermarkSettings.copy(textShadowEnabled = enabled))
                    }
                )
            }
        }
    }
}

/**
 * 水印滑块设置
 */
@Composable
fun WatermarkSliderSetting(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChanged: (Float) -> Unit,
    formatValue: (Float) -> String = { it.toString() },
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            Text(
                text = formatValue(value),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Slider(
            value = value,
            onValueChange = onValueChanged,
            valueRange = valueRange,
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary,
                activeTrackColor = MaterialTheme.colorScheme.primary,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            )
        )
    }
}

/**
 * 水印颜色选择器
 */
@Composable
fun WatermarkColorPicker(
    selectedColor: Color,
    onColorSelected: (Color) -> Unit,
    modifier: Modifier = Modifier
) {
    val colors = listOf(
        Color.White, Color.Black, Color.Red, Color.Green, Color.Blue,
        Color.Yellow, Color.Cyan, Color.Magenta, Color.Gray,
        Color(0xFFFF9800), Color(0xFF9C27B0), Color(0xFF607D8B)
    )
    
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(colors) { color ->
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(color)
                    .border(
                        width = if (selectedColor == color) 3.dp else 1.dp,
                        color = if (selectedColor == color) MaterialTheme.colorScheme.primary
                        else Color.White.copy(alpha = 0.3f),
                        shape = CircleShape
                    )
                    .clickable { onColorSelected(color) }
            )
        }
    }
}

/**
 * 字体粗细选择器
 */
@Composable
fun WatermarkFontWeightSelector(
    selectedWeight: FontWeight,
    onWeightSelected: (FontWeight) -> Unit,
    modifier: Modifier = Modifier
) {
    val weights = listOf(
        FontWeight.Light to "细",
        FontWeight.Normal to "正常",
        FontWeight.Medium to "中等",
        FontWeight.SemiBold to "半粗",
        FontWeight.Bold to "粗体"
    )
    
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(weights) { (weight, name) ->
            @OptIn(ExperimentalMaterial3Api::class)
            FilterChip(
                onClick = { onWeightSelected(weight) },
                label = {
                    Text(
                        text = name,
                        style = MaterialTheme.typography.bodySmall,
                        fontWeight = weight
                    )
                },
                selected = selectedWeight == weight
            )
        }
    }
}

// TODO: 实现其他设置组件
@Composable
fun WatermarkTimestampSettings(watermarkSettings: WatermarkSettings, onSettingsChanged: (WatermarkSettings) -> Unit) {}

@Composable
fun WatermarkCameraInfoSettings(watermarkSettings: WatermarkSettings, onSettingsChanged: (WatermarkSettings) -> Unit) {}
