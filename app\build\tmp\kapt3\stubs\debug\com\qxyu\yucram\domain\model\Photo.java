package com.qxyu.yucram.domain.model;

/**
 * 照片数据模型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b8\b\u0087\b\u0018\u00002\u00020\u0001B\u00dd\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\u000b\u0012\u0006\u0010\r\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u0012\u0006\u0010\u0010\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u000b\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0017\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u001a\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0017\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0017\u0012\u000e\b\u0002\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00030\u001e\u0012\u000e\b\u0002\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00030\u001e\u00a2\u0006\u0002\u0010 J\t\u0010<\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010=\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\t\u0010>\u001a\u00020\u000eH\u00c6\u0003J\t\u0010?\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010@\u001a\u0004\u0018\u00010\u0013H\u00c6\u0003J\u000b\u0010A\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003J\t\u0010B\u001a\u00020\u0017H\u00c6\u0003J\u000b\u0010C\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010D\u001a\u0004\u0018\u00010\u001aH\u00c6\u0003J\t\u0010E\u001a\u00020\u0017H\u00c6\u0003J\t\u0010F\u001a\u00020\u0017H\u00c6\u0003J\t\u0010G\u001a\u00020\u0003H\u00c6\u0003J\u000f\u0010H\u001a\b\u0012\u0004\u0012\u00020\u00030\u001eH\u00c6\u0003J\u000f\u0010I\u001a\b\u0012\u0004\u0012\u00020\u00030\u001eH\u00c6\u0003J\t\u0010J\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010K\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010L\u001a\u00020\u0003H\u00c6\u0003J\t\u0010M\u001a\u00020\tH\u00c6\u0003J\t\u0010N\u001a\u00020\u000bH\u00c6\u0003J\t\u0010O\u001a\u00020\u000bH\u00c6\u0003J\t\u0010P\u001a\u00020\u000eH\u00c6\u0003J\u00f3\u0001\u0010Q\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u0010\u001a\u00020\u000e2\b\b\u0002\u0010\u0011\u001a\u00020\u000b2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00132\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00172\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u001a2\b\b\u0002\u0010\u001b\u001a\u00020\u00172\b\b\u0002\u0010\u001c\u001a\u00020\u00172\u000e\b\u0002\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00030\u001e2\u000e\b\u0002\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00030\u001eH\u00c6\u0001J\u0013\u0010R\u001a\u00020\u00172\b\u0010S\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010T\u001a\u00020\u000bH\u00d6\u0001J\t\u0010U\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00030\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0011\u0010\u0010\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010$R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010$R\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010*R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010-R\u0013\u0010\u0018\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010*R\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010*R\u0011\u0010\u001c\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u00102R\u0011\u0010\u001b\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u00102R\u0011\u0010\u0016\u001a\u00020\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u00102R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u00104R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010*R\u0011\u0010\u0011\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00100R\u0013\u0010\u0019\u001a\u0004\u0018\u00010\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u00108R\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00030\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010\"R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010*R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00100\u00a8\u0006V"}, d2 = {"Lcom/qxyu/yucram/domain/model/Photo;", "", "id", "", "fileName", "filePath", "thumbnailPath", "mimeType", "fileSize", "", "width", "", "height", "dateAdded", "Ljava/time/LocalDateTime;", "dateTaken", "dateModified", "orientation", "location", "Lcom/qxyu/yucram/domain/model/PhotoLocation;", "exifData", "Lcom/qxyu/yucram/domain/model/ExifData;", "isYucramPhoto", "", "filterUsed", "processingInfo", "Lcom/qxyu/yucram/domain/model/ProcessingInfo;", "isFavorite", "isDeleted", "albumIds", "", "tags", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;JIILjava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;ILcom/qxyu/yucram/domain/model/PhotoLocation;Lcom/qxyu/yucram/domain/model/ExifData;ZLjava/lang/String;Lcom/qxyu/yucram/domain/model/ProcessingInfo;ZZLjava/util/List;Ljava/util/List;)V", "getAlbumIds", "()Ljava/util/List;", "getDateAdded", "()Ljava/time/LocalDateTime;", "getDateModified", "getDateTaken", "getExifData", "()Lcom/qxyu/yucram/domain/model/ExifData;", "getFileName", "()Ljava/lang/String;", "getFilePath", "getFileSize", "()J", "getFilterUsed", "getHeight", "()I", "getId", "()Z", "getLocation", "()Lcom/qxyu/yucram/domain/model/PhotoLocation;", "getMimeType", "getOrientation", "getProcessingInfo", "()Lcom/qxyu/yucram/domain/model/ProcessingInfo;", "getTags", "getThumbnailPath", "getWidth", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "photos")
public final class Photo {
    @androidx.room.PrimaryKey()
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String fileName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String filePath = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String thumbnailPath = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String mimeType = null;
    private final long fileSize = 0L;
    private final int width = 0;
    private final int height = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime dateAdded = null;
    @org.jetbrains.annotations.Nullable()
    private final java.time.LocalDateTime dateTaken = null;
    @org.jetbrains.annotations.NotNull()
    private final java.time.LocalDateTime dateModified = null;
    private final int orientation = 0;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.PhotoLocation location = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.ExifData exifData = null;
    private final boolean isYucramPhoto = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String filterUsed = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.ProcessingInfo processingInfo = null;
    private final boolean isFavorite = false;
    private final boolean isDeleted = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> albumIds = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> tags = null;
    
    public Photo(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    java.lang.String filePath, @org.jetbrains.annotations.Nullable()
    java.lang.String thumbnailPath, @org.jetbrains.annotations.NotNull()
    java.lang.String mimeType, long fileSize, int width, int height, @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime dateAdded, @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime dateTaken, @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime dateModified, int orientation, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoLocation location, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ExifData exifData, boolean isYucramPhoto, @org.jetbrains.annotations.Nullable()
    java.lang.String filterUsed, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ProcessingInfo processingInfo, boolean isFavorite, boolean isDeleted, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> albumIds, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> tags) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFileName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFilePath() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getThumbnailPath() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMimeType() {
        return null;
    }
    
    public final long getFileSize() {
        return 0L;
    }
    
    public final int getWidth() {
        return 0;
    }
    
    public final int getHeight() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getDateAdded() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime getDateTaken() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime getDateModified() {
        return null;
    }
    
    public final int getOrientation() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoLocation getLocation() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ExifData getExifData() {
        return null;
    }
    
    public final boolean isYucramPhoto() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFilterUsed() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ProcessingInfo getProcessingInfo() {
        return null;
    }
    
    public final boolean isFavorite() {
        return false;
    }
    
    public final boolean isDeleted() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAlbumIds() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getTags() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component11() {
        return null;
    }
    
    public final int component12() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoLocation component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ExifData component14() {
        return null;
    }
    
    public final boolean component15() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ProcessingInfo component17() {
        return null;
    }
    
    public final boolean component18() {
        return false;
    }
    
    public final boolean component19() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component20() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component21() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.time.LocalDateTime component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.Photo copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String fileName, @org.jetbrains.annotations.NotNull()
    java.lang.String filePath, @org.jetbrains.annotations.Nullable()
    java.lang.String thumbnailPath, @org.jetbrains.annotations.NotNull()
    java.lang.String mimeType, long fileSize, int width, int height, @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime dateAdded, @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime dateTaken, @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime dateModified, int orientation, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoLocation location, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ExifData exifData, boolean isYucramPhoto, @org.jetbrains.annotations.Nullable()
    java.lang.String filterUsed, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ProcessingInfo processingInfo, boolean isFavorite, boolean isDeleted, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> albumIds, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> tags) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}