package com.qxyu.yucram.utils;

/**
 * 设备适配器
 * 检测和适配特定设备的专有相机功能
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0002J\u0010\u0010\f\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0002J\u0006\u0010\r\u001a\u00020\u000eJ\b\u0010\u000f\u001a\u0004\u0018\u00010\u0010J\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u00042\u0006\u0010\n\u001a\u00020\u000bJ\u0006\u0010\u0013\u001a\u00020\tJ\u0006\u0010\u0014\u001a\u00020\tR\u0014\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0015"}, d2 = {"Lcom/qxyu/yucram/utils/DeviceAdapter;", "", "()V", "OPPO_BRANDS", "", "", "OPPO_FIND_X8_MODELS", "TAG", "checkJpgMaxSupport", "", "context", "Landroid/content/Context;", "checkRawMaxSupport", "getDeviceInfo", "Lcom/qxyu/yucram/utils/DeviceInfo;", "getOppoSpecificSettings", "Lcom/qxyu/yucram/utils/OppoSettings;", "getSupportedProprietaryFormats", "Lcom/qxyu/yucram/utils/ProprietaryFormat;", "isOppoDevice", "isOppoFindX8Ultra", "app_debug"})
public final class DeviceAdapter {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "DeviceAdapter";
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> OPPO_BRANDS = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<java.lang.String> OPPO_FIND_X8_MODELS = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.utils.DeviceAdapter INSTANCE = null;
    
    private DeviceAdapter() {
        super();
    }
    
    /**
     * 检测是否为OPPO设备
     */
    public final boolean isOppoDevice() {
        return false;
    }
    
    /**
     * 检测是否为OPPO Find X8 Ultra
     */
    public final boolean isOppoFindX8Ultra() {
        return false;
    }
    
    /**
     * 获取设备信息
     */
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.utils.DeviceInfo getDeviceInfo() {
        return null;
    }
    
    /**
     * 检测设备支持的专有格式
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.utils.ProprietaryFormat> getSupportedProprietaryFormats(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    /**
     * 检测JPG MAX支持
     */
    private final boolean checkJpgMaxSupport(android.content.Context context) {
        return false;
    }
    
    /**
     * 检测RAW MAX支持
     */
    private final boolean checkRawMaxSupport(android.content.Context context) {
        return false;
    }
    
    /**
     * 获取OPPO专有相机设置
     */
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.utils.OppoSettings getOppoSpecificSettings() {
        return null;
    }
}