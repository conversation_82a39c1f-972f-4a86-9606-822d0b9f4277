package com.qxyu.yucram.presentation.photoedit.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.*
import kotlin.math.*

/**
 * 曲线调整面板
 */
@Composable
fun CurveAdjustmentPanel(
    curveAdjustments: CurveAdjustments,
    onAdjustmentsChanged: (CurveAdjustments) -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedCurve by remember { mutableStateOf(CurveType.RGB) }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "曲线调整",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 曲线类型选择器
        CurveTypeSelector(
            selectedCurve = selectedCurve,
            onCurveSelected = { selectedCurve = it },
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 曲线编辑器
        CurveEditor(
            curveType = selectedCurve,
            curvePoints = getCurrentCurve(curveAdjustments, selectedCurve),
            onCurveChanged = { newPoints ->
                val newAdjustments = updateCurve(curveAdjustments, selectedCurve, newPoints)
                onAdjustmentsChanged(newAdjustments)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
        )
        
        // 操作按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            TextButton(
                onClick = {
                    val resetAdjustments = resetCurve(curveAdjustments, selectedCurve)
                    onAdjustmentsChanged(resetAdjustments)
                }
            ) {
                Text(
                    text = "重置${selectedCurve.displayName}",
                    color = Color.White
                )
            }
            
            TextButton(
                onClick = {
                    val resetAdjustments = CurveAdjustments()
                    onAdjustmentsChanged(resetAdjustments)
                }
            ) {
                Text(
                    text = "重置全部",
                    color = Color.White
                )
            }
        }
    }
}

/**
 * 曲线类型选择器
 */
@Composable
fun CurveTypeSelector(
    selectedCurve: CurveType,
    onCurveSelected: (CurveType) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(CurveType.values()) { curveType ->
            CurveTypeButton(
                curveType = curveType,
                isSelected = selectedCurve == curveType,
                onClick = { onCurveSelected(curveType) }
            )
        }
    }
}

/**
 * 曲线类型按钮
 */
@Composable
fun CurveTypeButton(
    curveType: CurveType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) {
                curveType.color
            } else {
                Color.Transparent
            },
            contentColor = if (isSelected) {
                Color.White
            } else {
                curveType.color
            }
        ),
        shape = RoundedCornerShape(8.dp),
        modifier = modifier
    ) {
        Text(
            text = curveType.displayName,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * 曲线编辑器
 */
@Composable
fun CurveEditor(
    curveType: CurveType,
    curvePoints: List<CurvePoint>,
    onCurveChanged: (List<CurvePoint>) -> Unit,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    var draggedPointIndex by remember { mutableStateOf(-1) }
    var currentPoints by remember(curvePoints) { mutableStateOf(curvePoints) }
    
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(8.dp))
            .background(Color.Black.copy(alpha = 0.5f))
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { offset ->
                            // 查找最近的控制点
                            val canvasWidth = size.width.toFloat()
                            val canvasHeight = size.height.toFloat()
                            
                            draggedPointIndex = findNearestPoint(
                                currentPoints,
                                offset,
                                canvasWidth,
                                canvasHeight
                            )
                            
                            // 如果没有找到现有点，创建新点
                            if (draggedPointIndex == -1) {
                                val newPoint = CurvePoint(
                                    input = offset.x / canvasWidth,
                                    output = 1f - (offset.y / canvasHeight)
                                )
                                currentPoints = (currentPoints + newPoint).sortedBy { it.input }
                                draggedPointIndex = currentPoints.indexOf(newPoint)
                            }
                        },
                        onDrag = { change, _ ->
                            if (draggedPointIndex >= 0 && draggedPointIndex < currentPoints.size) {
                                val canvasWidth = size.width.toFloat()
                                val canvasHeight = size.height.toFloat()

                                val point = currentPoints[draggedPointIndex]
                                val newInput = ((point.input * canvasWidth + change.position.x) / canvasWidth)
                                    .coerceIn(0f, 1f)
                                val newOutput = ((1f - point.output) * canvasHeight - change.position.y) / canvasHeight
                                    .let { 1f - it }
                                    .coerceIn(0f, 1f)
                                
                                // 防止端点移动
                                if (draggedPointIndex == 0 || draggedPointIndex == currentPoints.size - 1) {
                                    currentPoints = currentPoints.toMutableList().apply {
                                        this[draggedPointIndex] = point.copy(output = newOutput)
                                    }
                                } else {
                                    currentPoints = currentPoints.toMutableList().apply {
                                        this[draggedPointIndex] = CurvePoint(newInput, newOutput)
                                    }.sortedBy { it.input }
                                }
                            }
                        },
                        onDragEnd = {
                            onCurveChanged(currentPoints)
                            draggedPointIndex = -1
                        }
                    )
                }
        ) {
            drawCurveEditor(curveType, currentPoints, draggedPointIndex)
        }
    }
}

/**
 * 绘制曲线编辑器
 */
private fun DrawScope.drawCurveEditor(
    curveType: CurveType,
    points: List<CurvePoint>,
    draggedPointIndex: Int
) {
    val canvasWidth = size.width
    val canvasHeight = size.height
    
    // 绘制网格
    drawGrid(canvasWidth, canvasHeight)
    
    // 绘制对角线参考线
    drawLine(
        color = Color.White.copy(alpha = 0.3f),
        start = Offset(0f, canvasHeight),
        end = Offset(canvasWidth, 0f),
        strokeWidth = 1.dp.toPx()
    )
    
    // 绘制曲线
    drawCurve(points, curveType.color, canvasWidth, canvasHeight)
    
    // 绘制控制点
    points.forEachIndexed { index, point ->
        val x = point.input * canvasWidth
        val y = (1f - point.output) * canvasHeight
        
        val pointColor = if (index == draggedPointIndex) {
            Color.White
        } else {
            curveType.color
        }
        
        val pointRadius = if (index == draggedPointIndex) 8.dp.toPx() else 6.dp.toPx()
        
        drawCircle(
            color = pointColor,
            radius = pointRadius,
            center = Offset(x, y)
        )
        
        // 绘制点的边框
        drawCircle(
            color = Color.White,
            radius = pointRadius,
            center = Offset(x, y),
            style = Stroke(width = 2.dp.toPx())
        )
    }
}

/**
 * 绘制网格
 */
private fun DrawScope.drawGrid(canvasWidth: Float, canvasHeight: Float) {
    val gridColor = Color.White.copy(alpha = 0.1f)
    val strokeWidth = 1.dp.toPx()
    
    // 垂直线
    for (i in 1..3) {
        val x = canvasWidth * i / 4f
        drawLine(
            color = gridColor,
            start = Offset(x, 0f),
            end = Offset(x, canvasHeight),
            strokeWidth = strokeWidth
        )
    }
    
    // 水平线
    for (i in 1..3) {
        val y = canvasHeight * i / 4f
        drawLine(
            color = gridColor,
            start = Offset(0f, y),
            end = Offset(canvasWidth, y),
            strokeWidth = strokeWidth
        )
    }
}

/**
 * 绘制曲线
 */
private fun DrawScope.drawCurve(
    points: List<CurvePoint>,
    color: Color,
    canvasWidth: Float,
    canvasHeight: Float
) {
    if (points.size < 2) return
    
    val path = Path()
    val strokeWidth = 3.dp.toPx()
    
    // 创建平滑曲线路径
    val controlPoints = mutableListOf<Offset>()
    points.forEach { point ->
        controlPoints.add(
            Offset(
                point.input * canvasWidth,
                (1f - point.output) * canvasHeight
            )
        )
    }
    
    path.moveTo(controlPoints[0].x, controlPoints[0].y)
    
    for (i in 1 until controlPoints.size) {
        val current = controlPoints[i]
        val previous = controlPoints[i - 1]
        
        if (i == 1) {
            path.lineTo(current.x, current.y)
        } else {
            // 使用二次贝塞尔曲线创建平滑过渡
            val controlPoint = Offset(
                (previous.x + current.x) / 2f,
                (previous.y + current.y) / 2f
            )
            path.quadraticBezierTo(
                previous.x, previous.y,
                controlPoint.x, controlPoint.y
            )
            path.lineTo(current.x, current.y)
        }
    }
    
    drawPath(
        path = path,
        color = color,
        style = Stroke(
            width = strokeWidth,
            cap = StrokeCap.Round,
            join = StrokeJoin.Round
        )
    )
}

/**
 * 查找最近的控制点
 */
private fun findNearestPoint(
    points: List<CurvePoint>,
    offset: Offset,
    canvasWidth: Float,
    canvasHeight: Float
): Int {
    val threshold = 30f // 像素阈值
    
    points.forEachIndexed { index, point ->
        val pointX = point.input * canvasWidth
        val pointY = (1f - point.output) * canvasHeight
        
        val distance = sqrt(
            (offset.x - pointX).pow(2) + (offset.y - pointY).pow(2)
        )
        
        if (distance <= threshold) {
            return index
        }
    }
    
    return -1
}

/**
 * 曲线类型枚举
 */
enum class CurveType(val displayName: String, val color: Color) {
    RGB("RGB", Color.White),
    RED("红", Color.Red),
    GREEN("绿", Color.Green),
    BLUE("蓝", Color.Blue)
}

/**
 * 获取当前曲线
 */
private fun getCurrentCurve(adjustments: CurveAdjustments, curveType: CurveType): List<CurvePoint> {
    return when (curveType) {
        CurveType.RGB -> adjustments.rgbCurve
        CurveType.RED -> adjustments.redCurve
        CurveType.GREEN -> adjustments.greenCurve
        CurveType.BLUE -> adjustments.blueCurve
    }
}

/**
 * 更新曲线
 */
private fun updateCurve(
    adjustments: CurveAdjustments,
    curveType: CurveType,
    newPoints: List<CurvePoint>
): CurveAdjustments {
    return when (curveType) {
        CurveType.RGB -> adjustments.copy(rgbCurve = newPoints)
        CurveType.RED -> adjustments.copy(redCurve = newPoints)
        CurveType.GREEN -> adjustments.copy(greenCurve = newPoints)
        CurveType.BLUE -> adjustments.copy(blueCurve = newPoints)
    }
}

/**
 * 重置曲线
 */
private fun resetCurve(
    adjustments: CurveAdjustments,
    curveType: CurveType
): CurveAdjustments {
    val defaultCurve = defaultLinearCurve()
    return when (curveType) {
        CurveType.RGB -> adjustments.copy(rgbCurve = defaultCurve)
        CurveType.RED -> adjustments.copy(redCurve = defaultCurve)
        CurveType.GREEN -> adjustments.copy(greenCurve = defaultCurve)
        CurveType.BLUE -> adjustments.copy(blueCurve = defaultCurve)
    }
}
