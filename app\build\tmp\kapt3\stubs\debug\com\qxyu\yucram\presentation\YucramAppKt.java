package com.qxyu.yucram.presentation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0018\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u0018\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u0003\u001a\u001a\u0010\u0007\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a\u0012\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u0007\u00a8\u0006\f"}, d2 = {"CompactLayout", "", "navController", "Landroidx/navigation/NavHostController;", "cameraManager", "Lcom/qxyu/yucram/camera/CameraManager;", "MediumLayout", "YucramApp", "modifier", "Landroidx/compose/ui/Modifier;", "rememberYucramAppState", "Lcom/qxyu/yucram/presentation/YucramAppState;", "app_debug"})
public final class YucramAppKt {
    
    /**
     * Yucram应用主容器
     */
    @androidx.compose.runtime.Composable()
    public static final void YucramApp(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.camera.CameraManager cameraManager, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 紧凑布局（手机）- 全屏沉浸式
     */
    @androidx.compose.runtime.Composable()
    private static final void CompactLayout(androidx.navigation.NavHostController navController, com.qxyu.yucram.camera.CameraManager cameraManager) {
    }
    
    /**
     * 中等布局（平板横屏）
     */
    @androidx.compose.runtime.Composable()
    private static final void MediumLayout(androidx.navigation.NavHostController navController, com.qxyu.yucram.camera.CameraManager cameraManager) {
    }
    
    /**
     * 应用状态管理
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.presentation.YucramAppState rememberYucramAppState(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavHostController navController) {
        return null;
    }
}