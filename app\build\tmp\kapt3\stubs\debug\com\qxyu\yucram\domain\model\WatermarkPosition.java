package com.qxyu.yucram.domain.model;

/**
 * 水印位置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"}, d2 = {"Lcom/qxyu/yucram/domain/model/WatermarkPosition;", "", "(Ljava/lang/String;I)V", "TOP_LEFT", "TOP_CENTER", "TOP_RIGHT", "CENTER_LEFT", "CENTER", "CENTER_RIGHT", "BOTTOM_LEFT", "BOTTOM_CENTER", "BOTTOM_RIGHT", "CUSTOM", "app_debug"})
public enum WatermarkPosition {
    /*public static final*/ TOP_LEFT /* = new TOP_LEFT() */,
    /*public static final*/ TOP_CENTER /* = new TOP_CENTER() */,
    /*public static final*/ TOP_RIGHT /* = new TOP_RIGHT() */,
    /*public static final*/ CENTER_LEFT /* = new CENTER_LEFT() */,
    /*public static final*/ CENTER /* = new CENTER() */,
    /*public static final*/ CENTER_RIGHT /* = new CENTER_RIGHT() */,
    /*public static final*/ BOTTOM_LEFT /* = new BOTTOM_LEFT() */,
    /*public static final*/ BOTTOM_CENTER /* = new BOTTOM_CENTER() */,
    /*public static final*/ BOTTOM_RIGHT /* = new BOTTOM_RIGHT() */,
    /*public static final*/ CUSTOM /* = new CUSTOM() */;
    
    WatermarkPosition() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.WatermarkPosition> getEntries() {
        return null;
    }
}