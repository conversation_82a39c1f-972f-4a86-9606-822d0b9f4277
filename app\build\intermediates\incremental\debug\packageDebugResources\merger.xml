<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res"><file name="ic_launcher" path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_orange">#FF6600</color><color name="primary_orange_dark">#E55A00</color><color name="primary_orange_light">#FF8533</color><color name="secondary_black">#1A1A1A</color><color name="secondary_gray">#2D2D2D</color><color name="secondary_light_gray">#4A4A4A</color><color name="background_dark">#0D0D0D</color><color name="background_medium">#1A1A1A</color><color name="background_light">#2D2D2D</color><color name="surface_dark">#1A1A1A</color><color name="surface_medium">#2D2D2D</color><color name="surface_light">#4A4A4A</color><color name="text_primary">#FFFFFF</color><color name="text_secondary">#CCCCCC</color><color name="text_tertiary">#999999</color><color name="accent_green">#4CAF50</color><color name="accent_red">#F44336</color><color name="accent_blue">#2196F3</color><color name="accent_yellow">#FFC107</color><color name="black_50">#80000000</color><color name="black_30">#4D000000</color><color name="white_50">#80FFFFFF</color><color name="white_30">#4DFFFFFF</color><color name="status_bar">#0D0D0D</color><color name="navigation_bar">#0D0D0D</color></file><file path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FF6600</color></file><file path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Yucram</string><string name="camera_permission_required">需要相机权限才能使用此功能</string><string name="storage_permission_required">需要存储权限才能保存照片</string><string name="location_permission_required">需要位置权限才能添加GPS水印</string><string name="camera">相机</string><string name="gallery">图库</string><string name="settings">设置</string><string name="filters">滤镜</string><string name="flash_auto">自动闪光</string><string name="flash_on">开启闪光</string><string name="flash_off">关闭闪光</string><string name="raw_format">RAW格式</string><string name="timer">定时器</string><string name="grid_lines">网格线</string><string name="level">水平仪</string><string name="image_quality">图像质量</string><string name="image_format">图像格式</string><string name="aspect_ratio">画幅比例</string><string name="theme_settings">主题设置</string><string name="vibration_feedback">震动反馈</string><string name="about_yucram">关于Yucram</string><string name="app_version">版本 2.0.0</string><string name="developer">开发者: QXyu</string><string name="app_description">专业胶片相机应用</string><string name="ok">确定</string><string name="cancel">取消</string><string name="save">保存</string><string name="delete">删除</string><string name="share">分享</string><string name="edit">编辑</string></file><file path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Yucram" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_orange</item>
        <item name="colorPrimaryDark">@color/primary_orange_dark</item>
        <item name="colorAccent">@color/primary_orange_light</item>

        
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        
        <item name="android:statusBarColor">@color/status_bar</item>
        <item name="android:navigationBarColor">@color/navigation_bar</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style><style name="Theme.Yucram.Splash" parent="Theme.Yucram">
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style></file><file name="backup_rules" path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\project\Androidstd\y3\yu augment1.0.0\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\project\Androidstd\y3\yu augment1.0.0\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\project\Androidstd\y3\yu augment1.0.0\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>