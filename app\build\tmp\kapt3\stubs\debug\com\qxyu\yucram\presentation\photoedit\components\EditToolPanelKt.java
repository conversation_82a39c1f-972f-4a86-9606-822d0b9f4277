package com.qxyu.yucram.presentation.photoedit.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0000\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\t\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\n\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u000b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a6\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\b\u0010\u001e\u001a\u00020\u001fH\u0002\u00a8\u0006 "}, d2 = {"BorderWatermarkToolPanel", "", "editParams", "Lcom/qxyu/yucram/domain/model/PhotoEditParams;", "onParamsChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "BrushPanel", "ClarityDehazePanel", "ContrastBrightnessPanel", "CropRotatePanel", "EditToolPanel", "tool", "Lcom/qxyu/yucram/domain/model/EditTool;", "ExposureAdjustmentPanel", "FilmEmulationPanel", "GraduatedFilterPanel", "GrainPanel", "HighlightsShadowsPanel", "LUTFiltersPanel", "MaskingPanel", "NoiseReductionPanel", "PerspectivePanel", "RadialFilterPanel", "SharpnessPanel", "TemperatureTintPanel", "VibranceSaturationPanel", "VignettePanel", "WhitesBlacksPanel", "getDefaultLUTFilter", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "app_debug"})
public final class EditToolPanelKt {
    
    /**
     * 编辑工具面板
     */
    @androidx.compose.runtime.Composable()
    public static final void EditToolPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.EditTool tool, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 曝光调整面板
     */
    @androidx.compose.runtime.Composable()
    public static final void ExposureAdjustmentPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 高光阴影面板
     */
    @androidx.compose.runtime.Composable()
    public static final void HighlightsShadowsPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 白色黑色面板
     */
    @androidx.compose.runtime.Composable()
    public static final void WhitesBlacksPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 对比度亮度面板
     */
    @androidx.compose.runtime.Composable()
    public static final void ContrastBrightnessPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 自然饱和度饱和度面板
     */
    @androidx.compose.runtime.Composable()
    public static final void VibranceSaturationPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 色温色调面板
     */
    @androidx.compose.runtime.Composable()
    public static final void TemperatureTintPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 锐度面板
     */
    @androidx.compose.runtime.Composable()
    public static final void SharpnessPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 降噪面板
     */
    @androidx.compose.runtime.Composable()
    public static final void NoiseReductionPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 清晰度去雾面板
     */
    @androidx.compose.runtime.Composable()
    public static final void ClarityDehazePanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 暗角面板
     */
    @androidx.compose.runtime.Composable()
    public static final void VignettePanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 颗粒面板
     */
    @androidx.compose.runtime.Composable()
    public static final void GrainPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void FilmEmulationPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void CropRotatePanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void PerspectivePanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void RadialFilterPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void GraduatedFilterPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void MaskingPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BrushPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void LUTFiltersPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    private static final com.qxyu.yucram.domain.model.LUTFilter getDefaultLUTFilter() {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkToolPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.PhotoEditParams, kotlin.Unit> onParamsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}