// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import android.content.Context;
import com.qxyu.yucram.processing.RawToLogProcessor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSystemModule_ProvideRawToLogProcessorFactory implements Factory<RawToLogProcessor> {
  private final Provider<Context> contextProvider;

  public FilterSystemModule_ProvideRawToLogProcessorFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public RawToLogProcessor get() {
    return provideRawToLogProcessor(contextProvider.get());
  }

  public static FilterSystemModule_ProvideRawToLogProcessorFactory create(
      Provider<Context> contextProvider) {
    return new FilterSystemModule_ProvideRawToLogProcessorFactory(contextProvider);
  }

  public static RawToLogProcessor provideRawToLogProcessor(Context context) {
    return Preconditions.checkNotNullFromProvides(FilterSystemModule.INSTANCE.provideRawToLogProcessor(context));
  }
}
