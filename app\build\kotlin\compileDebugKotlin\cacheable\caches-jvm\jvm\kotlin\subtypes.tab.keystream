2kotlinx.serialization.internal.GeneratedSerializerandroidx.lifecycle.ViewModelkotlin.Enumdagger.internal.Factory#androidx.activity.ComponentActivityandroid.app.Application+androidx.lifecycle.DefaultLifecycleObserverandroidx.room.RoomDatabase2com.qxyu.yucram.domain.repository.CameraRepository1com.qxyu.yucram.domain.repository.PhotoRepository4com.qxyu.yucram.domain.repository.SettingsRepository#androidx.lifecycle.AndroidViewModel'com.qxyu.yucram.data.local.dao.PhotoDao1com.qxyu.yucram.domain.model.AlbumOperationResult1com.qxyu.yucram.domain.model.PhotoOperationResult+com.qxyu.yucram.domain.model.PhotoLoadState1com.qxyu.yucram.domain.repository.AlbumRepository+com.qxyu.yucram.domain.model.AlbumLoadStateAcom.qxyu.yucram.presentation.photoedit.components.ToolInteraction+com.qxyu.yucram.domain.model.AdjustmentArea                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       