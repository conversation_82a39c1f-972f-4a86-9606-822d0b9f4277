package com.qxyu.yucram.presentation.borderwater;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000N\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u0000\n\u0002\b\u0005\u001a6\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001aD\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a8\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u00132\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a.\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00102\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00182\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\\\u0010\u0019\u001a\u00020\u00012\b\u0010\u001a\u001a\u0004\u0018\u00010\u001b2\u0006\u0010\u001c\u001a\u00020\u00132\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a2\u0010 \u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00102\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00010\u00182\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a2\u0010$\u001a\u00020\u00012\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006\'"}, d2 = {"BorderWatermarkError", "", "error", "", "onRetry", "Lkotlin/Function0;", "onNavigateBack", "modifier", "Landroidx/compose/ui/Modifier;", "BorderWatermarkScreen", "photoId", "onSaveComplete", "viewModel", "Lcom/qxyu/yucram/presentation/borderwater/BorderWatermarkViewModel;", "BorderWatermarkTabButton", "tab", "Lcom/qxyu/yucram/presentation/borderwater/BorderWatermarkTab;", "name", "isSelected", "", "onClick", "BorderWatermarkTabs", "currentTab", "onTabSelected", "Lkotlin/Function1;", "BorderWatermarkTopBar", "photo", "Lcom/qxyu/yucram/domain/model/Photo;", "hasChanges", "onReset", "onSave", "onPresets", "PresetSelectionDialog", "onPresetSelected", "", "onDismiss", "SaveConfirmationDialog", "onDiscard", "onCancel", "app_debug"})
public final class BorderWatermarkScreenKt {
    
    /**
     * 边框和水印编辑界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkScreen(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSaveComplete, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.borderwater.BorderWatermarkViewModel viewModel) {
    }
    
    /**
     * 边框水印顶部工具栏
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkTopBar(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.Photo photo, boolean hasChanges, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onReset, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSave, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onPresets, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 功能选项卡
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkTabs(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab currentTab, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab, kotlin.Unit> onTabSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 选项卡按钮
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkTabButton(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab tab, @org.jetbrains.annotations.NotNull()
    java.lang.String name, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 保存确认对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void SaveConfirmationDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSave, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDiscard, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel) {
    }
    
    /**
     * 预设选择对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void PresetSelectionDialog(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab currentTab, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<java.lang.Object, kotlin.Unit> onPresetSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    /**
     * 边框水印错误状态
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkError(@org.jetbrains.annotations.NotNull()
    java.lang.String error, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}