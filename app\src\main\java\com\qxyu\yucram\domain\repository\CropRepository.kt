package com.qxyu.yucram.domain.repository

import android.graphics.Bitmap
import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 裁剪和旋转操作Repository接口
 */
interface CropRepository {
    
    // ========== 裁剪操作 ==========
    
    /**
     * 应用裁剪到图像
     */
    suspend fun applyCrop(
        imagePath: String,
        cropSettings: CropSettings,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String>
    
    /**
     * 生成裁剪预览
     */
    suspend fun generateCropPreview(
        imagePath: String,
        cropSettings: CropSettings,
        previewSize: Int = 512
    ): Result<Bitmap>
    
    /**
     * 批量应用裁剪
     */
    suspend fun applyCropBatch(
        imagePaths: List<String>,
        cropSettings: CropSettings,
        outputDir: String,
        onProgress: (Float) -> Unit = {}
    ): Result<List<String>>
    
    /**
     * 智能裁剪建议
     */
    suspend fun getSmartCropSuggestions(
        imagePath: String,
        targetAspectRatio: AspectRatio? = null,
        maxSuggestions: Int = 5
    ): Result<List<SmartCropSuggestion>>
    
    /**
     * 验证裁剪设置
     */
    suspend fun validateCropSettings(
        imagePath: String,
        cropSettings: CropSettings
    ): Result<Boolean>
    
    // ========== 旋转操作 ==========
    
    /**
     * 应用旋转到图像
     */
    suspend fun applyRotation(
        imagePath: String,
        rotationSettings: RotationSettings,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String>
    
    /**
     * 生成旋转预览
     */
    suspend fun generateRotationPreview(
        imagePath: String,
        rotationSettings: RotationSettings,
        previewSize: Int = 512
    ): Result<Bitmap>
    
    /**
     * 自动拉直检测
     */
    suspend fun detectAutoStraighten(
        imagePath: String,
        method: StraightenMethod = StraightenMethod.HORIZON_DETECTION
    ): Result<AutoStraightenResult>
    
    /**
     * 应用透视校正
     */
    suspend fun applyPerspectiveCorrection(
        imagePath: String,
        perspectiveSettings: PerspectiveSettings,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String>
    
    /**
     * 生成透视校正预览
     */
    suspend fun generatePerspectivePreview(
        imagePath: String,
        perspectiveSettings: PerspectiveSettings,
        previewSize: Int = 512
    ): Result<Bitmap>
    
    // ========== 组合操作 ==========
    
    /**
     * 应用裁剪和旋转组合操作
     */
    suspend fun applyCropAndRotation(
        imagePath: String,
        cropSettings: CropSettings,
        rotationSettings: RotationSettings,
        perspectiveSettings: PerspectiveSettings? = null,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String>
    
    /**
     * 生成组合操作预览
     */
    suspend fun generateCombinedPreview(
        imagePath: String,
        cropSettings: CropSettings,
        rotationSettings: RotationSettings,
        perspectiveSettings: PerspectiveSettings? = null,
        previewSize: Int = 512
    ): Result<Bitmap>
    
    // ========== 预设管理 ==========
    
    /**
     * 获取所有裁剪预设
     */
    suspend fun getAllCropPresets(): Flow<List<CropPreset>>
    
    /**
     * 根据分类获取裁剪预设
     */
    suspend fun getCropPresetsByCategory(
        category: CropPresetCategory
    ): Flow<List<CropPreset>>
    
    /**
     * 创建裁剪预设
     */
    suspend fun createCropPreset(preset: CropPreset): Result<String>
    
    /**
     * 更新裁剪预设
     */
    suspend fun updateCropPreset(preset: CropPreset): Result<Unit>
    
    /**
     * 删除裁剪预设
     */
    suspend fun deleteCropPreset(presetId: String): Result<Unit>
    
    /**
     * 获取所有旋转预设
     */
    suspend fun getAllRotationPresets(): Flow<List<RotationPreset>>
    
    /**
     * 创建旋转预设
     */
    suspend fun createRotationPreset(preset: RotationPreset): Result<String>
    
    /**
     * 更新旋转预设
     */
    suspend fun updateRotationPreset(preset: RotationPreset): Result<Unit>
    
    /**
     * 删除旋转预设
     */
    suspend fun deleteRotationPreset(presetId: String): Result<Unit>
    
    // ========== 历史记录 ==========
    
    /**
     * 获取裁剪历史记录
     */
    suspend fun getCropHistory(photoId: String): Flow<List<CropHistory>>
    
    /**
     * 添加裁剪历史记录
     */
    suspend fun addCropHistory(history: CropHistory): Result<Unit>
    
    /**
     * 删除裁剪历史记录
     */
    suspend fun deleteCropHistory(historyId: String): Result<Unit>
    
    /**
     * 清空裁剪历史记录
     */
    suspend fun clearCropHistory(): Result<Unit>
    
    /**
     * 获取旋转历史记录
     */
    suspend fun getRotationHistory(photoId: String): Flow<List<RotationHistory>>
    
    /**
     * 添加旋转历史记录
     */
    suspend fun addRotationHistory(history: RotationHistory): Result<Unit>
    
    /**
     * 删除旋转历史记录
     */
    suspend fun deleteRotationHistory(historyId: String): Result<Unit>
    
    /**
     * 清空旋转历史记录
     */
    suspend fun clearRotationHistory(): Result<Unit>
    
    // ========== 工具方法 ==========
    
    /**
     * 获取图像信息
     */
    suspend fun getImageInfo(imagePath: String): Result<ImageInfo>
    
    /**
     * 计算最佳裁剪区域
     */
    suspend fun calculateOptimalCropArea(
        imagePath: String,
        targetAspectRatio: Float
    ): Result<CropRect>
    
    /**
     * 检测图像中的直线
     */
    suspend fun detectLines(
        imagePath: String,
        lineType: LineType = LineType.HORIZON
    ): Result<List<DetectedLine>>
    
    /**
     * 分析图像构图
     */
    suspend fun analyzeComposition(
        imagePath: String
    ): Result<CompositionAnalysis>
    
    /**
     * 生成网格辅助线
     */
    suspend fun generateGridLines(
        gridType: GridType,
        width: Float,
        height: Float
    ): List<GridLine>
}

/**
 * 图像信息
 */
data class ImageInfo(
    val width: Int,
    val height: Int,
    val aspectRatio: Float,
    val orientation: Int,
    val hasExif: Boolean,
    val colorSpace: String?,
    val fileSize: Long,
    val format: String
)

/**
 * 构图分析结果
 */
data class CompositionAnalysis(
    val ruleOfThirdsScore: Float, // 三分法评分
    val symmetryScore: Float, // 对称性评分
    val balanceScore: Float, // 平衡性评分
    val leadingLinesScore: Float, // 引导线评分
    val subjectPlacement: SubjectPlacement,
    val suggestions: List<CompositionSuggestion>
)

/**
 * 主体位置
 */
data class SubjectPlacement(
    val centerX: Float, // 0-1
    val centerY: Float, // 0-1
    val confidence: Float,
    val boundingBox: CropRect
)

/**
 * 构图建议
 */
data class CompositionSuggestion(
    val type: CompositionType,
    val description: String,
    val suggestedCrop: CropRect,
    val score: Float
)

/**
 * 构图类型
 */
enum class CompositionType(
    val displayName: String
) {
    RULE_OF_THIRDS("三分法"),
    CENTER_COMPOSITION("居中构图"),
    SYMMETRY("对称构图"),
    LEADING_LINES("引导线构图"),
    FRAME_IN_FRAME("框中框"),
    DIAGONAL("对角线构图"),
    PATTERN("图案构图"),
    NEGATIVE_SPACE("留白构图")
}

/**
 * 网格线
 */
data class GridLine(
    val startX: Float,
    val startY: Float,
    val endX: Float,
    val endY: Float,
    val type: GridLineType,
    val weight: Float = 1f
)

/**
 * 网格线类型
 */
enum class GridLineType {
    HORIZONTAL,
    VERTICAL,
    DIAGONAL,
    CURVE
}
