package com.qxyu.yucram.presentation.borderwater

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.PhotoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 边框和水印ViewModel
 */
@HiltViewModel
class BorderWatermarkViewModel @Inject constructor(
    private val photoRepository: PhotoRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(BorderWatermarkUiState())
    val uiState: StateFlow<BorderWatermarkUiState> = _uiState.asStateFlow()
    
    private val _photo = MutableStateFlow<Photo?>(null)
    val photo: StateFlow<Photo?> = _photo.asStateFlow()
    
    private val _settings = MutableStateFlow(BorderWatermarkSettings())
    val settings: StateFlow<BorderWatermarkSettings> = _settings.asStateFlow()
    
    private val _currentTab = MutableStateFlow(BorderWatermarkTab.BORDER)
    val currentTab: StateFlow<BorderWatermarkTab> = _currentTab.asStateFlow()
    
    // ========== 原始设置 ==========
    
    private val originalSettings = BorderWatermarkSettings()
    
    // ========== 照片加载 ==========
    
    fun loadPhoto(photoId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                val photo = photoRepository.getPhotoById(photoId)
                if (photo != null) {
                    _photo.value = photo
                    
                    // 加载现有的边框水印设置（如果有）
                    val existingSettings = loadExistingSettings(photo)
                    _settings.value = existingSettings
                    
                    _uiState.update { it.copy(isLoading = false) }
                } else {
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = "照片不存在"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载照片失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 选项卡管理 ==========
    
    fun selectTab(tab: BorderWatermarkTab) {
        _currentTab.value = tab
    }
    
    // ========== 设置管理 ==========
    
    fun updateSettings(newSettings: BorderWatermarkSettings) {
        _settings.value = newSettings
    }
    
    fun resetSettings() {
        _settings.value = originalSettings
    }
    
    fun hasChanges(): Boolean {
        return _settings.value != originalSettings
    }
    
    // ========== 预设管理 ==========
    
    fun applyPreset(preset: Any) {
        when (preset) {
            is BorderPreset -> {
                _settings.update { 
                    it.copy(borderSettings = preset.settings)
                }
            }
            
            is WatermarkPreset -> {
                _settings.update { 
                    it.copy(watermarkSettings = preset.settings)
                }
            }
            
            is BorderWatermarkPreset -> {
                _settings.value = preset.settings
            }
        }
    }
    
    fun saveAsPreset(name: String, description: String): BorderWatermarkPreset {
        return BorderWatermarkPreset(
            id = generatePresetId(),
            name = name,
            description = description,
            settings = _settings.value,
            category = BorderWatermarkCategory.USER
        )
    }
    
    // ========== 保存功能 ==========
    
    fun saveSettings() {
        val currentPhoto = _photo.value ?: return
        val currentSettings = _settings.value
        
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isSaving = true) }
                
                // TODO: 实际的图像处理和保存
                // 这里应该调用图像处理引擎应用边框和水印
                val processedPhoto = applyBorderAndWatermark(currentPhoto, currentSettings)
                
                val result = photoRepository.updatePhoto(processedPhoto)
                
                result.fold(
                    onSuccess = {
                        _uiState.update { 
                            it.copy(
                                isSaving = false,
                                isSaved = true,
                                message = "保存成功"
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(
                                isSaving = false,
                                error = "保存失败: ${error.message}"
                            )
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isSaving = false,
                        error = "保存失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 导出功能 ==========
    
    fun exportPhoto(format: ExportFormat, quality: Float) {
        val currentPhoto = _photo.value ?: return
        val currentSettings = _settings.value
        
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isExporting = true) }
                
                // TODO: 实现导出功能
                val exportedPath = exportPhotoWithBorderWatermark(
                    currentPhoto, 
                    currentSettings, 
                    format, 
                    quality
                )
                
                _uiState.update { 
                    it.copy(
                        isExporting = false,
                        message = "导出成功: $exportedPath"
                    )
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isExporting = false,
                        error = "导出失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 预览功能 ==========
    
    fun togglePreview() {
        _settings.update { 
            it.copy(previewEnabled = !it.previewEnabled)
        }
    }
    
    // ========== 辅助方法 ==========
    
    private fun loadExistingSettings(photo: Photo): BorderWatermarkSettings {
        // TODO: 从照片的处理信息中加载现有的边框水印设置
        return BorderWatermarkSettings()
    }
    
    private suspend fun applyBorderAndWatermark(
        photo: Photo, 
        settings: BorderWatermarkSettings
    ): Photo {
        // TODO: 实现实际的图像处理
        // 这里应该调用图像处理引擎应用边框和水印
        return photo.copy(
            processingInfo = photo.processingInfo?.copy(
                hasBorder = settings.borderSettings.isEnabled,
                hasWatermark = settings.watermarkSettings.isEnabled
            )
        )
    }
    
    private suspend fun exportPhotoWithBorderWatermark(
        photo: Photo,
        settings: BorderWatermarkSettings,
        format: ExportFormat,
        quality: Float
    ): String {
        // TODO: 实现实际的导出功能
        return "exported_photo.${format.name.lowercase()}"
    }
    
    private fun generatePresetId(): String {
        return "preset_${System.currentTimeMillis()}_${(0..999).random()}"
    }
    
    // ========== 内置预设 ==========
    
    fun getBuiltInBorderPresets(): List<BorderPreset> {
        return listOf(
            BorderPreset(
                id = "border_classic_white",
                name = "经典白框",
                description = "简洁的白色边框",
                settings = BorderSettings(
                    isEnabled = true,
                    borderType = BorderType.SOLID,
                    borderStyle = BorderStyle.CLASSIC,
                    width = 20f,
                    color = androidx.compose.ui.graphics.Color.White
                ),
                category = BorderCategory.CLASSIC
            ),
            BorderPreset(
                id = "border_vintage_sepia",
                name = "复古棕框",
                description = "复古风格的棕色边框",
                settings = BorderSettings(
                    isEnabled = true,
                    borderType = BorderType.SOLID,
                    borderStyle = BorderStyle.VINTAGE,
                    width = 30f,
                    color = androidx.compose.ui.graphics.Color(0xFF8B4513),
                    shadowEnabled = true
                ),
                category = BorderCategory.VINTAGE
            ),
            BorderPreset(
                id = "border_modern_gradient",
                name = "现代渐变",
                description = "现代风格的渐变边框",
                settings = BorderSettings(
                    isEnabled = true,
                    borderType = BorderType.GRADIENT,
                    borderStyle = BorderStyle.MODERN,
                    width = 25f,
                    gradientColors = listOf(
                        Color.Blue,
                        Color(0xFF9C27B0)
                    )
                ),
                category = BorderCategory.MODERN
            )
        )
    }
    
    fun getBuiltInWatermarkPresets(): List<WatermarkPreset> {
        return listOf(
            WatermarkPreset(
                id = "watermark_yucram_logo",
                name = "Yucram Logo",
                description = "Yucram品牌Logo水印",
                settings = WatermarkSettings(
                    isEnabled = true,
                    watermarkType = WatermarkType.LOGO,
                    logoType = LogoType.YUCRAM,
                    logoSize = 80f,
                    position = WatermarkPosition.BOTTOM_RIGHT,
                    opacity = 0.7f
                ),
                category = WatermarkCategory.LOGO
            ),
            WatermarkPreset(
                id = "watermark_photographer_text",
                name = "摄影师签名",
                description = "摄影师文字签名水印",
                settings = WatermarkSettings(
                    isEnabled = true,
                    watermarkType = WatermarkType.TEXT,
                    text = "© Photographer",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    textColor = Color.White,
                    position = WatermarkPosition.BOTTOM_LEFT,
                    textShadowEnabled = true
                ),
                category = WatermarkCategory.TEXT
            ),
            WatermarkPreset(
                id = "watermark_timestamp",
                name = "时间戳",
                description = "拍摄时间水印",
                settings = WatermarkSettings(
                    isEnabled = true,
                    watermarkType = WatermarkType.TIMESTAMP,
                    fontSize = 12.sp,
                    textColor = Color.Yellow,
                    position = WatermarkPosition.TOP_RIGHT
                ),
                category = WatermarkCategory.INFO
            )
        )
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
}

/**
 * 边框水印UI状态
 */
data class BorderWatermarkUiState(
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isExporting: Boolean = false,
    val isSaved: Boolean = false,
    val error: String? = null,
    val message: String? = null
)

/**
 * 边框水印组合预设
 */
data class BorderWatermarkPreset(
    val id: String,
    val name: String,
    val description: String,
    val thumbnail: String? = null,
    val settings: BorderWatermarkSettings,
    val category: BorderWatermarkCategory = BorderWatermarkCategory.USER,
    val isBuiltIn: Boolean = true
)

/**
 * 边框水印分类
 */
enum class BorderWatermarkCategory {
    USER,           // 用户
    CLASSIC,        // 经典
    MODERN,         // 现代
    VINTAGE,        // 复古
    ARTISTIC,       // 艺术
    PROFESSIONAL,   // 专业
    SOCIAL          // 社交
}
