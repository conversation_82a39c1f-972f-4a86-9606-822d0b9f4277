package com.qxyu.yucram.domain.model

import androidx.compose.ui.graphics.Color

/**
 * 专业照片编辑参数
 */
data class PhotoEditParams(
    // ========== 基础调整 ==========
    val exposure: Float = 0f,           // 曝光 (-2.0 to +2.0)
    val highlights: Float = 0f,         // 高光 (-100 to +100)
    val shadows: Float = 0f,            // 阴影 (-100 to +100)
    val whites: Float = 0f,             // 白色 (-100 to +100)
    val blacks: Float = 0f,             // 黑色 (-100 to +100)
    val contrast: Float = 0f,           // 对比度 (-100 to +100)
    val brightness: Float = 0f,         // 亮度 (-100 to +100)
    
    // ========== 色彩调整 ==========
    val vibrance: Float = 0f,           // 自然饱和度 (-100 to +100)
    val saturation: Float = 0f,         // 饱和度 (-100 to +100)
    val temperature: Float = 0f,        // 色温 (-100 to +100)
    val tint: Float = 0f,               // 色调 (-100 to +100)
    
    // ========== HSL调整 ==========
    val hslAdjustments: HSLAdjustments = HSLAdjustments(),
    
    // ========== 曲线调整 ==========
    val curves: CurveAdjustments = CurveAdjustments(),
    
    // ========== 细节调整 ==========
    val sharpness: Float = 0f,          // 锐度 (0 to +100)
    val noiseReduction: Float = 0f,     // 降噪 (0 to +100)
    val clarity: Float = 0f,            // 清晰度 (-100 to +100)
    val dehaze: Float = 0f,             // 去雾 (-100 to +100)
    
    // ========== 效果调整 ==========
    val vignette: Float = 0f,           // 暗角 (-100 to +100)
    val grain: Float = 0f,              // 颗粒 (0 to +100)
    val filmEmulation: String? = null,   // 胶片模拟
    
    // ========== 几何调整 ==========
    val rotation: Float = 0f,           // 旋转角度 (-45 to +45)
    val cropRatio: CropRatio? = null,   // 裁剪比例
    val perspective: PerspectiveAdjustment = PerspectiveAdjustment(),
    
    // ========== 局部调整 ==========
    val localAdjustments: List<LocalAdjustment> = emptyList(),

    // ========== 边框和水印 ==========
    val borderWatermarkSettings: BorderWatermarkSettings? = null,

    // ========== LUT滤镜 ==========
    val lutFilterSettings: LutFilterSettings? = null
)

/**
 * HSL调整参数
 */
data class HSLAdjustments(
    // 红色调整
    val redHue: Float = 0f,             // 红色色相 (-100 to +100)
    val redSaturation: Float = 0f,      // 红色饱和度 (-100 to +100)
    val redLuminance: Float = 0f,       // 红色明度 (-100 to +100)
    
    // 橙色调整
    val orangeHue: Float = 0f,
    val orangeSaturation: Float = 0f,
    val orangeLuminance: Float = 0f,
    
    // 黄色调整
    val yellowHue: Float = 0f,
    val yellowSaturation: Float = 0f,
    val yellowLuminance: Float = 0f,
    
    // 绿色调整
    val greenHue: Float = 0f,
    val greenSaturation: Float = 0f,
    val greenLuminance: Float = 0f,
    
    // 青色调整
    val cyanHue: Float = 0f,
    val cyanSaturation: Float = 0f,
    val cyanLuminance: Float = 0f,
    
    // 蓝色调整
    val blueHue: Float = 0f,
    val blueSaturation: Float = 0f,
    val blueLuminance: Float = 0f,
    
    // 紫色调整
    val purpleHue: Float = 0f,
    val purpleSaturation: Float = 0f,
    val purpleLuminance: Float = 0f,
    
    // 洋红调整
    val magentaHue: Float = 0f,
    val magentaSaturation: Float = 0f,
    val magentaLuminance: Float = 0f
)

/**
 * 曲线调整参数
 */
data class CurveAdjustments(
    val rgbCurve: List<CurvePoint> = defaultLinearCurve(),      // RGB曲线
    val redCurve: List<CurvePoint> = defaultLinearCurve(),      // 红色曲线
    val greenCurve: List<CurvePoint> = defaultLinearCurve(),    // 绿色曲线
    val blueCurve: List<CurvePoint> = defaultLinearCurve()      // 蓝色曲线
)

/**
 * 曲线控制点
 */
data class CurvePoint(
    val input: Float,   // 输入值 (0.0 to 1.0)
    val output: Float   // 输出值 (0.0 to 1.0)
)

/**
 * 默认线性曲线
 */
fun defaultLinearCurve(): List<CurvePoint> = listOf(
    CurvePoint(0f, 0f),
    CurvePoint(1f, 1f)
)

/**
 * 裁剪比例
 */
enum class CropRatio(val ratio: Float, val displayName: String) {
    ORIGINAL(0f, "原始"),
    SQUARE(1f, "1:1"),
    RATIO_4_3(4f/3f, "4:3"),
    RATIO_3_2(3f/2f, "3:2"),
    RATIO_16_9(16f/9f, "16:9"),
    RATIO_21_9(21f/9f, "21:9"),
    CUSTOM(-1f, "自定义")
}

/**
 * 透视调整
 */
data class PerspectiveAdjustment(
    val verticalPerspective: Float = 0f,    // 垂直透视 (-100 to +100)
    val horizontalPerspective: Float = 0f,  // 水平透视 (-100 to +100)
    val distortion: Float = 0f,             // 畸变校正 (-100 to +100)
    val scale: Float = 1f                   // 缩放 (0.5 to 2.0)
)

/**
 * 局部调整
 */
data class LocalAdjustment(
    val id: String,
    val type: LocalAdjustmentType,
    val area: AdjustmentArea,
    val params: PhotoEditParams,
    val opacity: Float = 1f,                // 不透明度 (0.0 to 1.0)
    val blendMode: BlendMode = BlendMode.NORMAL
)

/**
 * 局部调整类型
 */
enum class LocalAdjustmentType {
    RADIAL_FILTER,      // 径向滤镜
    GRADUATED_FILTER,   // 渐变滤镜
    MASKING,           // 蒙版
    BRUSH              // 画笔
}

/**
 * 调整区域
 */
sealed class AdjustmentArea {
    data class RadialArea(
        val centerX: Float,     // 中心X坐标 (0.0 to 1.0)
        val centerY: Float,     // 中心Y坐标 (0.0 to 1.0)
        val radiusX: Float,     // X轴半径 (0.0 to 1.0)
        val radiusY: Float,     // Y轴半径 (0.0 to 1.0)
        val feather: Float,     // 羽化 (0.0 to 1.0)
        val invert: Boolean = false
    ) : AdjustmentArea()
    
    data class GradientArea(
        val startX: Float,      // 起始X坐标 (0.0 to 1.0)
        val startY: Float,      // 起始Y坐标 (0.0 to 1.0)
        val endX: Float,        // 结束X坐标 (0.0 to 1.0)
        val endY: Float,        // 结束Y坐标 (0.0 to 1.0)
        val feather: Float,     // 羽化 (0.0 to 1.0)
        val invert: Boolean = false
    ) : AdjustmentArea()
    
    data class MaskArea(
        val maskData: ByteArray   // 蒙版数据
    ) : AdjustmentArea()
}

/**
 * 混合模式
 */
enum class BlendMode {
    NORMAL,         // 正常
    MULTIPLY,       // 正片叠底
    SCREEN,         // 滤色
    OVERLAY,        // 叠加
    SOFT_LIGHT,     // 柔光
    HARD_LIGHT,     // 强光
    COLOR_DODGE,    // 颜色减淡
    COLOR_BURN,     // 颜色加深
    DARKEN,         // 变暗
    LIGHTEN,        // 变亮
    DIFFERENCE,     // 差值
    EXCLUSION       // 排除
}

/**
 * 编辑工具类型
 */
enum class EditTool {
    // 基础调整
    EXPOSURE,
    HIGHLIGHTS_SHADOWS,
    WHITES_BLACKS,
    CONTRAST_BRIGHTNESS,
    
    // 色彩调整
    VIBRANCE_SATURATION,
    TEMPERATURE_TINT,
    HSL,
    
    // 曲线
    CURVES,
    
    // 细节
    SHARPNESS,
    NOISE_REDUCTION,
    CLARITY_DEHAZE,
    
    // 效果
    VIGNETTE,
    GRAIN,
    FILM_EMULATION,
    
    // 几何
    CROP_ROTATE,
    PERSPECTIVE,
    
    // 局部调整
    RADIAL_FILTER,
    GRADUATED_FILTER,
    MASKING,
    BRUSH,
    
    // LUT滤镜
    LUT_FILTERS,

    // 边框水印
    BORDER_WATERMARK
}

/**
 * 编辑历史记录
 */
data class EditHistory(
    val id: String,
    val timestamp: Long,
    val tool: EditTool,
    val beforeParams: PhotoEditParams,
    val afterParams: PhotoEditParams,
    val description: String
)

/**
 * 编辑预设
 */
data class EditPreset(
    val id: String,
    val name: String,
    val description: String,
    val params: PhotoEditParams,
    val thumbnail: String? = null,
    val isBuiltIn: Boolean = false,
    val category: PresetCategory = PresetCategory.USER
)

/**
 * 预设分类
 */
enum class PresetCategory {
    USER,           // 用户预设
    PORTRAIT,       // 人像
    LANDSCAPE,      // 风景
    STREET,         // 街拍
    VINTAGE,        // 复古
    DRAMATIC,       // 戏剧性
    NATURAL,        // 自然
    BLACK_WHITE     // 黑白
}
