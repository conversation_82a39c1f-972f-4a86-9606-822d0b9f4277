package com.qxyu.yucram.domain.model;

/**
 * 相机设置数据模型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b&\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0091\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\t\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\t\u0012\b\b\u0002\u0010\u000f\u001a\u00020\t\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0016\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\u0002\u0010\u0019J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\t\u0010-\u001a\u00020\u0011H\u00c6\u0003J\t\u0010.\u001a\u00020\u0013H\u00c6\u0003J\t\u0010/\u001a\u00020\u0011H\u00c6\u0003J\t\u00100\u001a\u00020\u0016H\u00c6\u0003J\t\u00101\u001a\u00020\u0018H\u00c6\u0003J\t\u00102\u001a\u00020\u0005H\u00c6\u0003J\t\u00103\u001a\u00020\u0007H\u00c6\u0003J\t\u00104\u001a\u00020\tH\u00c6\u0003J\t\u00105\u001a\u00020\tH\u00c6\u0003J\t\u00106\u001a\u00020\tH\u00c6\u0003J\t\u00107\u001a\u00020\rH\u00c6\u0003J\t\u00108\u001a\u00020\tH\u00c6\u0003J\t\u00109\u001a\u00020\tH\u00c6\u0003J\u0095\u0001\u0010:\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\t2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\t2\b\b\u0002\u0010\u000f\u001a\u00020\t2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u00112\b\b\u0002\u0010\u0015\u001a\u00020\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u00c6\u0001J\u0013\u0010;\u001a\u00020\t2\b\u0010<\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010=\u001a\u00020\u0011H\u00d6\u0001J\t\u0010>\u001a\u00020?H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0014\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u000e\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010&R\u0011\u0010\u000b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010&R\u0011\u0010\u000f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010&R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010&R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010&R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001dR\u0011\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0011\u0010\u0012\u001a\u00020\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+\u00a8\u0006@"}, d2 = {"Lcom/qxyu/yucram/domain/model/CameraSettings;", "", "aspectRatio", "Lcom/qxyu/yucram/domain/model/AspectRatio;", "imageQuality", "Lcom/qxyu/yucram/domain/model/ImageQuality;", "imageFormat", "Lcom/qxyu/yucram/domain/model/ImageFormat;", "isRawEnabled", "", "isLogEnabled", "isHdrEnabled", "flashMode", "Lcom/qxyu/yucram/domain/model/FlashMode;", "isGridEnabled", "isLevelEnabled", "timerDuration", "", "zoomLevel", "", "exposureCompensation", "focusMode", "Lcom/qxyu/yucram/domain/model/FocusMode;", "whiteBalance", "Lcom/qxyu/yucram/domain/model/WhiteBalance;", "(Lcom/qxyu/yucram/domain/model/AspectRatio;Lcom/qxyu/yucram/domain/model/ImageQuality;Lcom/qxyu/yucram/domain/model/ImageFormat;ZZZLcom/qxyu/yucram/domain/model/FlashMode;ZZIFILcom/qxyu/yucram/domain/model/FocusMode;Lcom/qxyu/yucram/domain/model/WhiteBalance;)V", "getAspectRatio", "()Lcom/qxyu/yucram/domain/model/AspectRatio;", "getExposureCompensation", "()I", "getFlashMode", "()Lcom/qxyu/yucram/domain/model/FlashMode;", "getFocusMode", "()Lcom/qxyu/yucram/domain/model/FocusMode;", "getImageFormat", "()Lcom/qxyu/yucram/domain/model/ImageFormat;", "getImageQuality", "()Lcom/qxyu/yucram/domain/model/ImageQuality;", "()Z", "getTimerDuration", "getWhiteBalance", "()Lcom/qxyu/yucram/domain/model/WhiteBalance;", "getZoomLevel", "()F", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
public final class CameraSettings {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.AspectRatio aspectRatio = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ImageQuality imageQuality = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ImageFormat imageFormat = null;
    private final boolean isRawEnabled = false;
    private final boolean isLogEnabled = false;
    private final boolean isHdrEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.FlashMode flashMode = null;
    private final boolean isGridEnabled = false;
    private final boolean isLevelEnabled = false;
    private final int timerDuration = 0;
    private final float zoomLevel = 0.0F;
    private final int exposureCompensation = 0;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.FocusMode focusMode = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.WhiteBalance whiteBalance = null;
    
    public CameraSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.AspectRatio aspectRatio, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ImageQuality imageQuality, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ImageFormat imageFormat, boolean isRawEnabled, boolean isLogEnabled, boolean isHdrEnabled, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.FlashMode flashMode, boolean isGridEnabled, boolean isLevelEnabled, int timerDuration, float zoomLevel, int exposureCompensation, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.FocusMode focusMode, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WhiteBalance whiteBalance) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.AspectRatio getAspectRatio() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ImageQuality getImageQuality() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ImageFormat getImageFormat() {
        return null;
    }
    
    public final boolean isRawEnabled() {
        return false;
    }
    
    public final boolean isLogEnabled() {
        return false;
    }
    
    public final boolean isHdrEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.FlashMode getFlashMode() {
        return null;
    }
    
    public final boolean isGridEnabled() {
        return false;
    }
    
    public final boolean isLevelEnabled() {
        return false;
    }
    
    public final int getTimerDuration() {
        return 0;
    }
    
    public final float getZoomLevel() {
        return 0.0F;
    }
    
    public final int getExposureCompensation() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.FocusMode getFocusMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WhiteBalance getWhiteBalance() {
        return null;
    }
    
    public CameraSettings() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.AspectRatio component1() {
        return null;
    }
    
    public final int component10() {
        return 0;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    public final int component12() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.FocusMode component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WhiteBalance component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ImageQuality component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ImageFormat component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.FlashMode component7() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.CameraSettings copy(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.AspectRatio aspectRatio, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ImageQuality imageQuality, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ImageFormat imageFormat, boolean isRawEnabled, boolean isLogEnabled, boolean isHdrEnabled, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.FlashMode flashMode, boolean isGridEnabled, boolean isLevelEnabled, int timerDuration, float zoomLevel, int exposureCompensation, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.FocusMode focusMode, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WhiteBalance whiteBalance) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}