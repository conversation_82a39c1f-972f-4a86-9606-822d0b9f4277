package com.qxyu.yucram.domain.model;

/**
 * 相册操作结果
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0005\u0003\u0004\u0005\u0006\u0007B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0005\b\t\n\u000b\f\u00a8\u0006\r"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumOperationResult;", "", "()V", "AlbumCreated", "AlbumDeleted", "AlbumUpdated", "Error", "Success", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult$AlbumCreated;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult$AlbumDeleted;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult$AlbumUpdated;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult$Error;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult$Success;", "app_debug"})
public abstract class AlbumOperationResult {
    
    private AlbumOperationResult() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumOperationResult$AlbumCreated;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult;", "album", "Lcom/qxyu/yucram/domain/model/Album;", "(Lcom/qxyu/yucram/domain/model/Album;)V", "getAlbum", "()Lcom/qxyu/yucram/domain/model/Album;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class AlbumCreated extends com.qxyu.yucram.domain.model.AlbumOperationResult {
        @org.jetbrains.annotations.NotNull()
        private final com.qxyu.yucram.domain.model.Album album = null;
        
        public AlbumCreated(@org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.Album album) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.Album getAlbum() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.Album component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.AlbumOperationResult.AlbumCreated copy(@org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.Album album) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumOperationResult$AlbumDeleted;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult;", "albumId", "", "(Ljava/lang/String;)V", "getAlbumId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class AlbumDeleted extends com.qxyu.yucram.domain.model.AlbumOperationResult {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String albumId = null;
        
        public AlbumDeleted(@org.jetbrains.annotations.NotNull()
        java.lang.String albumId) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getAlbumId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.AlbumOperationResult.AlbumDeleted copy(@org.jetbrains.annotations.NotNull()
        java.lang.String albumId) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumOperationResult$AlbumUpdated;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult;", "album", "Lcom/qxyu/yucram/domain/model/Album;", "(Lcom/qxyu/yucram/domain/model/Album;)V", "getAlbum", "()Lcom/qxyu/yucram/domain/model/Album;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class AlbumUpdated extends com.qxyu.yucram.domain.model.AlbumOperationResult {
        @org.jetbrains.annotations.NotNull()
        private final com.qxyu.yucram.domain.model.Album album = null;
        
        public AlbumUpdated(@org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.Album album) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.Album getAlbum() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.Album component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.AlbumOperationResult.AlbumUpdated copy(@org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.Album album) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0003\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0015"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumOperationResult$Error;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult;", "message", "", "exception", "", "(Ljava/lang/String;Ljava/lang/Throwable;)V", "getException", "()Ljava/lang/Throwable;", "getMessage", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class Error extends com.qxyu.yucram.domain.model.AlbumOperationResult {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String message = null;
        @org.jetbrains.annotations.Nullable()
        private final java.lang.Throwable exception = null;
        
        public Error(@org.jetbrains.annotations.NotNull()
        java.lang.String message, @org.jetbrains.annotations.Nullable()
        java.lang.Throwable exception) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getMessage() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Throwable getException() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final java.lang.Throwable component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.AlbumOperationResult.Error copy(@org.jetbrains.annotations.NotNull()
        java.lang.String message, @org.jetbrains.annotations.Nullable()
        java.lang.Throwable exception) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumOperationResult$Success;", "Lcom/qxyu/yucram/domain/model/AlbumOperationResult;", "()V", "app_debug"})
    public static final class Success extends com.qxyu.yucram.domain.model.AlbumOperationResult {
        @org.jetbrains.annotations.NotNull()
        public static final com.qxyu.yucram.domain.model.AlbumOperationResult.Success INSTANCE = null;
        
        private Success() {
        }
    }
}