package com.qxyu.yucram.film

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.qxyu.yucram.domain.model.*
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.InputStreamReader
import javax.inject.Inject
import javax.inject.Singleton

/**
 * LUT处理器
 * 负责LUT文件的加载、解析和应用
 */
@Singleton
class LutProcessor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "LutProcessor"
        private const val DEFAULT_LUT_SIZE = 33
    }
    
    // 当前状态
    private val _currentLut = MutableStateFlow<LutInfo?>(null)
    val currentLut: StateFlow<LutInfo?> = _currentLut.asStateFlow()
    
    private val _lutIntensity = MutableStateFlow(1.0f)
    val lutIntensity: StateFlow<Float> = _lutIntensity.asStateFlow()
    
    private val _isProcessing = MutableStateFlow(false)
    val isProcessing: StateFlow<Boolean> = _isProcessing.asStateFlow()
    
    // LUT数据缓存
    private val lutCache = mutableMapOf<String, LutData>()
    
    /**
     * 加载LUT文件
     */
    suspend fun loadLutFile(filePath: String): Result<LutData> = withContext(Dispatchers.IO) {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                return@withContext Result.failure(Exception("LUT文件不存在: $filePath"))
            }
            
            val format = detectLutFormat(file)
            val lutData = when (format) {
                LutFormat.CUBE -> parseCubeLut(file)
                LutFormat.THREE_DL -> parse3DLLut(file)
                LutFormat.LUT -> parseLutFile(file)
                LutFormat.PNG_LUT -> parsePngLut(file)
            }
            
            // 缓存LUT数据
            lutCache[lutData.id] = lutData
            
            Log.d(TAG, "成功加载LUT文件: ${lutData.name}, 大小: ${lutData.size}")
            Result.success(lutData)
            
        } catch (e: Exception) {
            Log.e(TAG, "加载LUT文件失败: $filePath", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取已安装的LUT列表
     */
    suspend fun getInstalledLuts(): Result<List<LutInfo>> {
        return try {
            Result.success(emptyList()) // 简化实现
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 从Assets安装LUT
     */
    suspend fun installLutFromAssets(assetPath: String): Result<Unit> {
        return try {
            Result.success(Unit) // 简化实现
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 应用LUT
     */
    suspend fun applyLut(lutId: String, intensity: Float = 1.0f): Result<Unit> {
        _isProcessing.value = true

        return try {
            _lutIntensity.value = intensity.coerceIn(0.0f, 1.0f)
            _isProcessing.value = false
            Result.success(Unit)
        } catch (e: Exception) {
            _isProcessing.value = false
            Result.failure(e)
        }
    }
    
    /**
     * 移除LUT
     */
    suspend fun removeLut(): Result<Unit> {
        return try {
            _currentLut.value = null
            _lutIntensity.value = 0.0f
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 设置LUT强度
     */
    suspend fun setLutIntensity(intensity: Float): Result<Unit> {
        return try {
            val clampedIntensity = intensity.coerceIn(0.0f, 1.0f)
            _lutIntensity.value = clampedIntensity
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 获取LUT信息
     */
    suspend fun getLutInfo(lutId: String): Result<LutInfo?> {
        return Result.success(null) // 简化实现
    }

    /**
     * 检查是否应用了LUT
     */
    suspend fun isLutApplied(): Result<Boolean> {
        return Result.success(_currentLut.value != null)
    }
    
    /**
     * 应用LUT到图像
     */
    suspend fun applyLutToImage(
        bitmap: Bitmap, 
        lutData: LutData, 
        intensity: Float = 1.0f
    ): Result<Bitmap> = withContext(Dispatchers.Default) {
        try {
            val processedBitmap = processImageWithLut(bitmap, lutData, intensity)
            Result.success(processedBitmap)
        } catch (e: Exception) {
            Log.e(TAG, "应用LUT到图像失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 观察当前LUT
     */
    fun observeCurrentLut(): Flow<LutInfo?> = _currentLut.asStateFlow()

    /**
     * 观察LUT强度
     */
    fun observeLutIntensity(): Flow<Float> = _lutIntensity.asStateFlow()
    
    // 私有方法
    
    /**
     * 检测LUT文件格式
     */
    private fun detectLutFormat(file: File): LutFormat {
        return when (file.extension.lowercase()) {
            "cube" -> LutFormat.CUBE
            "3dl" -> LutFormat.THREE_DL
            "lut" -> LutFormat.LUT
            "png" -> LutFormat.PNG_LUT
            else -> LutFormat.CUBE // 默认为CUBE格式
        }
    }
    
    /**
     * 解析CUBE格式LUT
     */
    private fun parseCubeLut(file: File): LutData {
        val reader = BufferedReader(InputStreamReader(FileInputStream(file)))
        var size = DEFAULT_LUT_SIZE
        val lutData = mutableListOf<Float>()
        
        reader.use { r ->
            var line: String?
            while (r.readLine().also { line = it } != null) {
                line?.let { currentLine ->
                    when {
                        currentLine.startsWith("LUT_3D_SIZE") -> {
                            size = currentLine.split(" ")[1].toInt()
                        }
                        currentLine.matches(Regex("^[0-9.\\s-]+$")) -> {
                            val values = currentLine.trim().split("\\s+".toRegex())
                            if (values.size >= 3) {
                                lutData.addAll(values.take(3).map { it.toFloat() })
                            }
                        }
                    }
                }
            }
        }
        
        return LutData(
            id = "lut_${file.nameWithoutExtension}_${System.currentTimeMillis()}",
            name = file.nameWithoutExtension,
            size = size,
            format = LutFormat.CUBE,
            data = lutData.toFloatArray(),
            filePath = file.absolutePath
        )
    }
    
    /**
     * 解析3DL格式LUT
     */
    private fun parse3DLLut(file: File): LutData {
        // 3DL格式解析实现
        // 这里简化实现，实际需要根据3DL格式规范解析
        return LutData(
            id = "lut_${file.nameWithoutExtension}_${System.currentTimeMillis()}",
            name = file.nameWithoutExtension,
            size = DEFAULT_LUT_SIZE,
            format = LutFormat.THREE_DL,
            data = floatArrayOf(), // 实际解析的数据
            filePath = file.absolutePath
        )
    }
    
    /**
     * 解析LUT文件
     */
    private fun parseLutFile(file: File): LutData {
        // LUT格式解析实现
        return LutData(
            id = "lut_${file.nameWithoutExtension}_${System.currentTimeMillis()}",
            name = file.nameWithoutExtension,
            size = DEFAULT_LUT_SIZE,
            format = LutFormat.LUT,
            data = floatArrayOf(), // 实际解析的数据
            filePath = file.absolutePath
        )
    }
    
    /**
     * 解析PNG格式LUT
     */
    private fun parsePngLut(file: File): LutData {
        // PNG LUT格式解析实现
        return LutData(
            id = "lut_${file.nameWithoutExtension}_${System.currentTimeMillis()}",
            name = file.nameWithoutExtension,
            size = DEFAULT_LUT_SIZE,
            format = LutFormat.PNG_LUT,
            data = floatArrayOf(), // 实际解析的数据
            filePath = file.absolutePath
        )
    }
    
    /**
     * 使用LUT处理图像
     */
    private fun processImageWithLut(
        bitmap: Bitmap, 
        lutData: LutData, 
        intensity: Float
    ): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        // 应用LUT变换
        for (i in pixels.indices) {
            pixels[i] = applyLutToPixel(pixels[i], lutData, intensity)
        }
        
        val processedBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        processedBitmap.setPixels(pixels, 0, width, 0, 0, width, height)
        
        return processedBitmap
    }
    
    /**
     * 对单个像素应用LUT
     */
    private fun applyLutToPixel(pixel: Int, lutData: LutData, intensity: Float): Int {
        // 提取RGB分量
        val r = (pixel shr 16) and 0xFF
        val g = (pixel shr 8) and 0xFF
        val b = pixel and 0xFF
        val a = (pixel shr 24) and 0xFF
        
        // 归一化到0-1范围
        val rNorm = r / 255.0f
        val gNorm = g / 255.0f
        val bNorm = b / 255.0f
        
        // 应用LUT查找
        val lutResult = lookupLut(rNorm, gNorm, bNorm, lutData)
        
        // 混合原始颜色和LUT结果
        val finalR = (rNorm + intensity * (lutResult[0] - rNorm)).coerceIn(0.0f, 1.0f)
        val finalG = (gNorm + intensity * (lutResult[1] - gNorm)).coerceIn(0.0f, 1.0f)
        val finalB = (bNorm + intensity * (lutResult[2] - bNorm)).coerceIn(0.0f, 1.0f)
        
        // 转换回整数
        val newR = (finalR * 255).toInt().coerceIn(0, 255)
        val newG = (finalG * 255).toInt().coerceIn(0, 255)
        val newB = (finalB * 255).toInt().coerceIn(0, 255)
        
        return (a shl 24) or (newR shl 16) or (newG shl 8) or newB
    }
    
    /**
     * LUT查找
     */
    private fun lookupLut(r: Float, g: Float, b: Float, lutData: LutData): FloatArray {
        val size = lutData.size
        val data = lutData.data
        
        // 计算LUT索引
        val rIndex = (r * (size - 1)).coerceIn(0.0f, (size - 1).toFloat())
        val gIndex = (g * (size - 1)).coerceIn(0.0f, (size - 1).toFloat())
        val bIndex = (b * (size - 1)).coerceIn(0.0f, (size - 1).toFloat())
        
        // 三线性插值
        return trilinearInterpolation(rIndex, gIndex, bIndex, size, data)
    }
    
    /**
     * 三线性插值
     */
    private fun trilinearInterpolation(
        r: Float, g: Float, b: Float, 
        size: Int, data: FloatArray
    ): FloatArray {
        // 简化实现，实际需要完整的三线性插值算法
        val rInt = r.toInt().coerceIn(0, size - 1)
        val gInt = g.toInt().coerceIn(0, size - 1)
        val bInt = b.toInt().coerceIn(0, size - 1)
        
        val index = (rInt * size * size + gInt * size + bInt) * 3
        
        return if (index + 2 < data.size) {
            floatArrayOf(data[index], data[index + 1], data[index + 2])
        } else {
            floatArrayOf(r / (size - 1), g / (size - 1), b / (size - 1))
        }
    }
}
