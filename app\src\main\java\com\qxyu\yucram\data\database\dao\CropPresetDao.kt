package com.qxyu.yucram.data.database.dao

import androidx.room.*
import com.qxyu.yucram.data.database.entity.CropPresetEntity
import kotlinx.coroutines.flow.Flow

/**
 * 裁剪预设DAO
 */
@Dao
interface CropPresetDao {
    
    /**
     * 获取所有裁剪预设
     */
    @Query("SELECT * FROM crop_presets ORDER BY sortOrder ASC, name ASC")
    fun getAllPresets(): Flow<List<CropPresetEntity>>
    
    /**
     * 根据分类获取裁剪预设
     */
    @Query("SELECT * FROM crop_presets WHERE category = :category ORDER BY sortOrder ASC, name ASC")
    fun getPresetsByCategory(category: String): Flow<List<CropPresetEntity>>
    
    /**
     * 获取内置预设
     */
    @Query("SELECT * FROM crop_presets WHERE isBuiltIn = 1 ORDER BY sortOrder ASC, name ASC")
    fun getBuiltInPresets(): Flow<List<CropPresetEntity>>
    
    /**
     * 获取用户自定义预设
     */
    @Query("SELECT * FROM crop_presets WHERE isBuiltIn = 0 ORDER BY sortOrder ASC, name ASC")
    fun getUserPresets(): Flow<List<CropPresetEntity>>
    
    /**
     * 根据ID获取预设
     */
    @Query("SELECT * FROM crop_presets WHERE id = :presetId")
    suspend fun getPresetById(presetId: String): CropPresetEntity?
    
    /**
     * 根据名称获取预设
     */
    @Query("SELECT * FROM crop_presets WHERE name = :name LIMIT 1")
    suspend fun getPresetByName(name: String): CropPresetEntity?
    
    /**
     * 插入预设
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPreset(preset: CropPresetEntity)
    
    /**
     * 批量插入预设
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPresets(presets: List<CropPresetEntity>)
    
    /**
     * 更新预设
     */
    @Update
    suspend fun updatePreset(preset: CropPresetEntity)
    
    /**
     * 删除预设
     */
    @Query("DELETE FROM crop_presets WHERE id = :presetId")
    suspend fun deletePreset(presetId: String)
    
    /**
     * 删除用户自定义预设
     */
    @Query("DELETE FROM crop_presets WHERE isBuiltIn = 0")
    suspend fun deleteUserPresets()
    
    /**
     * 清空所有预设
     */
    @Query("DELETE FROM crop_presets")
    suspend fun clearAllPresets()
    
    /**
     * 获取预设数量
     */
    @Query("SELECT COUNT(*) FROM crop_presets")
    suspend fun getPresetCount(): Int
    
    /**
     * 获取指定分类的预设数量
     */
    @Query("SELECT COUNT(*) FROM crop_presets WHERE category = :category")
    suspend fun getPresetCountByCategory(category: String): Int
    
    /**
     * 搜索预设
     */
    @Query("SELECT * FROM crop_presets WHERE name LIKE '%' || :query || '%' OR description LIKE '%' || :query || '%' ORDER BY sortOrder ASC, name ASC")
    fun searchPresets(query: String): Flow<List<CropPresetEntity>>
    
    /**
     * 更新预设排序
     */
    @Query("UPDATE crop_presets SET sortOrder = :sortOrder WHERE id = :presetId")
    suspend fun updatePresetSortOrder(presetId: String, sortOrder: Int)
    
    /**
     * 检查预设名称是否存在
     */
    @Query("SELECT COUNT(*) FROM crop_presets WHERE name = :name AND id != :excludeId")
    suspend fun isPresetNameExists(name: String, excludeId: String = ""): Int
    
    /**
     * 获取最大排序值
     */
    @Query("SELECT MAX(sortOrder) FROM crop_presets")
    suspend fun getMaxSortOrder(): Int?
}
