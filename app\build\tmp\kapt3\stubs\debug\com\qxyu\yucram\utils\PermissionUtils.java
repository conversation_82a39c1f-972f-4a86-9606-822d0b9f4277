package com.qxyu.yucram.utils;

/**
 * 权限管理工具类
 * 支持Android 14+的权限管理
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0011\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J!\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\u0002\u0010\u0013J\u0011\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\u0002\u0010\u000bJ\u0011\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u0016\u0010\u0018\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0019\u001a\u00020\u0004J\u000e\u0010\u001a\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u001b\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0019\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\t\u00a2\u0006\n\n\u0002\u0010\f\u001a\u0004\b\n\u0010\u000bR\u000e\u0010\r\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/qxyu/yucram/utils/PermissionUtils;", "", "()V", "CAMERA_PERMISSION", "", "LOCATION_COARSE_PERMISSION", "LOCATION_FINE_PERMISSION", "RECORD_AUDIO_PERMISSION", "STORAGE_PERMISSIONS", "", "getSTORAGE_PERMISSIONS", "()[Ljava/lang/String;", "[Ljava/lang/String;", "VIBRATE_PERMISSION", "arePermissionsGranted", "", "context", "Landroid/content/Context;", "permissions", "(Landroid/content/Context;[Ljava/lang/String;)Z", "getCameraPermissions", "getRequiredPermissions", "isCameraPermissionGranted", "isLocationPermissionGranted", "isPermissionGranted", "permission", "isRecordAudioPermissionGranted", "isStoragePermissionGranted", "app_debug"})
public final class PermissionUtils {
    
    /**
     * 相机权限
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CAMERA_PERMISSION = "android.permission.CAMERA";
    
    /**
     * 录音权限（用于视频录制）
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String RECORD_AUDIO_PERMISSION = "android.permission.RECORD_AUDIO";
    
    /**
     * 位置权限（用于GPS水印）
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String LOCATION_FINE_PERMISSION = "android.permission.ACCESS_FINE_LOCATION";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String LOCATION_COARSE_PERMISSION = "android.permission.ACCESS_COARSE_LOCATION";
    
    /**
     * 存储权限（Android 14+）
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String[] STORAGE_PERMISSIONS = null;
    
    /**
     * 震动权限
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String VIBRATE_PERMISSION = "android.permission.VIBRATE";
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.utils.PermissionUtils INSTANCE = null;
    
    private PermissionUtils() {
        super();
    }
    
    /**
     * 存储权限（Android 14+）
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String[] getSTORAGE_PERMISSIONS() {
        return null;
    }
    
    /**
     * 检查单个权限是否已授予
     */
    public final boolean isPermissionGranted(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String permission) {
        return false;
    }
    
    /**
     * 检查多个权限是否已授予
     */
    public final boolean arePermissionsGranted(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    java.lang.String[] permissions) {
        return false;
    }
    
    /**
     * 检查相机权限
     */
    public final boolean isCameraPermissionGranted(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    /**
     * 检查存储权限
     */
    public final boolean isStoragePermissionGranted(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    /**
     * 检查位置权限
     */
    public final boolean isLocationPermissionGranted(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    /**
     * 检查录音权限
     */
    public final boolean isRecordAudioPermissionGranted(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return false;
    }
    
    /**
     * 获取所有必需的权限
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String[] getRequiredPermissions() {
        return null;
    }
    
    /**
     * 获取相机相关的核心权限
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String[] getCameraPermissions() {
        return null;
    }
}