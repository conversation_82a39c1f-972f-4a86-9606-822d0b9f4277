package com.qxyu.yucram.filter;

/**
 * 图像处理管道
 * 实现基于RAW/LOG的专业色彩处理流程
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0082\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0012\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0007\u0018\u0000 H2\u00020\u0001:\u0001HB1\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u0018\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0018\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u0014H\u0002J\u0018\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J \u0010\u0016\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u0011H\u0002J\u001e\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u001a\u001a\u00020\u001bH\u0082@\u00a2\u0006\u0002\u0010\u001cJ\u0010\u0010\u001d\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002J\u0010\u0010\u001e\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002J\u0018\u0010\u001f\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020!H\u0002J\u0010\u0010\"\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002J\u0018\u0010#\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0010\u0010$\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002J\u0018\u0010%\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u0011H\u0002J\u0010\u0010&\u001a\u00020\'2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002J\u0010\u0010(\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002J\u0010\u0010)\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0002J\u001a\u0010*\u001a\u0004\u0018\u00010\u000e2\u0006\u0010+\u001a\u00020\'2\u0006\u0010,\u001a\u00020-H\u0002J\u0012\u0010.\u001a\u0004\u0018\u00010\u000e2\u0006\u0010/\u001a\u00020\'H\u0002J\u0012\u00100\u001a\u0004\u0018\u00010\u000e2\u0006\u0010/\u001a\u00020\'H\u0002J\u0012\u00101\u001a\u0004\u0018\u00010\u000e2\u0006\u00102\u001a\u00020\'H\u0002J\u0012\u00103\u001a\u0004\u0018\u00010\u000e2\u0006\u0010/\u001a\u00020\'H\u0002J\u0006\u00104\u001a\u00020-J\u000e\u00105\u001a\u0002062\u0006\u00107\u001a\u00020-J@\u00108\u001a\b\u0012\u0004\u0012\u00020:092\u0006\u0010+\u001a\u00020\'2\u0006\u0010,\u001a\u00020-2\u0006\u0010;\u001a\u00020<2\n\b\u0002\u0010=\u001a\u0004\u0018\u00010>H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u0010@J\u0016\u0010A\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0002\u0010BJ\u0016\u0010C\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0002\u0010BJ\u0016\u0010D\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0002\u0010BJ&\u0010E\u001a\u00020\u000e2\u0006\u00102\u001a\u00020\'2\u0006\u0010=\u001a\u00020>2\u0006\u0010F\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0002\u0010GR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006I"}, d2 = {"Lcom/qxyu/yucram/filter/ImageProcessingPipeline;", "", "context", "Landroid/content/Context;", "lutProcessor", "Lcom/qxyu/yucram/film/LutProcessor;", "deviceCapabilityDetector", "Lcom/qxyu/yucram/filter/DeviceCapabilityDetector;", "rawToLogProcessor", "Lcom/qxyu/yucram/processing/RawToLogProcessor;", "rawCaptureManager", "Lcom/qxyu/yucram/camera/RawCaptureManager;", "(Landroid/content/Context;Lcom/qxyu/yucram/film/LutProcessor;Lcom/qxyu/yucram/filter/DeviceCapabilityDetector;Lcom/qxyu/yucram/processing/RawToLogProcessor;Lcom/qxyu/yucram/camera/RawCaptureManager;)V", "applyChromaticAberration", "Landroid/graphics/Bitmap;", "bitmap", "intensity", "", "applyColorMatrix", "colorMatrix", "Landroid/graphics/ColorMatrix;", "applyGrain", "applyHighlightsShadows", "highlights", "shadows", "applyLut", "lutFilePath", "", "(Landroid/graphics/Bitmap;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyOppoLogProcessing", "applyOppoLogToLinear", "applyPostProcessing", "params", "Lcom/qxyu/yucram/domain/model/PostProcessingParams;", "applyRawProcessing", "applySharpness", "applySonyLogProcessing", "applyVignette", "bitmapToByteArray", "", "convertJpegToLogBase", "convertRawToLogBase", "decodeImage", "imageData", "sourceFormat", "Lcom/qxyu/yucram/domain/model/ImageSourceFormat;", "decodeLogImage", "logData", "decodeOppoLogImage", "decodeRawImage", "rawData", "decodeSonyLogImage", "getRecommendedSourceFormat", "isSourceFormatSupported", "", "format", "processImage", "Lkotlin/Result;", "Lcom/qxyu/yucram/domain/model/ProcessedImage;", "filterPreset", "Lcom/qxyu/yucram/domain/model/FilterPreset;", "captureResult", "Landroid/hardware/camera2/CaptureResult;", "processImage-yxL6bBk", "([BLcom/qxyu/yucram/domain/model/ImageSourceFormat;Lcom/qxyu/yucram/domain/model/FilterPreset;Landroid/hardware/camera2/CaptureResult;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processJpegImage", "(Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processLogImage", "processRawImage", "processRawToLogProfessional", "fallbackBitmap", "([BLandroid/hardware/camera2/CaptureResult;Landroid/graphics/Bitmap;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class ImageProcessingPipeline {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.film.LutProcessor lutProcessor = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.filter.DeviceCapabilityDetector deviceCapabilityDetector = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.processing.RawToLogProcessor rawToLogProcessor = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.camera.RawCaptureManager rawCaptureManager = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ImageProcessingPipeline";
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.filter.ImageProcessingPipeline.Companion Companion = null;
    
    @javax.inject.Inject()
    public ImageProcessingPipeline(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.film.LutProcessor lutProcessor, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.filter.DeviceCapabilityDetector deviceCapabilityDetector, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.processing.RawToLogProcessor rawToLogProcessor, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.camera.RawCaptureManager rawCaptureManager) {
        super();
    }
    
    /**
     * 检查是否支持指定的源格式
     */
    public final boolean isSourceFormatSupported(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ImageSourceFormat format) {
        return false;
    }
    
    /**
     * 获取推荐的源格式
     */
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ImageSourceFormat getRecommendedSourceFormat() {
        return null;
    }
    
    /**
     * 解码图像
     */
    private final android.graphics.Bitmap decodeImage(byte[] imageData, com.qxyu.yucram.domain.model.ImageSourceFormat sourceFormat) {
        return null;
    }
    
    /**
     * 解码RAW图像
     */
    private final android.graphics.Bitmap decodeRawImage(byte[] rawData) {
        return null;
    }
    
    /**
     * 解码LOG图像
     */
    private final android.graphics.Bitmap decodeLogImage(byte[] logData) {
        return null;
    }
    
    /**
     * 解码OPPO LOG图像
     */
    private final android.graphics.Bitmap decodeOppoLogImage(byte[] logData) {
        return null;
    }
    
    /**
     * 解码Sony LOG图像
     */
    private final android.graphics.Bitmap decodeSonyLogImage(byte[] logData) {
        return null;
    }
    
    /**
     * 专业RAW到LOG处理
     */
    private final java.lang.Object processRawToLogProfessional(byte[] rawData, android.hardware.camera2.CaptureResult captureResult, android.graphics.Bitmap fallbackBitmap, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 处理RAW图像（基础方案）
     */
    private final java.lang.Object processRawImage(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 处理LOG图像
     */
    private final java.lang.Object processLogImage(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 处理JPEG图像
     */
    private final java.lang.Object processJpegImage(android.graphics.Bitmap bitmap, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 应用LUT
     */
    private final java.lang.Object applyLut(android.graphics.Bitmap bitmap, java.lang.String lutFilePath, kotlin.coroutines.Continuation<? super android.graphics.Bitmap> $completion) {
        return null;
    }
    
    /**
     * 应用后处理效果
     */
    private final android.graphics.Bitmap applyPostProcessing(android.graphics.Bitmap bitmap, com.qxyu.yucram.domain.model.PostProcessingParams params) {
        return null;
    }
    
    /**
     * OPPO LOG到线性空间的转换
     */
    private final android.graphics.Bitmap applyOppoLogToLinear(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * RAW处理
     */
    private final android.graphics.Bitmap applyRawProcessing(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * OPPO LOG处理
     */
    private final android.graphics.Bitmap applyOppoLogProcessing(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * Sony LOG处理
     */
    private final android.graphics.Bitmap applySonyLogProcessing(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 将RAW转换为LOG静帧基础
     */
    private final android.graphics.Bitmap convertRawToLogBase(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 将JPEG转换为LOG静帧基础
     */
    private final android.graphics.Bitmap convertJpegToLogBase(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    /**
     * 应用颜色矩阵
     */
    private final android.graphics.Bitmap applyColorMatrix(android.graphics.Bitmap bitmap, android.graphics.ColorMatrix colorMatrix) {
        return null;
    }
    
    /**
     * 应用高光阴影调整
     */
    private final android.graphics.Bitmap applyHighlightsShadows(android.graphics.Bitmap bitmap, float highlights, float shadows) {
        return null;
    }
    
    /**
     * 应用暗角效果
     */
    private final android.graphics.Bitmap applyVignette(android.graphics.Bitmap bitmap, float intensity) {
        return null;
    }
    
    /**
     * 应用色散效果
     */
    private final android.graphics.Bitmap applyChromaticAberration(android.graphics.Bitmap bitmap, float intensity) {
        return null;
    }
    
    /**
     * 应用颗粒效果
     */
    private final android.graphics.Bitmap applyGrain(android.graphics.Bitmap bitmap, float intensity) {
        return null;
    }
    
    /**
     * 应用锐度调整
     */
    private final android.graphics.Bitmap applySharpness(android.graphics.Bitmap bitmap, float intensity) {
        return null;
    }
    
    /**
     * 将Bitmap转换为字节数组
     */
    private final byte[] bitmapToByteArray(android.graphics.Bitmap bitmap) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/qxyu/yucram/filter/ImageProcessingPipeline$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}