package com.qxyu.yucram.domain.repository;

/**
 * 相机Repository接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\bf\u0018\u00002\u00020\u0001J$\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ\u000e\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\f\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\u000bJ\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eH\u00a6@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00060\u0011H&J$\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u00032\u0006\u0010\u0014\u001a\u00020\u000fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0015\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00132\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\b\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0018"}, d2 = {"Lcom/qxyu/yucram/domain/repository/CameraRepository;", "", "capturePhoto", "Lkotlin/Result;", "Lcom/qxyu/yucram/domain/model/Photo;", "settings", "Lcom/qxyu/yucram/domain/model/CameraSettings;", "capturePhoto-gIAlu-s", "(Lcom/qxyu/yucram/domain/model/CameraSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkCameraPermission", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkStoragePermission", "getAvailableCameras", "", "", "getCameraSettings", "Lkotlinx/coroutines/flow/Flow;", "switchCamera", "", "cameraId", "switchCamera-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateCameraSettings", "app_debug"})
public abstract interface CameraRepository {
    
    /**
     * 获取相机设置
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.qxyu.yucram.domain.model.CameraSettings> getCameraSettings();
    
    /**
     * 更新相机设置
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCameraSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.CameraSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 检查相机权限
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object checkCameraPermission(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * 检查存储权限
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object checkStoragePermission(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * 获取可用的相机列表
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAvailableCameras(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.String>> $completion);
}