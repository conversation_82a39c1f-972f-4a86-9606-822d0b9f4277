package com.qxyu.yucram.domain.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\f\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\u001a\f\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a8\u0006\u0003"}, d2 = {"defaultLinearCurve", "", "Lcom/qxyu/yucram/domain/model/CurvePoint;", "app_debug"})
public final class PhotoEditKt {
    
    /**
     * 默认线性曲线
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.qxyu.yucram.domain.model.CurvePoint> defaultLinearCurve() {
        return null;
    }
}