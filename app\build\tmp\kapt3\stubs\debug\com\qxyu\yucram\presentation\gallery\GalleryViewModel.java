package com.qxyu.yucram.presentation.gallery;

/**
 * 图库页面ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J$\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00130\u000e2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000e2\u0006\u0010\u001a\u001a\u00020\u001bH\u0002J\u0006\u0010\u001c\u001a\u00020\u001dJ\u0006\u0010\u001e\u001a\u00020\u001dJ\u0006\u0010\u001f\u001a\u00020\u001dJ\u0006\u0010 \u001a\u00020\u001dJ\u0006\u0010!\u001a\u00020\u001dJ\u000e\u0010\"\u001a\u00020\u001d2\u0006\u0010#\u001a\u00020$J\b\u0010%\u001a\u00020\u001dH\u0002J\u0006\u0010&\u001a\u00020\u001dJ$\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00130\u000e2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000e2\u0006\u0010(\u001a\u00020)H\u0002J\u000e\u0010*\u001a\u00020\u001d2\u0006\u0010#\u001a\u00020$J\u000e\u0010+\u001a\u00020\u001d2\u0006\u0010#\u001a\u00020$J\u0006\u0010,\u001a\u00020\u001dJ\u000e\u0010-\u001a\u00020\u001d2\u0006\u0010.\u001a\u00020/J\u0010\u00100\u001a\u00020\u001d2\b\u0010\u001a\u001a\u0004\u0018\u00010\u001bJ\u000e\u00101\u001a\u00020\u001d2\u0006\u00102\u001a\u00020$J\u000e\u00103\u001a\u00020\u001d2\u0006\u0010(\u001a\u00020)R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\t0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011\u00a8\u00064"}, d2 = {"Lcom/qxyu/yucram/presentation/gallery/GalleryViewModel;", "Landroidx/lifecycle/ViewModel;", "photoRepository", "Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "albumRepository", "Lcom/qxyu/yucram/domain/repository/AlbumRepository;", "(Lcom/qxyu/yucram/domain/repository/PhotoRepository;Lcom/qxyu/yucram/domain/repository/AlbumRepository;)V", "_selectionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/domain/model/PhotoSelectionState;", "_uiState", "Lcom/qxyu/yucram/presentation/gallery/GalleryUiState;", "albums", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/qxyu/yucram/domain/model/Album;", "getAlbums", "()Lkotlinx/coroutines/flow/StateFlow;", "photos", "Lcom/qxyu/yucram/domain/model/Photo;", "getPhotos", "selectionState", "getSelectionState", "uiState", "getUiState", "applyFilter", "filter", "Lcom/qxyu/yucram/domain/model/PhotoFilter;", "clearError", "", "clearMessage", "clearSearch", "clearSelection", "deleteSelectedPhotos", "enterSelectionMode", "photoId", "", "loadInitialData", "selectAllPhotos", "sortPhotos", "sortOrder", "Lcom/qxyu/yucram/domain/model/PhotoSortOrder;", "toggleFavorite", "togglePhotoSelection", "toggleSearchMode", "updateDisplayMode", "displayMode", "Lcom/qxyu/yucram/domain/model/PhotoDisplayMode;", "updateFilter", "updateSearchQuery", "query", "updateSortOrder", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class GalleryViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.PhotoRepository photoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.AlbumRepository albumRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.gallery.GalleryUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.gallery.GalleryUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.PhotoSelectionState> _selectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.PhotoSelectionState> selectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> photos = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> albums = null;
    
    @javax.inject.Inject()
    public GalleryViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.PhotoRepository photoRepository, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.AlbumRepository albumRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.gallery.GalleryUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.PhotoSelectionState> getSelectionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotos() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> getAlbums() {
        return null;
    }
    
    private final void loadInitialData() {
    }
    
    public final void toggleSearchMode() {
    }
    
    public final void updateSearchQuery(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    public final void clearSearch() {
    }
    
    public final void updateSortOrder(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoSortOrder sortOrder) {
    }
    
    private final java.util.List<com.qxyu.yucram.domain.model.Photo> sortPhotos(java.util.List<com.qxyu.yucram.domain.model.Photo> photos, com.qxyu.yucram.domain.model.PhotoSortOrder sortOrder) {
        return null;
    }
    
    public final void updateFilter(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoFilter filter) {
    }
    
    private final java.util.List<com.qxyu.yucram.domain.model.Photo> applyFilter(java.util.List<com.qxyu.yucram.domain.model.Photo> photos, com.qxyu.yucram.domain.model.PhotoFilter filter) {
        return null;
    }
    
    public final void togglePhotoSelection(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void enterSelectionMode(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void selectAllPhotos() {
    }
    
    public final void clearSelection() {
    }
    
    public final void deleteSelectedPhotos() {
    }
    
    public final void toggleFavorite(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void updateDisplayMode(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoDisplayMode displayMode) {
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}