package com.qxyu.yucram.presentation.photodetail

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.photodetail.components.ExifInfoBottomSheet

/**
 * 照片详情页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhotoDetailScreen(
    photoId: String,
    onNavigateBack: () -> Unit = {},
    onNavigateToEdit: (String) -> Unit = {},
    onNavigateToAlbums: (String) -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: PhotoDetailViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val photo by viewModel.photo.collectAsStateWithLifecycle()
    
    var showExifInfo by remember { mutableStateOf(false) }
    var showActions by remember { mutableStateOf(true) }
    
    // 缩放和平移状态
    var scale by remember { mutableStateOf(1f) }
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }
    
    LaunchedEffect(photoId) {
        viewModel.loadPhoto(photoId)
    }
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        when {
            uiState.isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center),
                    color = MaterialTheme.colorScheme.primary
                )
            }
            
            photo != null -> {
                // 主要照片显示
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(photo!!.filePath)
                        .crossfade(true)
                        .build(),
                    contentDescription = photo!!.fileName,
                    contentScale = ContentScale.Fit,
                    modifier = Modifier
                        .fillMaxSize()
                        .graphicsLayer(
                            scaleX = scale,
                            scaleY = scale,
                            translationX = offsetX,
                            translationY = offsetY
                        )
                        .pointerInput(Unit) {
                            detectTransformGestures(
                                onGesture = { _, pan, zoom, _ ->
                                    scale = (scale * zoom).coerceIn(0.5f, 5f)
                                    if (scale > 1f) {
                                        offsetX += pan.x
                                        offsetY += pan.y
                                    } else {
                                        offsetX = 0f
                                        offsetY = 0f
                                    }
                                }
                            )
                        }
                )
                
                // 顶部操作栏
                if (showActions) {
                    PhotoDetailTopBar(
                        photo = photo!!,
                        onNavigateBack = onNavigateBack,
                        onToggleFavorite = { viewModel.toggleFavorite() },
                        onShowExifInfo = { showExifInfo = true },
                        onShare = { viewModel.sharePhoto() },
                        modifier = Modifier.align(Alignment.TopCenter)
                    )
                }
                
                // 底部操作栏
                if (showActions) {
                    PhotoDetailBottomBar(
                        photo = photo!!,
                        onEdit = { onNavigateToEdit(photoId) },
                        onAddToAlbum = { onNavigateToAlbums(photoId) },
                        onDelete = { viewModel.deletePhoto() },
                        modifier = Modifier.align(Alignment.BottomCenter)
                    )
                }
                
                // 点击切换UI显示
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .pointerInput(Unit) {
                            detectTransformGestures { _, _, _, _ ->
                                showActions = !showActions
                            }
                        }
                )
            }
            
            uiState.error != null -> {
                PhotoDetailError(
                    error = uiState.error!!,
                    onRetry = { viewModel.loadPhoto(photoId) },
                    onNavigateBack = onNavigateBack,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
        
        // EXIF信息底部表单
        if (showExifInfo && photo != null) {
            ExifInfoBottomSheet(
                photo = photo!!,
                onDismiss = { showExifInfo = false }
            )
        }
    }
}

/**
 * 照片详情顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhotoDetailTopBar(
    photo: Photo,
    onNavigateBack: () -> Unit,
    onToggleFavorite: () -> Unit,
    onShowExifInfo: () -> Unit,
    onShare: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Column {
                Text(
                    text = photo.fileName,
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White
                )
                Text(
                    text = formatFileSize(photo.fileSize),
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
        },
        actions = {
            IconButton(onClick = onToggleFavorite) {
                Icon(
                    imageVector = if (photo.isFavorite) Icons.Filled.Favorite else Icons.Filled.FavoriteBorder,
                    contentDescription = if (photo.isFavorite) "取消收藏" else "收藏",
                    tint = if (photo.isFavorite) Color.Red else Color.White
                )
            }
            IconButton(onClick = onShowExifInfo) {
                Icon(
                    imageVector = Icons.Filled.Info,
                    contentDescription = "详细信息",
                    tint = Color.White
                )
            }
            IconButton(onClick = onShare) {
                Icon(
                    imageVector = Icons.Filled.Share,
                    contentDescription = "分享",
                    tint = Color.White
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        modifier = modifier
    )
}

/**
 * 照片详情底部栏
 */
@Composable
fun PhotoDetailBottomBar(
    photo: Photo,
    onEdit: () -> Unit,
    onAddToAlbum: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        color = Color.Black.copy(alpha = 0.7f),
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 编辑按钮
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                IconButton(
                    onClick = onEdit,
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            RoundedCornerShape(24.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Edit,
                        contentDescription = "编辑",
                        tint = Color.White
                    )
                }
                Text(
                    text = "编辑",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            // 添加到相册
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                IconButton(
                    onClick = onAddToAlbum,
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            MaterialTheme.colorScheme.secondary,
                            RoundedCornerShape(24.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Filled.LibraryAdd,
                        contentDescription = "添加到相册",
                        tint = Color.White
                    )
                }
                Text(
                    text = "相册",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
            
            // 删除按钮
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                IconButton(
                    onClick = onDelete,
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            MaterialTheme.colorScheme.error,
                            RoundedCornerShape(24.dp)
                        )
                ) {
                    Icon(
                        imageVector = Icons.Filled.Delete,
                        contentDescription = "删除",
                        tint = Color.White
                    )
                }
                Text(
                    text = "删除",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }
        }
    }
}

/**
 * 错误状态显示
 */
@Composable
fun PhotoDetailError(
    error: String,
    onRetry: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Filled.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "加载失败",
            style = MaterialTheme.typography.headlineSmall,
            color = Color.White
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                onClick = onNavigateBack,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.White
                )
            ) {
                Text("返回")
            }
            
            Button(
                onClick = onRetry
            ) {
                Text("重试")
            }
        }
    }
}

/**
 * 格式化文件大小
 */
private fun formatFileSize(bytes: Long): String {
    val kb = bytes / 1024.0
    val mb = kb / 1024.0
    val gb = mb / 1024.0
    
    return when {
        gb >= 1 -> String.format("%.1f GB", gb)
        mb >= 1 -> String.format("%.1f MB", mb)
        kb >= 1 -> String.format("%.1f KB", kb)
        else -> "$bytes B"
    }
}
