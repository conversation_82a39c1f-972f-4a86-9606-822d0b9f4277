// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.photodetail;

import com.qxyu.yucram.domain.repository.PhotoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PhotoDetailViewModel_Factory implements Factory<PhotoDetailViewModel> {
  private final Provider<PhotoRepository> photoRepositoryProvider;

  public PhotoDetailViewModel_Factory(Provider<PhotoRepository> photoRepositoryProvider) {
    this.photoRepositoryProvider = photoRepositoryProvider;
  }

  @Override
  public PhotoDetailViewModel get() {
    return newInstance(photoRepositoryProvider.get());
  }

  public static PhotoDetailViewModel_Factory create(
      Provider<PhotoRepository> photoRepositoryProvider) {
    return new PhotoDetailViewModel_Factory(photoRepositoryProvider);
  }

  public static PhotoDetailViewModel newInstance(PhotoRepository photoRepository) {
    return new PhotoDetailViewModel(photoRepository);
  }
}
