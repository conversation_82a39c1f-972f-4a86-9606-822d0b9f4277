package com.qxyu.yucram.domain.model;

/**
 * 水印分类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/qxyu/yucram/domain/model/WatermarkCategory;", "", "(Ljava/lang/String;I)V", "TEXT", "LOG<PERSON>", "SIGNATURE", "INFO", "ARTISTIC", "BRAND", "app_debug"})
public enum WatermarkCategory {
    /*public static final*/ TEXT /* = new TEXT() */,
    /*public static final*/ LOGO /* = new LOGO() */,
    /*public static final*/ SIGNATURE /* = new SIGNATURE() */,
    /*public static final*/ INFO /* = new INFO() */,
    /*public static final*/ ARTISTIC /* = new ARTISTIC() */,
    /*public static final*/ BRAND /* = new BRAND() */;
    
    WatermarkCategory() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.WatermarkCategory> getEntries() {
        return null;
    }
}