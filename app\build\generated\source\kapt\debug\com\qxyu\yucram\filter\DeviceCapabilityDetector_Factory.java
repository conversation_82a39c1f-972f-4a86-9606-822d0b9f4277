// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.filter;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DeviceCapabilityDetector_Factory implements Factory<DeviceCapabilityDetector> {
  private final Provider<Context> contextProvider;

  public DeviceCapabilityDetector_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DeviceCapabilityDetector get() {
    return newInstance(contextProvider.get());
  }

  public static DeviceCapabilityDetector_Factory create(Provider<Context> contextProvider) {
    return new DeviceCapabilityDetector_Factory(contextProvider);
  }

  public static DeviceCapabilityDetector newInstance(Context context) {
    return new DeviceCapabilityDetector(context);
  }
}
