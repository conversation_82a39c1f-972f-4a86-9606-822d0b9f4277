package com.qxyu.yucram.presentation.lutfilter.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000T\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a@\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a^\u0010\u000f\u001a\u00020\u00062\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u00012\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00060\u00142\u0014\b\u0002\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00060\u00142\b\b\u0002\u0010\u0016\u001a\u00020\u00172\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a@\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\u000e\b\u0002\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001aT\u0010\u001a\u001a\u00020\u00062\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\b\u0010\u0012\u001a\u0004\u0018\u00010\u00012\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00060\u00142\u0014\b\u0002\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0001\u0012\u0004\u0012\u00020\u00060\u00142\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a(\u0010\u001b\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00060\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000eH\u0007\u001a\u0015\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002\u00a2\u0006\u0002\u0010 \u001a\u0010\u0010!\u001a\u00020\"2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002\"\u0018\u0010\u0000\u001a\u00020\u0001*\u00020\u00028BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006#"}, d2 = {"NONE", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "error/NonExistentClass", "getNONE", "(Lerror/NonExistentClass;)Lcom/qxyu/yucram/domain/model/LUTFilter;", "LUTFilterDetailCard", "", "filter", "isSelected", "", "onClick", "Lkotlin/Function0;", "onInfoClick", "modifier", "Landroidx/compose/ui/Modifier;", "LUTFilterGrid", "filters", "", "selectedFilter", "onFilterSelected", "Lkotlin/Function1;", "onFilterLongPress", "columns", "", "LUTFilterItem", "onLongClick", "LUTFilterList", "NoFilterItem", "getFilterCategoryColor", "Landroidx/compose/ui/graphics/Color;", "category", "Lcom/qxyu/yucram/domain/model/LUTCategory;", "(Lcom/qxyu/yucram/domain/model/LUTCategory;)J", "getFilterCategoryIcon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "app_debug"})
public final class LUTFilterListKt {
    
    /**
     * LUT滤镜列表组件
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTFilterList(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LUTFilter> filters, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter selectedFilter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTFilter, kotlin.Unit> onFilterSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTFilter, kotlin.Unit> onFilterLongPress, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 无滤镜选项
     */
    @androidx.compose.runtime.Composable()
    public static final void NoFilterItem(boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * LUT滤镜项
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTFilterItem(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onLongClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 滤镜网格视图
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTFilterGrid(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LUTFilter> filters, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter selectedFilter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTFilter, kotlin.Unit> onFilterSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.LUTFilter, kotlin.Unit> onFilterLongPress, int columns, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 滤镜详情卡片
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTFilterDetailCard(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onInfoClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 获取滤镜分类颜色
     */
    private static final long getFilterCategoryColor(com.qxyu.yucram.domain.model.LUTCategory category) {
        return 0L;
    }
    
    /**
     * 获取滤镜分类图标
     */
    private static final androidx.compose.ui.graphics.vector.ImageVector getFilterCategoryIcon(com.qxyu.yucram.domain.model.LUTCategory category) {
        return null;
    }
    
    private static final com.qxyu.yucram.domain.model.LUTFilter getNONE(error.NonExistentClass $this$NONE) {
        return null;
    }
}