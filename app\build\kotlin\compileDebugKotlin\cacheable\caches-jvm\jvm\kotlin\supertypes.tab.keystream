com.qxyu.yucram.MainActivity!com.qxyu.yucram.YucramApplication$com.qxyu.yucram.camera.CameraManager"com.qxyu.yucram.camera.CameraState)com.qxyu.yucram.data.local.YucramDatabase4com.qxyu.yucram.data.repository.CameraRepositoryImpl3com.qxyu.yucram.data.repository.PhotoRepositoryImpl6com.qxyu.yucram.data.repository.SettingsRepositoryImpl(com.qxyu.yucram.domain.model.AspectRatio)com.qxyu.yucram.domain.model.ImageQuality(com.qxyu.yucram.domain.model.ImageFormat&com.qxyu.yucram.domain.model.FlashMode&com.qxyu.yucram.domain.model.FocusMode)com.qxyu.yucram.domain.model.WhiteBalance5com.qxyu.yucram.domain.model.FilterPreset.$serializer=com.qxyu.yucram.domain.model.PostProcessingParams.$serializer.com.qxyu.yucram.domain.model.ImageSourceFormat*com.qxyu.yucram.domain.model.LogCapability0com.qxyu.yucram.domain.model.LutData.$serializer0com.qxyu.yucram.domain.model.LutInfo.$serializer&com.qxyu.yucram.domain.model.LutFormat<com.qxyu.yucram.domain.model.RawProcessingParams.$serializer<com.qxyu.yucram.domain.model.LogProcessingParams.$serializer;com.qxyu.yucram.domain.model.WhiteBalanceParams.$serializer7com.qxyu.yucram.domain.model.ExposureParams.$serializer:com.qxyu.yucram.domain.model.ToneMappingParams.$serializer;com.qxyu.yucram.domain.model.ColorGradingParams.$serializer9com.qxyu.yucram.domain.model.ColorWheelParams.$serializer=com.qxyu.yucram.domain.model.NoiseReductionParams.$serializer9com.qxyu.yucram.domain.model.SharpeningParams.$serializer'com.qxyu.yucram.domain.model.ColorSpace%com.qxyu.yucram.domain.model.LogCurve.com.qxyu.yucram.domain.model.ToneMappingMethod1com.qxyu.yucram.domain.model.ProcessedImageFormat6com.qxyu.yucram.domain.model.ImageMetadata.$serializer*com.qxyu.yucram.navigation.MainDestination5com.qxyu.yucram.presentation.animation.SlideDirection4com.qxyu.yucram.presentation.animation.AnimationType3com.qxyu.yucram.presentation.camera.CameraViewModel<com.qxyu.yucram.presentation.filter.FilterSelectionViewModel;<EMAIL>.FilterSystemModule_ProvideLutProcessorFactory6com.qxyu.yucram.filter.ImageProcessingPipeline_Factory-com.qxyu.yucram.filter.LutFileManager_FactoryDcom.qxyu.yucram.presentation.filter.FilterSelectionViewModel_Factoryacom.qxyu.yucram.presentation.filter.FilterSelectionViewModel_HiltModules_KeyModule_ProvideFactory4com.qxyu.yucram.processing.RawToLogProcessor_Factory8com.qxyu.yucram.di.AppModule_ProvideCameraManagerFactoryEcom.qxyu.yucram.di.FilterSystemModule_ProvideRawCaptureManagerFactory,com.qxyu.yucram.camera.CameraManager_FactoryEcom.qxyu.yucram.di.FilterSystemModule_ProvideRawToLogProcessorFactory0com.qxyu.yucram.camera.RawCaptureManager_Factory3com.qxyu.yucram.data.repository.AlbumRepositoryImpl&com.qxyu.yucram.domain.model.AlbumType'com.qxyu.yucram.domain.model.AlbumColor9com.qxyu.yucram.domain.model.AlbumOperationResult.Success7com.qxyu.yucram.domain.model.AlbumOperationResult.Error>com.qxyu.yucram.domain.model.AlbumOperationResult.AlbumCreated>com.qxyu.yucram.domain.model.AlbumOperationResult.AlbumUpdated>com.qxyu.yucram.domain.model.AlbumOperationResult.AlbumDeleted3com.qxyu.yucram.domain.model.AlbumLoadState.Loading3com.qxyu.yucram.domain.model.AlbumLoadState.Success1com.qxyu.yucram.domain.model.AlbumLoadState.Error1com.qxyu.yucram.domain.model.AlbumLoadState.Empty/<EMAIL>=com.qxyu.yucram.presentation.gallery.GalleryViewModel_Factory,com.qxyu.yucram.data.local.dao.PhotoDao_ImplCcom.qxyu.yucram.di.FilterSystemModule_ProvidePhotoRepositoryFactory;com.qxyu.yucram.data.repository.AlbumRepositoryImpl_FactoryCcom.qxyu.yucram.di.FilterSystemModule_ProvideAlbumRepositoryFactoryZcom.qxyu.yucram.presentation.gallery.GalleryViewModel_HiltModules_KeyModule_ProvideFactory/com.qxyu.yucram.domain.model.SmartAlbumOperator&com.qxyu.yucram.domain.model.CropRatio0com.qxyu.yucram.domain.model.LocalAdjustmentType6com.qxyu.yucram.domain.model.AdjustmentArea.RadialArea8com.qxyu.yucram.domain.model.AdjustmentArea.GradientArea4com.qxyu.yucram.domain.model.AdjustmentArea.MaskArea&com.qxyu.yucram.domain.model.BlendMode%com.qxyu.yucram.domain.model.EditTool+com.qxyu.yucram.domain.model.PresetCategory0com.qxyu.yucram.presentation.album.AlbumViewMode5com.qxyu.yucram.presentation.album.AlbumListViewModel7com.qxyu.yucram.presentation.album.AlbumDetailViewModel6com.qxyu.yucram.presentation.album.SmartAlbumViewModel=com.qxyu.yucram.presentation.photodetail.PhotoDetailViewModel9com.qxyu.yucram.presentation.photoedit.PhotoEditViewModel;com.qxyu.yucram.presentation.photoedit.components.CurveType:com.qxyu.yucram.presentation.photoedit.components.HSLColor=com.qxyu.yucram.presentation.photoedit.components.HSLPropertyMcom.qxyu.yucram.presentation.photoedit.components.ToolInteraction.CropChangedVcom.qxyu.yucram.presentation.photoedit.components.ToolInteraction.LocalAdjustmentAddedYcom.qxyu.yucram.presentation.photoedit.components.ToolInteraction.LocalAdjustmentModifiedXcom.qxyu.yucram.presentation.photoedit.components.ToolInteraction.LocalAdjustmentRemoved?com.qxyu.yucram.presentation.album.AlbumDetailViewModel_Factory\com.qxyu.yucram.presentation.album.AlbumDetailViewModel_HiltModules_KeyModule_ProvideFactorybcom.qxyu.yucram.presentation.photodetail.PhotoDetailViewModel_HiltModules_KeyModule_ProvideFactoryEcom.qxyu.yucram.presentation.photodetail.PhotoDetailViewModel_Factory[com.qxyu.yucram.presentation.album.SmartAlbumViewModel_HiltModules_KeyModule_ProvideFactory^com.qxyu.yucram.presentation.photoedit.PhotoEditViewModel_HiltModules_KeyModule_ProvideFactoryAcom.qxyu.yucram.presentation.photoedit.PhotoEditViewModel_FactoryZcom.qxyu.yucram.presentation.album.AlbumListViewModel_HiltModules_KeyModule_ProvideFactory=com.qxyu.yucram.presentation.album.AlbumListViewModel_Factory>com.qxyu.yucram.presentation.album.SmartAlbumViewModel_Factory'com.qxyu.yucram.domain.model.BorderType(com.qxyu.yucram.domain.model.BorderStyle*com.qxyu.yucram.domain.model.BorderPattern,com.qxyu.yucram.domain.model.DecorativeStyle*com.qxyu.yucram.domain.model.WatermarkType%com.qxyu.yucram.domain.model.LogoType.com.qxyu.yucram.domain.model.WatermarkPosition/com.qxyu.yucram.domain.model.WatermarkBlendMode)com.qxyu.yucram.domain.model.ExportFormat+com.qxyu.yucram.domain.model.BorderCategory.com.qxyu.yucram.domain.model.WatermarkCategory;<EMAIL>.BorderWatermarkViewModel_Factoryfcom.qxyu.yucram.presentation.borderwater.BorderWatermarkViewModel_HiltModules_KeyModule_ProvideFactory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           