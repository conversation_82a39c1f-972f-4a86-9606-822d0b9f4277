package com.qxyu.yucram.presentation.photodetail;

/**
 * 照片详情页ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0010\u001a\u00020\u0011J\u0006\u0010\u0012\u001a\u00020\u0011J\u0006\u0010\u0013\u001a\u00020\u0011J\u000e\u0010\u0014\u001a\u00020\u00112\u0006\u0010\u0015\u001a\u00020\u0016J\u0006\u0010\u0017\u001a\u00020\u0011J\u0006\u0010\u0018\u001a\u00020\u0011R\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u0019"}, d2 = {"Lcom/qxyu/yucram/presentation/photodetail/PhotoDetailViewModel;", "Landroidx/lifecycle/ViewModel;", "photoRepository", "Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "(Lcom/qxyu/yucram/domain/repository/PhotoRepository;)V", "_photo", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/domain/model/Photo;", "_uiState", "Lcom/qxyu/yucram/presentation/photodetail/PhotoDetailUiState;", "photo", "Lkotlinx/coroutines/flow/StateFlow;", "getPhoto", "()Lkotlinx/coroutines/flow/StateFlow;", "uiState", "getUiState", "clearError", "", "clearMessage", "deletePhoto", "loadPhoto", "photoId", "", "sharePhoto", "toggleFavorite", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class PhotoDetailViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.PhotoRepository photoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.photodetail.PhotoDetailUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.photodetail.PhotoDetailUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.Photo> _photo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> photo = null;
    
    @javax.inject.Inject()
    public PhotoDetailViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.PhotoRepository photoRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.photodetail.PhotoDetailUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> getPhoto() {
        return null;
    }
    
    public final void loadPhoto(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void toggleFavorite() {
    }
    
    public final void deletePhoto() {
    }
    
    public final void sharePhoto() {
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}