package com.qxyu.yucram.presentation.lutfilter.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*

/**
 * CUBE滤镜展示组件
 * 专门展示您上传的CUBE文件滤镜
 */
@Composable
fun CubeFilterShowcase(
    filters: List<LutFilter>,
    selectedFilter: LutFilter?,
    onFilterSelected: (LutFilter) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.9f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        item {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "专业CUBE滤镜",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                    Text(
                        text = "${filters.size}个专业滤镜可用",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
                
                Icon(
                    imageVector = Icons.Filled.AutoAwesome,
                    contentDescription = "专业滤镜",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(32.dp)
                )
            }
        }
        
        // 按分类分组显示滤镜
        val filtersByCategory = filters.groupBy { it.category }
        
        filtersByCategory.forEach { (category, categoryFilters) ->
            item {
                CubeFilterCategorySection(
                    category = category,
                    filters = categoryFilters,
                    selectedFilter = selectedFilter,
                    onFilterSelected = onFilterSelected
                )
            }
        }
        
        // 滤镜统计信息
        item {
            CubeFilterStats(filters = filters)
        }
    }
}

/**
 * CUBE滤镜分类区域
 */
@Composable
fun CubeFilterCategorySection(
    category: LutCategory,
    filters: List<LutFilter>,
    selectedFilter: LutFilter?,
    onFilterSelected: (LutFilter) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 分类标题
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                imageVector = getCategoryIcon(category),
                contentDescription = category.displayName,
                tint = getCategoryColor(category),
                modifier = Modifier.size(20.dp)
            )
            
            Text(
                text = category.displayName,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = Color.White
            )
            
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = getCategoryColor(category).copy(alpha = 0.2f)
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = "${filters.size}",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold,
                    color = getCategoryColor(category),
                    modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = category.description,
            style = MaterialTheme.typography.bodySmall,
            color = Color.White.copy(alpha = 0.7f),
            modifier = Modifier.padding(start = 28.dp)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // 滤镜列表
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(filters) { filter ->
                CubeFilterCard(
                    filter = filter,
                    isSelected = selectedFilter?.id == filter.id,
                    onClick = { onFilterSelected(filter) }
                )
            }
        }
    }
}

/**
 * CUBE滤镜卡片
 */
@Composable
fun CubeFilterCard(
    filter: LutFilter,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(140.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
            else Color.White.copy(alpha = 0.05f)
        ),
        border = CardDefaults.outlinedCardBorder().copy(
            brush = if (isSelected) androidx.compose.ui.graphics.SolidColor(MaterialTheme.colorScheme.primary)
            else androidx.compose.ui.graphics.SolidColor(Color.White.copy(alpha = 0.2f))
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 滤镜预览区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(getCategoryColor(filter.category).copy(alpha = 0.3f)),
                contentAlignment = Alignment.Center
            ) {
                if (filter.thumbnailPath != null) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(filter.thumbnailPath)
                            .crossfade(true)
                            .build(),
                        contentDescription = filter.displayName,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize()
                    )
                } else {
                    // 显示分类图标作为占位符
                    Icon(
                        imageVector = getCategoryIcon(filter.category),
                        contentDescription = filter.displayName,
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
                
                // 选中指示器
                if (isSelected) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(4.dp)
                            .size(20.dp)
                            .background(
                                MaterialTheme.colorScheme.primary,
                                RoundedCornerShape(10.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Check,
                            contentDescription = "已选中",
                            tint = Color.White,
                            modifier = Modifier.size(12.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 滤镜名称
            Text(
                text = filter.displayName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = if (isSelected) MaterialTheme.colorScheme.primary else Color.White,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            // 滤镜描述
            Text(
                text = filter.description,
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.7f),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 作者信息
            if (filter.author != null) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Person,
                        contentDescription = "作者",
                        tint = Color.White.copy(alpha = 0.5f),
                        modifier = Modifier.size(12.dp)
                    )
                    Text(
                        text = filter.author!!,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White.copy(alpha = 0.5f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }
}

/**
 * CUBE滤镜统计信息
 */
@Composable
fun CubeFilterStats(
    filters: List<LutFilter>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.05f)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "滤镜库统计",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            val categoryStats = filters.groupBy { it.category }
                .mapValues { it.value.size }
                .toList()
                .sortedByDescending { it.second }
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(categoryStats) { (category, count) ->
                    StatCard(
                        category = category,
                        count = count
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "总计: ${filters.size} 个滤镜",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = "支持CUBE格式",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 统计卡片
 */
@Composable
fun StatCard(
    category: LutCategory,
    count: Int,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = getCategoryColor(category).copy(alpha = 0.2f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = getCategoryIcon(category),
                contentDescription = category.displayName,
                tint = getCategoryColor(category),
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = "$count",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = getCategoryColor(category)
            )
            
            Text(
                text = category.displayName,
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.8f)
            )
        }
    }
}

/**
 * 获取分类图标
 */
private fun getCategoryIcon(category: LutCategory): androidx.compose.ui.graphics.vector.ImageVector {
    return when (category) {
        LutCategory.CINEMATIC -> Icons.Filled.Movie
        LutCategory.VINTAGE -> Icons.Filled.CameraRoll
        LutCategory.PORTRAIT -> Icons.Filled.Person
        LutCategory.LANDSCAPE -> Icons.Filled.Landscape
        LutCategory.STREET -> Icons.Filled.LocationCity
        LutCategory.FASHION -> Icons.Filled.Style
        LutCategory.MOODY -> Icons.Filled.Mood
        LutCategory.BRIGHT -> Icons.Filled.WbSunny
        LutCategory.DARK -> Icons.Filled.Brightness2
        LutCategory.WARM -> Icons.Filled.Whatshot
        LutCategory.COOL -> Icons.Filled.AcUnit
        LutCategory.MONOCHROME -> Icons.Filled.Contrast
        LutCategory.CREATIVE -> Icons.Filled.AutoAwesome
        LutCategory.PROFESSIONAL -> Icons.Filled.WorkspacePremium
        LutCategory.USER_CUSTOM -> Icons.Filled.PersonalVideo
    }
}

/**
 * 获取分类颜色
 */
private fun getCategoryColor(category: LutCategory): Color {
    return when (category) {
        LutCategory.CINEMATIC -> Color(0xFF1E90FF)
        LutCategory.VINTAGE -> Color(0xFFD2691E)
        LutCategory.PORTRAIT -> Color(0xFFFF69B4)
        LutCategory.LANDSCAPE -> Color(0xFF32CD32)
        LutCategory.STREET -> Color(0xFF696969)
        LutCategory.FASHION -> Color(0xFF9370DB)
        LutCategory.MOODY -> Color(0xFF8B0000)
        LutCategory.BRIGHT -> Color(0xFFFFD700)
        LutCategory.DARK -> Color(0xFF2F4F4F)
        LutCategory.WARM -> Color(0xFFFF4500)
        LutCategory.COOL -> Color(0xFF00CED1)
        LutCategory.MONOCHROME -> Color(0xFF808080)
        LutCategory.CREATIVE -> Color(0xFFFF1493)
        LutCategory.PROFESSIONAL -> Color(0xFF4169E1)
        LutCategory.USER_CUSTOM -> Color(0xFF20B2AA)
    }
}
