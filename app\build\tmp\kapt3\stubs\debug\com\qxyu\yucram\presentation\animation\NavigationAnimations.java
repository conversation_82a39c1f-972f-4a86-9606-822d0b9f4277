package com.qxyu.yucram.presentation.animation;

/**
 * 导航动画配置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bJ\u0010\u0010\u000b\u001a\u00020\t2\b\b\u0002\u0010\f\u001a\u00020\u0004J\u0010\u0010\r\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\u0004J\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bJ\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bJ0\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\b2\b\b\u0002\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u0012J\u001a\u0010\u0014\u001a\u00020\t2\b\b\u0002\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\u0011\u001a\u00020\u0012J\u001a\u0010\u0015\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\u00042\b\b\u0002\u0010\u0013\u001a\u00020\u0012J\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\bJ&\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\n0\b2\b\b\u0002\u0010\u0018\u001a\u00020\u00192\b\b\u0002\u0010\f\u001a\u00020\u0004J&\u0010\u001a\u001a\u00020\t2\b\b\u0002\u0010\f\u001a\u00020\u00042\u0014\b\u0002\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u001cJ&\u0010\u001d\u001a\u00020\t2\b\b\u0002\u0010\f\u001a\u00020\u00042\u0014\b\u0002\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u001cJ&\u0010\u001f\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\u00042\u0014\b\u0002\u0010 \u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u001cJ&\u0010!\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\u00042\u0014\b\u0002\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00040\u001cJ \u0010#\u001a\b\u0012\u0004\u0012\u00020\u00120$2\b\b\u0002\u0010%\u001a\u00020\u00122\b\b\u0002\u0010&\u001a\u00020\u0012R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/qxyu/yucram/presentation/animation/NavigationAnimations;", "", "()V", "ANIMATION_DURATION", "", "FAST_ANIMATION_DURATION", "SLOW_ANIMATION_DURATION", "cameraPageAnimation", "Lkotlin/Pair;", "Landroidx/compose/animation/EnterTransition;", "Landroidx/compose/animation/ExitTransition;", "fadeIn", "duration", "fadeOut", "galleryPageAnimation", "photoDetailAnimation", "scaleAndFade", "initialScale", "", "targetScale", "scaleIn", "scaleOut", "settingsPageAnimation", "slideAndFade", "direction", "Lcom/qxyu/yucram/presentation/animation/SlideDirection;", "slideInHorizontally", "initialOffsetX", "Lkotlin/Function1;", "slideInVertically", "initialOffsetY", "slideOutHorizontally", "targetOffsetX", "slideOutVertically", "targetOffsetY", "springAnimation", "Landroidx/compose/animation/core/AnimationSpec;", "dampingRatio", "stiffness", "app_debug"})
public final class NavigationAnimations {
    public static final int ANIMATION_DURATION = 300;
    public static final int FAST_ANIMATION_DURATION = 200;
    public static final int SLOW_ANIMATION_DURATION = 500;
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.presentation.animation.NavigationAnimations INSTANCE = null;
    
    private NavigationAnimations() {
        super();
    }
    
    /**
     * 水平滑动进入动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.EnterTransition slideInHorizontally(int duration, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, java.lang.Integer> initialOffsetX) {
        return null;
    }
    
    /**
     * 水平滑动退出动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.ExitTransition slideOutHorizontally(int duration, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, java.lang.Integer> targetOffsetX) {
        return null;
    }
    
    /**
     * 垂直滑动进入动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.EnterTransition slideInVertically(int duration, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, java.lang.Integer> initialOffsetY) {
        return null;
    }
    
    /**
     * 垂直滑动退出动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.ExitTransition slideOutVertically(int duration, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, java.lang.Integer> targetOffsetY) {
        return null;
    }
    
    /**
     * 缩放进入动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.EnterTransition scaleIn(int duration, float initialScale) {
        return null;
    }
    
    /**
     * 缩放退出动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.ExitTransition scaleOut(int duration, float targetScale) {
        return null;
    }
    
    /**
     * 淡入动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.EnterTransition fadeIn(int duration) {
        return null;
    }
    
    /**
     * 淡出动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.ExitTransition fadeOut(int duration) {
        return null;
    }
    
    /**
     * 组合动画：滑动 + 淡入淡出
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<androidx.compose.animation.EnterTransition, androidx.compose.animation.ExitTransition> slideAndFade(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.animation.SlideDirection direction, int duration) {
        return null;
    }
    
    /**
     * 组合动画：缩放 + 淡入淡出
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<androidx.compose.animation.EnterTransition, androidx.compose.animation.ExitTransition> scaleAndFade(int duration, float initialScale, float targetScale) {
        return null;
    }
    
    /**
     * 弹性动画
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.animation.core.AnimationSpec<java.lang.Float> springAnimation(float dampingRatio, float stiffness) {
        return null;
    }
    
    /**
     * 相机页面专用动画
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<androidx.compose.animation.EnterTransition, androidx.compose.animation.ExitTransition> cameraPageAnimation() {
        return null;
    }
    
    /**
     * 图库页面专用动画
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<androidx.compose.animation.EnterTransition, androidx.compose.animation.ExitTransition> galleryPageAnimation() {
        return null;
    }
    
    /**
     * 设置页面专用动画
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<androidx.compose.animation.EnterTransition, androidx.compose.animation.ExitTransition> settingsPageAnimation() {
        return null;
    }
    
    /**
     * 照片详情页面动画
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<androidx.compose.animation.EnterTransition, androidx.compose.animation.ExitTransition> photoDetailAnimation() {
        return null;
    }
}