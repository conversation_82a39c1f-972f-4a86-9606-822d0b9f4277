package com.qxyu.yucram

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

import androidx.compose.ui.text.font.FontWeight
import androidx.hilt.navigation.compose.hiltViewModel
import com.qxyu.yucram.camera.CameraManager
import com.qxyu.yucram.presentation.camera.CameraScreen
import com.qxyu.yucram.presentation.permission.PermissionScreen
import com.qxyu.yucram.presentation.permission.PermissionViewModel
import com.qxyu.yucram.utils.PermissionUtils
import javax.inject.Inject

/**
 * Yucram应用程序主组件
 */
@Composable
fun YucramApp() {
    val permissionViewModel: PermissionViewModel = hiltViewModel()
    val permissionState by permissionViewModel.permissionState.collectAsState()

    // 检查核心权限是否已授予
    if (permissionState.allCorePermissionsGranted) {
        // 权限已授予，显示主界面
        MainCameraScreen()
    } else {
        // 权限未授予，显示权限请求界面
        PermissionScreen(
            onPermissionsGranted = {
                permissionViewModel.onPermissionResult()
            }
        )
    }
}

/**
 * 主相机界面
 */
@Composable
private fun MainCameraScreen() {
    // 使用Hilt注入的CameraManager
    CameraScreen()
}
