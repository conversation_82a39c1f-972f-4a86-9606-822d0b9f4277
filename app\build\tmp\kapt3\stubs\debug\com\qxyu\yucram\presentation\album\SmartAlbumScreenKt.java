package com.qxyu.yucram.presentation.album;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000Z\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\u001aJ\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00032\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a6\u0010\n\u001a\u00020\u00012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a<\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00142\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a.\u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0018\u001a\u00020\u00192\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a6\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u00192\u0006\u0010\u001d\u001a\u00020\u00032\u0012\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001a\\\u0010\u001f\u001a\u00020\u00012\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00140\f2\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\u0018\u0010\"\u001a\u0014\u0012\u0004\u0012\u00020$\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010#2\u0012\u0010%\u001a\u000e\u0012\u0004\u0012\u00020$\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u001aB\u0010&\u001a\u00020\u00012\u000e\b\u0002\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\u0014\b\u0002\u0010(\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010)\u001a\u00020*H\u0007\u001a6\u0010+\u001a\u00020\u00012\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00010\u00112\u0006\u0010-\u001a\u00020\u000f2\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00a8\u0006."}, d2 = {"SmartAlbumBasicInfo", "", "albumName", "", "onAlbumNameChanged", "Lkotlin/Function1;", "albumDescription", "onAlbumDescriptionChanged", "modifier", "Landroidx/compose/ui/Modifier;", "SmartAlbumPreview", "photos", "", "Lcom/qxyu/yucram/domain/model/Photo;", "isLoading", "", "onRefreshPreview", "Lkotlin/Function0;", "SmartAlbumRuleItem", "rule", "Lcom/qxyu/yucram/domain/model/SmartAlbumRule;", "onUpdate", "onRemove", "SmartAlbumRuleTypeSelector", "selectedType", "Lcom/qxyu/yucram/domain/model/SmartAlbumRuleType;", "onTypeSelected", "SmartAlbumRuleValueInput", "ruleType", "value", "onValueChanged", "SmartAlbumRulesSection", "rules", "onAddRule", "onUpdateRule", "Lkotlin/Function2;", "", "onRemoveRule", "SmartAlbumScreen", "onNavigateBack", "onAlbumCreated", "viewModel", "Lcom/qxyu/yucram/presentation/album/SmartAlbumViewModel;", "SmartAlbumTopBar", "onSave", "canSave", "app_debug"})
public final class SmartAlbumScreenKt {
    
    /**
     * 智能相册创建界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAlbumCreated, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.album.SmartAlbumViewModel viewModel) {
    }
    
    /**
     * 智能相册顶部栏
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumTopBar(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSave, boolean canSave, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 智能相册基本信息
     */
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumBasicInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String albumName, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAlbumNameChanged, @org.jetbrains.annotations.NotNull()
    java.lang.String albumDescription, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAlbumDescriptionChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 智能相册规则区域
     */
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumRulesSection(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule> rules, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onAddRule, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.Integer, ? super com.qxyu.yucram.domain.model.SmartAlbumRule, kotlin.Unit> onUpdateRule, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onRemoveRule, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 智能相册规则项
     */
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumRuleItem(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SmartAlbumRule rule, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.SmartAlbumRule, kotlin.Unit> onUpdate, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRemove, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 智能相册规则类型选择器
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumRuleTypeSelector(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SmartAlbumRuleType selectedType, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.SmartAlbumRuleType, kotlin.Unit> onTypeSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 智能相册规则值输入
     */
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumRuleValueInput(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SmartAlbumRuleType ruleType, @org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onValueChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 智能相册预览
     */
    @androidx.compose.runtime.Composable()
    public static final void SmartAlbumPreview(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.Photo> photos, boolean isLoading, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRefreshPreview, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}