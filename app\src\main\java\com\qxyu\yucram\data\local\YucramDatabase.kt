package com.qxyu.yucram.data.local

import androidx.room.Database
import androidx.room.RoomDatabase
import com.qxyu.yucram.data.local.dao.PhotoDao
import com.qxyu.yucram.data.local.dao.SettingsDao
import com.qxyu.yucram.data.local.entity.PhotoEntity
import com.qxyu.yucram.data.local.entity.SettingsEntity

/**
 * Yucram应用程序数据库
 */
@Database(
    entities = [PhotoEntity::class, SettingsEntity::class],
    version = 1,
    exportSchema = false
)
abstract class YucramDatabase : RoomDatabase() {
    
    abstract fun photoDao(): PhotoDao
    abstract fun settingsDao(): SettingsDao
}
