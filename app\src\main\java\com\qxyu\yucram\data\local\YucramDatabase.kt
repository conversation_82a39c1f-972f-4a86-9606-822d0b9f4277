package com.qxyu.yucram.data.local

import androidx.room.Database
import androidx.room.RoomDatabase
import com.qxyu.yucram.data.database.dao.CropHistoryDao
import com.qxyu.yucram.data.database.dao.CropPresetDao
import com.qxyu.yucram.data.database.entity.CropHistoryEntity
import com.qxyu.yucram.data.database.entity.CropPresetEntity
import com.qxyu.yucram.data.local.dao.PhotoDao
import com.qxyu.yucram.data.local.dao.SettingsDao
import com.qxyu.yucram.data.local.entity.PhotoEntity
import com.qxyu.yucram.data.local.entity.SettingsEntity

/**
 * Yucram应用程序数据库
 */
@Database(
    entities = [
        PhotoEntity::class,
        SettingsEntity::class,
        CropHistoryEntity::class,
        CropPresetEntity::class
    ],
    version = 2,
    exportSchema = false
)
abstract class YucramDatabase : RoomDatabase() {

    abstract fun photoDao(): PhotoDao
    abstract fun settingsDao(): SettingsDao
    abstract fun cropHistoryDao(): CropHistoryDao
    abstract fun cropPresetDao(): CropPresetDao
}
