package com.qxyu.yucram.filter;

/**
 * LUT文件管理器
 * 管理LUT/HNCS/CUBE文件的导入、导出、存储
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 H2\u00020\u0001:\u0001HB\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u0006H\u0002J\u000e\u0010\u001e\u001a\u00020\u001aH\u0082@\u00a2\u0006\u0002\u0010\u001fJ\u0018\u0010 \u001a\u00020\u001a2\u0006\u0010!\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u0006H\u0002J\u0010\u0010\"\u001a\u00020\u001a2\u0006\u0010#\u001a\u00020\u0006H\u0002J\b\u0010$\u001a\u00020\u001aH\u0002J$\u0010%\u001a\b\u0012\u0004\u0012\u00020\u001a0&2\u0006\u0010\'\u001a\u00020(H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b)\u0010*J\u0012\u0010+\u001a\u0004\u0018\u00010,2\u0006\u0010-\u001a\u00020\u001cH\u0002J,\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00060&2\u0006\u0010\'\u001a\u00020(2\u0006\u0010/\u001a\u00020\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b0\u00101J\"\u00102\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u000204030&H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b5\u0010\u001fJ\u0012\u00106\u001a\u0004\u0018\u0001042\u0006\u0010\'\u001a\u00020(H\u0002J6\u00107\u001a\b\u0012\u0004\u0012\u0002040&2\u0006\u00108\u001a\u00020\u001c2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u00109\u001a\u00020(H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b:\u0010;J\u001c\u0010<\u001a\b\u0012\u0004\u0012\u00020\u001a0&H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b=\u0010\u001fJ\u000e\u0010>\u001a\b\u0012\u0004\u0012\u00020403H\u0002J\u000e\u0010?\u001a\b\u0012\u0004\u0012\u00020403H\u0002J\u0010\u0010@\u001a\u00020\u001a2\u0006\u0010\'\u001a\u00020(H\u0002J\u0010\u0010A\u001a\u00020\u001a2\u0006\u0010B\u001a\u000204H\u0002J,\u0010C\u001a\b\u0012\u0004\u0012\u00020\u001a0&2\u0006\u0010\'\u001a\u00020(2\u0006\u0010D\u001a\u00020EH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bF\u0010GR\u001b\u0010\u0005\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u0007\u0010\bR\u001b\u0010\u000b\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\r\u0010\n\u001a\u0004\b\f\u0010\bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u000e\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0010\u0010\n\u001a\u0004\b\u000f\u0010\bR\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\u0013\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0015\u0010\n\u001a\u0004\b\u0014\u0010\bR\u001b\u0010\u0016\u001a\u00020\u00068BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0018\u0010\n\u001a\u0004\b\u0017\u0010\b\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006I"}, d2 = {"Lcom/qxyu/yucram/filter/LutFileManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "builtinDir", "Ljava/io/File;", "getBuiltinDir", "()Ljava/io/File;", "builtinDir$delegate", "Lkotlin/Lazy;", "configFile", "getConfigFile", "configFile$delegate", "iconsDir", "getIconsDir", "iconsDir$delegate", "json", "Lkotlinx/serialization/json/Json;", "lutRootDir", "getLutRootDir", "lutRootDir$delegate", "userDir", "getUserDir", "userDir$delegate", "copyAndResizeIcon", "", "iconUri", "Landroid/net/Uri;", "targetFile", "copyBuiltinLutsFromAssets", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "copyFile", "sourceUri", "createDefaultIcon", "iconFile", "createDirectories", "deleteUserLut", "Lkotlin/Result;", "filterId", "", "deleteUserLut-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "detectLutFormat", "Lcom/qxyu/yucram/domain/model/LutFormat;", "uri", "exportLut", "outputDir", "exportLut-0E7RQCE", "(Ljava/lang/String;Ljava/io/File;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAvailableFilters", "", "Lcom/qxyu/yucram/domain/model/FilterPreset;", "getAvailableFilters-IoAF18A", "getFilterById", "importLut", "lutFileUri", "filterName", "importLut-BWLJW6A", "(Landroid/net/Uri;Landroid/net/Uri;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initialize", "initialize-IoAF18A", "loadBuiltinFilters", "loadUserFilters", "removeFilterFromConfig", "saveFilterToConfig", "filter", "updateFilterParams", "params", "Lcom/qxyu/yucram/domain/model/PostProcessingParams;", "updateFilterParams-0E7RQCE", "(Ljava/lang/String;Lcom/qxyu/yucram/domain/model/PostProcessingParams;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class LutFileManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "LutFileManager";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String LUT_DIR = "luts";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String BUILTIN_DIR = "builtin";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String USER_DIR = "user";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String ICONS_DIR = "icons";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String FILTERS_CONFIG_FILE = "filters.json";
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.serialization.json.Json json = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy lutRootDir$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy builtinDir$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy userDir$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy iconsDir$delegate = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy configFile$delegate = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.filter.LutFileManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public LutFileManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    private final java.io.File getLutRootDir() {
        return null;
    }
    
    private final java.io.File getBuiltinDir() {
        return null;
    }
    
    private final java.io.File getUserDir() {
        return null;
    }
    
    private final java.io.File getIconsDir() {
        return null;
    }
    
    private final java.io.File getConfigFile() {
        return null;
    }
    
    private final void createDirectories() {
    }
    
    private final java.lang.Object copyBuiltinLutsFromAssets(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.util.List<com.qxyu.yucram.domain.model.FilterPreset> loadBuiltinFilters() {
        return null;
    }
    
    private final java.util.List<com.qxyu.yucram.domain.model.FilterPreset> loadUserFilters() {
        return null;
    }
    
    private final com.qxyu.yucram.domain.model.LutFormat detectLutFormat(android.net.Uri uri) {
        return null;
    }
    
    private final void copyFile(android.net.Uri sourceUri, java.io.File targetFile) {
    }
    
    private final void copyAndResizeIcon(android.net.Uri iconUri, java.io.File targetFile) {
    }
    
    private final void createDefaultIcon(java.io.File iconFile) {
    }
    
    private final com.qxyu.yucram.domain.model.FilterPreset getFilterById(java.lang.String filterId) {
        return null;
    }
    
    private final void saveFilterToConfig(com.qxyu.yucram.domain.model.FilterPreset filter) {
    }
    
    private final void removeFilterFromConfig(java.lang.String filterId) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/qxyu/yucram/filter/LutFileManager$Companion;", "", "()V", "BUILTIN_DIR", "", "FILTERS_CONFIG_FILE", "ICONS_DIR", "LUT_DIR", "TAG", "USER_DIR", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}