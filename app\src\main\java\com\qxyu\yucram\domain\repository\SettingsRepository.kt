package com.qxyu.yucram.domain.repository

import kotlinx.coroutines.flow.Flow

/**
 * 设置Repository接口
 */
interface SettingsRepository {
    
    /**
     * 获取主题颜色
     */
    fun getThemeColor(): Flow<String>
    
    /**
     * 设置主题颜色
     */
    suspend fun setThemeColor(color: String)
    
    /**
     * 获取震动强度
     */
    fun getVibrationIntensity(): Flow<Int>
    
    /**
     * 设置震动强度
     */
    suspend fun setVibrationIntensity(intensity: Int)
    
    /**
     * 获取语言设置
     */
    fun getLanguage(): Flow<String>
    
    /**
     * 设置语言
     */
    suspend fun setLanguage(language: String)
    
    /**
     * 获取是否启用GPS水印
     */
    fun isGpsWatermarkEnabled(): Flow<Boolean>
    
    /**
     * 设置GPS水印
     */
    suspend fun setGpsWatermarkEnabled(enabled: Boolean)
    
    /**
     * 获取是否启用震动反馈
     */
    fun isVibrationEnabled(): Flow<Boolean>
    
    /**
     * 设置震动反馈
     */
    suspend fun setVibrationEnabled(enabled: Boolean)
    
    /**
     * 重置所有设置
     */
    suspend fun resetAllSettings()
}
