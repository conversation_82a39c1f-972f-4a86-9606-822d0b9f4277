package com.qxyu.yucram.presentation;

/**
 * 应用状态类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000bJ\u0006\u0010\f\u001a\u00020\bJ\u0006\u0010\r\u001a\u00020\bJ\u000e\u0010\u000e\u001a\u00020\b2\u0006\u0010\u000f\u001a\u00020\u000bJ\u000e\u0010\u0010\u001a\u00020\b2\u0006\u0010\u000f\u001a\u00020\u000bJ\u0006\u0010\u0011\u001a\u00020\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0012"}, d2 = {"Lcom/qxyu/yucram/presentation/YucramAppState;", "", "navController", "Landroidx/navigation/NavHostController;", "(Landroidx/navigation/NavHostController;)V", "getNavController", "()Landroidx/navigation/NavHostController;", "navigateBack", "", "navigateTo", "route", "", "navigateToCamera", "navigateToGallery", "navigateToPhotoDetail", "photoId", "navigateToPhotoEdit", "navigateToSettings", "app_debug"})
public final class YucramAppState {
    @org.jetbrains.annotations.NotNull()
    private final androidx.navigation.NavHostController navController = null;
    
    public YucramAppState(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavHostController navController) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.navigation.NavHostController getNavController() {
        return null;
    }
    
    /**
     * 导航到指定路由
     */
    public final void navigateTo(@org.jetbrains.annotations.NotNull()
    java.lang.String route) {
    }
    
    /**
     * 返回上一页
     */
    public final void navigateBack() {
    }
    
    /**
     * 导航到相机页面
     */
    public final void navigateToCamera() {
    }
    
    /**
     * 导航到图库页面
     */
    public final void navigateToGallery() {
    }
    
    /**
     * 导航到设置页面
     */
    public final void navigateToSettings() {
    }
    
    /**
     * 导航到照片详情
     */
    public final void navigateToPhotoDetail(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    /**
     * 导航到照片编辑
     */
    public final void navigateToPhotoEdit(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
}