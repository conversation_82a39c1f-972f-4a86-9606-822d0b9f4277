package com.qxyu.yucram.domain.model;

/**
 * 预设分类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/domain/model/PresetCategory;", "", "(Ljava/lang/String;I)V", "USER", "PORTRAIT", "LANDSCAPE", "STREET", "VINTAGE", "DRAMATIC", "NATURAL", "BLACK_WHITE", "app_debug"})
public enum PresetCategory {
    /*public static final*/ USER /* = new USER() */,
    /*public static final*/ PORTRAIT /* = new PORTRAIT() */,
    /*public static final*/ LANDSCAPE /* = new LANDSCAPE() */,
    /*public static final*/ STREET /* = new STREET() */,
    /*public static final*/ VINTAGE /* = new VINTAGE() */,
    /*public static final*/ DRAMATIC /* = new DRAMATIC() */,
    /*public static final*/ NATURAL /* = new NATURAL() */,
    /*public static final*/ BLACK_WHITE /* = new BLACK_WHITE() */;
    
    PresetCategory() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.PresetCategory> getEntries() {
        return null;
    }
}