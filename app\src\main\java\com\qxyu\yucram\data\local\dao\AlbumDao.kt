package com.qxyu.yucram.data.local.dao

import androidx.room.*
import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.flow.Flow
import java.time.LocalDateTime

/**
 * 相册数据访问对象
 */
@Dao
interface AlbumDao {
    
    // ========== 基础查询 ==========
    
    /**
     * 获取所有相册
     */
    @Query("SELECT * FROM albums ORDER BY dateModified DESC")
    fun getAllAlbums(): Flow<List<Album>>
    
    /**
     * 根据ID获取相册
     */
    @Query("SELECT * FROM albums WHERE id = :albumId")
    suspend fun getAlbumById(albumId: String): Album?
    
    /**
     * 获取用户创建的相册
     */
    @Query("SELECT * FROM albums WHERE albumType = 'USER_CREATED' ORDER BY dateModified DESC")
    fun getUserAlbums(): Flow<List<Album>>
    
    /**
     * 获取系统相册
     */
    @Query("SELECT * FROM albums WHERE isSystemAlbum = 1 ORDER BY albumType")
    fun getSystemAlbums(): Flow<List<Album>>
    
    // ========== 插入和更新 ==========
    
    /**
     * 插入相册
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlbum(album: Album)
    
    /**
     * 批量插入相册
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAlbums(albums: List<Album>)
    
    /**
     * 更新相册
     */
    @Update
    suspend fun updateAlbum(album: Album)
    
    /**
     * 更新相册照片数量
     */
    @Query("UPDATE albums SET photoCount = :count, dateModified = :modifyTime WHERE id = :albumId")
    suspend fun updatePhotoCount(albumId: String, count: Int, modifyTime: LocalDateTime)
    
    /**
     * 更新相册封面
     */
    @Query("UPDATE albums SET coverPhotoId = :photoId, dateModified = :modifyTime WHERE id = :albumId")
    suspend fun updateCoverPhoto(albumId: String, photoId: String?, modifyTime: LocalDateTime)
    
    // ========== 删除 ==========
    
    /**
     * 删除相册
     */
    @Delete
    suspend fun deleteAlbum(album: Album)
    
    /**
     * 根据ID删除相册
     */
    @Query("DELETE FROM albums WHERE id = :albumId")
    suspend fun deleteAlbumById(albumId: String)
    
    // ========== 搜索 ==========
    
    /**
     * 搜索相册
     */
    @Query("""
        SELECT * FROM albums 
        WHERE name LIKE '%' || :query || '%' 
        OR description LIKE '%' || :query || '%'
        ORDER BY dateModified DESC
    """)
    fun searchAlbums(query: String): Flow<List<Album>>
    
    /**
     * 根据标签搜索相册
     */
    @Query("SELECT * FROM albums WHERE tags LIKE '%' || :tag || '%' ORDER BY dateModified DESC")
    fun getAlbumsByTag(tag: String): Flow<List<Album>>
    
    // ========== 统计 ==========
    
    /**
     * 获取相册数量
     */
    @Query("SELECT COUNT(*) FROM albums")
    suspend fun getAlbumCount(): Int
    
    /**
     * 获取用户相册数量
     */
    @Query("SELECT COUNT(*) FROM albums WHERE albumType = 'USER_CREATED'")
    suspend fun getUserAlbumCount(): Int
}

/**
 * 相册照片关联DAO
 */
@Dao
interface AlbumPhotoDao {
    
    /**
     * 添加照片到相册
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun addPhotoToAlbum(albumPhoto: AlbumPhoto)
    
    /**
     * 批量添加照片到相册
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun addPhotosToAlbum(albumPhotos: List<AlbumPhoto>)
    
    /**
     * 从相册移除照片
     */
    @Query("DELETE FROM album_photos WHERE albumId = :albumId AND photoId = :photoId")
    suspend fun removePhotoFromAlbum(albumId: String, photoId: String)
    
    /**
     * 批量从相册移除照片
     */
    @Query("DELETE FROM album_photos WHERE albumId = :albumId AND photoId IN (:photoIds)")
    suspend fun removePhotosFromAlbum(albumId: String, photoIds: List<String>)
    
    /**
     * 获取相册中的照片
     */
    @Query("""
        SELECT p.* FROM photos p
        INNER JOIN album_photos ap ON p.id = ap.photoId
        WHERE ap.albumId = :albumId AND p.isDeleted = 0
        ORDER BY ap.order ASC, ap.dateAdded DESC
    """)
    fun getPhotosInAlbum(albumId: String): Flow<List<Photo>>
    
    /**
     * 获取照片所属的相册
     */
    @Query("""
        SELECT a.* FROM albums a
        INNER JOIN album_photos ap ON a.id = ap.albumId
        WHERE ap.photoId = :photoId
        ORDER BY a.dateModified DESC
    """)
    suspend fun getAlbumsForPhoto(photoId: String): List<Album>
    
    /**
     * 获取相册照片数量
     */
    @Query("""
        SELECT COUNT(*) FROM album_photos ap
        INNER JOIN photos p ON ap.photoId = p.id
        WHERE ap.albumId = :albumId AND p.isDeleted = 0
    """)
    suspend fun getPhotoCountInAlbum(albumId: String): Int
    
    /**
     * 检查照片是否在相册中
     */
    @Query("SELECT COUNT(*) > 0 FROM album_photos WHERE albumId = :albumId AND photoId = :photoId")
    suspend fun isPhotoInAlbum(albumId: String, photoId: String): Boolean
    
    /**
     * 更新照片在相册中的顺序
     */
    @Query("UPDATE album_photos SET `order` = :order WHERE albumId = :albumId AND photoId = :photoId")
    suspend fun updatePhotoOrder(albumId: String, photoId: String, order: Int)
    
    /**
     * 清理已删除照片的关联
     */
    @Query("""
        DELETE FROM album_photos 
        WHERE photoId IN (
            SELECT id FROM photos WHERE isDeleted = 1
        )
    """)
    suspend fun cleanupDeletedPhotoAssociations()
    
    /**
     * 删除相册的所有照片关联
     */
    @Query("DELETE FROM album_photos WHERE albumId = :albumId")
    suspend fun removeAllPhotosFromAlbum(albumId: String)
}

/**
 * 智能相册规则DAO
 */
@Dao
interface SmartAlbumRuleDao {
    
    /**
     * 插入规则
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRule(rule: SmartAlbumRule)
    
    /**
     * 批量插入规则
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRules(rules: List<SmartAlbumRule>)
    
    /**
     * 更新规则
     */
    @Update
    suspend fun updateRule(rule: SmartAlbumRule)
    
    /**
     * 删除规则
     */
    @Delete
    suspend fun deleteRule(rule: SmartAlbumRule)
    
    /**
     * 获取相册的所有规则
     */
    @Query("SELECT * FROM smart_album_rules WHERE albumId = :albumId AND isEnabled = 1")
    suspend fun getRulesForAlbum(albumId: String): List<SmartAlbumRule>
    
    /**
     * 删除相册的所有规则
     */
    @Query("DELETE FROM smart_album_rules WHERE albumId = :albumId")
    suspend fun deleteRulesForAlbum(albumId: String)
    
    /**
     * 启用/禁用规则
     */
    @Query("UPDATE smart_album_rules SET isEnabled = :enabled WHERE id = :ruleId")
    suspend fun setRuleEnabled(ruleId: String, enabled: Boolean)
}
