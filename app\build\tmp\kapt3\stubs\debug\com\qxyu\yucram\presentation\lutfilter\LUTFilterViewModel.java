package com.qxyu.yucram.presentation.lutfilter;

/**
 * LUT滤镜ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0010\n\u0002\u0010\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u000f\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J&\u0010\'\u001a\u00020(2\u0006\u0010\u001f\u001a\u00020\u00102\u0006\u0010)\u001a\u00020\n2\u0006\u0010*\u001a\u00020\u000eH\u0082@\u00a2\u0006\u0002\u0010+J\u0006\u0010,\u001a\u00020(J\u0006\u0010-\u001a\u00020(J\u0006\u0010.\u001a\u00020(J\b\u0010/\u001a\u00020\u0012H\u0002J\u0018\u00100\u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020\u00102\u0006\u0010)\u001a\u00020\nH\u0002J\b\u00101\u001a\u00020(H\u0002J\u0006\u00102\u001a\u000203J\b\u00104\u001a\u00020(H\u0002J\u0010\u00105\u001a\u00020(2\u0006\u0010\u001f\u001a\u00020\u0010H\u0002J\u000e\u00106\u001a\u00020(2\u0006\u00107\u001a\u00020\u0012J\b\u00108\u001a\u00020(H\u0002J\u0006\u00109\u001a\u00020(J\u000e\u0010:\u001a\u00020(2\u0006\u0010;\u001a\u00020\u0012J\u0010\u0010<\u001a\u00020(2\b\u0010=\u001a\u0004\u0018\u00010\fJ\u000e\u0010>\u001a\u00020(2\u0006\u0010)\u001a\u00020\nJ\u000e\u0010?\u001a\u00020(2\u0006\u0010)\u001a\u00020\nJ\u000e\u0010@\u001a\u00020(2\u0006\u0010A\u001a\u00020\u000eR\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\f0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u001d\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0019R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00120\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0019\u0010#\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0019R\u0017\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00150\u0017\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0019\u00a8\u0006B"}, d2 = {"Lcom/qxyu/yucram/presentation/lutfilter/LUTFilterViewModel;", "Landroidx/lifecycle/ViewModel;", "photoRepository", "Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "lutFilterRepository", "Lcom/qxyu/yucram/domain/repository/LUTFilterRepository;", "(Lcom/qxyu/yucram/domain/repository/PhotoRepository;Lcom/qxyu/yucram/domain/repository/LUTFilterRepository;)V", "_allFilters", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "_currentCategory", "Lcom/qxyu/yucram/domain/model/LUTCategory;", "_filterSettings", "Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "_photo", "Lcom/qxyu/yucram/domain/model/Photo;", "_searchQuery", "", "_selectedFilter", "_uiState", "Lcom/qxyu/yucram/presentation/lutfilter/LUTFilterUiState;", "currentCategory", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentCategory", "()Lkotlinx/coroutines/flow/StateFlow;", "filterSettings", "getFilterSettings", "filters", "getFilters", "originalSettings", "photo", "getPhoto", "searchQuery", "getSearchQuery", "selectedFilter", "getSelectedFilter", "uiState", "getUiState", "addFilterHistory", "", "filter", "settings", "(Lcom/qxyu/yucram/domain/model/Photo;Lcom/qxyu/yucram/domain/model/LUTFilter;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyFilter", "clearError", "clearMessage", "generateHistoryId", "generateOutputPath", "generatePreview", "hasChanges", "", "loadBuiltInFilters", "loadExistingLUTSettings", "loadPhoto", "photoId", "loadRecentFilters", "resetFilter", "searchFilters", "query", "selectCategory", "category", "selectFilter", "showFilterDetails", "updateFilterSettings", "newSettings", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class LUTFilterViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.PhotoRepository photoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.LUTFilterRepository lutFilterRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.lutfilter.LUTFilterUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.lutfilter.LUTFilterUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.Photo> _photo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> photo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.LUTFilter> _selectedFilter = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LUTFilter> selectedFilter = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.LUTFilterSettings> _filterSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LUTFilterSettings> filterSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.LUTCategory> _currentCategory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LUTCategory> currentCategory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _searchQuery = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> searchQuery = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.qxyu.yucram.domain.model.LUTFilter>> _allFilters = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.LUTFilter>> filters = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LUTFilterSettings originalSettings = null;
    
    @javax.inject.Inject()
    public LUTFilterViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.PhotoRepository photoRepository, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.LUTFilterRepository lutFilterRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.lutfilter.LUTFilterUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> getPhoto() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LUTFilter> getSelectedFilter() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LUTFilterSettings> getFilterSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LUTCategory> getCurrentCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getSearchQuery() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.LUTFilter>> getFilters() {
        return null;
    }
    
    public final void loadPhoto(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void selectFilter(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter) {
    }
    
    public final void resetFilter() {
    }
    
    public final void updateFilterSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings newSettings) {
    }
    
    public final boolean hasChanges() {
        return false;
    }
    
    public final void selectCategory(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTCategory category) {
    }
    
    public final void searchFilters(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    public final void applyFilter() {
    }
    
    private final void generatePreview() {
    }
    
    public final void showFilterDetails(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter) {
    }
    
    private final void loadBuiltInFilters() {
    }
    
    private final void loadRecentFilters() {
    }
    
    private final void loadExistingLUTSettings(com.qxyu.yucram.domain.model.Photo photo) {
    }
    
    private final java.lang.String generateOutputPath(com.qxyu.yucram.domain.model.Photo photo, com.qxyu.yucram.domain.model.LUTFilter filter) {
        return null;
    }
    
    private final java.lang.Object addFilterHistory(com.qxyu.yucram.domain.model.Photo photo, com.qxyu.yucram.domain.model.LUTFilter filter, com.qxyu.yucram.domain.model.LUTFilterSettings settings, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.String generateHistoryId() {
        return null;
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}