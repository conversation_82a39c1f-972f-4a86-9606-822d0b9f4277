package com.qxyu.yucram.domain.repository;

/**
 * 设置Repository接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u000e\bf\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H&J\u000e\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H&J\u000e\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00070\u0003H&J\u000e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0003H&J\u000e\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u0003H&J\u000e\u0010\u000b\u001a\u00020\fH\u00a6@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u000e\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\tH\u00a6@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010\u0011\u001a\u00020\f2\u0006\u0010\u0012\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\f2\u0006\u0010\u0015\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0016\u001a\u00020\f2\u0006\u0010\u000f\u001a\u00020\tH\u00a6@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010\u0017\u001a\u00020\f2\u0006\u0010\u0018\u001a\u00020\u0007H\u00a6@\u00a2\u0006\u0002\u0010\u0019\u00a8\u0006\u001a"}, d2 = {"Lcom/qxyu/yucram/domain/repository/SettingsRepository;", "", "getLanguage", "Lkotlinx/coroutines/flow/Flow;", "", "getThemeColor", "getVibrationIntensity", "", "isGpsWatermarkEnabled", "", "isVibrationEnabled", "resetAllSettings", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setGpsWatermarkEnabled", "enabled", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setLanguage", "language", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setThemeColor", "color", "setVibrationEnabled", "setVibrationIntensity", "intensity", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface SettingsRepository {
    
    /**
     * 获取主题颜色
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.String> getThemeColor();
    
    /**
     * 设置主题颜色
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setThemeColor(@org.jetbrains.annotations.NotNull()
    java.lang.String color, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取震动强度
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Integer> getVibrationIntensity();
    
    /**
     * 设置震动强度
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setVibrationIntensity(int intensity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取语言设置
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.String> getLanguage();
    
    /**
     * 设置语言
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setLanguage(@org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取是否启用GPS水印
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Boolean> isGpsWatermarkEnabled();
    
    /**
     * 设置GPS水印
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setGpsWatermarkEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取是否启用震动反馈
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.lang.Boolean> isVibrationEnabled();
    
    /**
     * 设置震动反馈
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setVibrationEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 重置所有设置
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object resetAllSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}