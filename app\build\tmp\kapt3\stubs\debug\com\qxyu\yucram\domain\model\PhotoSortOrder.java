package com.qxyu.yucram.domain.model;

/**
 * 照片排序方式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/domain/model/PhotoSortOrder;", "", "(Ljava/lang/String;I)V", "DATE_TAKEN_DESC", "DATE_TAKEN_ASC", "DATE_ADDED_DESC", "DATE_ADDED_ASC", "NAME_ASC", "NAME_DESC", "SIZE_DESC", "SIZE_ASC", "app_debug"})
public enum PhotoSortOrder {
    /*public static final*/ DATE_TAKEN_DESC /* = new DATE_TAKEN_DESC() */,
    /*public static final*/ DATE_TAKEN_ASC /* = new DATE_TAKEN_ASC() */,
    /*public static final*/ DATE_ADDED_DESC /* = new DATE_ADDED_DESC() */,
    /*public static final*/ DATE_ADDED_ASC /* = new DATE_ADDED_ASC() */,
    /*public static final*/ NAME_ASC /* = new NAME_ASC() */,
    /*public static final*/ NAME_DESC /* = new NAME_DESC() */,
    /*public static final*/ SIZE_DESC /* = new SIZE_DESC() */,
    /*public static final*/ SIZE_ASC /* = new SIZE_ASC() */;
    
    PhotoSortOrder() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.PhotoSortOrder> getEntries() {
        return null;
    }
}