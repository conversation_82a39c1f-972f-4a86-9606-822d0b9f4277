package com.qxyu.yucram.domain.model;

/**
 * 装饰样式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/domain/model/DecorativeStyle;", "", "(Ljava/lang/String;I)V", "NONE", "CORNER_ORNAMENTS", "EDGE_ORNAMENTS", "FULL_FRAME", "VINTAGE_CORNERS", "FLORAL_BORDER", "GEOMETRIC_FRAME", "ARTISTIC_FRAME", "app_debug"})
public enum DecorativeStyle {
    /*public static final*/ NONE /* = new NONE() */,
    /*public static final*/ CORNER_ORNAMENTS /* = new CORNER_ORNAMENTS() */,
    /*public static final*/ EDGE_ORNAMENTS /* = new EDGE_ORNAMENTS() */,
    /*public static final*/ FULL_FRAME /* = new FULL_FRAME() */,
    /*public static final*/ VINTAGE_CORNERS /* = new VINTAGE_CORNERS() */,
    /*public static final*/ FLORAL_BORDER /* = new FLORAL_BORDER() */,
    /*public static final*/ GEOMETRIC_FRAME /* = new GEOMETRIC_FRAME() */,
    /*public static final*/ ARTISTIC_FRAME /* = new ARTISTIC_FRAME() */;
    
    DecorativeStyle() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.DecorativeStyle> getEntries() {
        return null;
    }
}