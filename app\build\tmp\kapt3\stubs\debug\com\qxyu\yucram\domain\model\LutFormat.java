package com.qxyu.yucram.domain.model;

/**
 * LUT文件格式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0007j\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTFormat;", "", "extension", "", "description", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getExtension", "CUBE", "LUT_3D", "ICC", "CSP", "LOOK", "CDL", "CUSTOM", "app_debug"})
public enum LUTFormat {
    /*public static final*/ CUBE /* = new CUBE(null, null) */,
    /*public static final*/ LUT_3D /* = new LUT_3D(null, null) */,
    /*public static final*/ ICC /* = new ICC(null, null) */,
    /*public static final*/ CSP /* = new CSP(null, null) */,
    /*public static final*/ LOOK /* = new LOOK(null, null) */,
    /*public static final*/ CDL /* = new CDL(null, null) */,
    /*public static final*/ CUSTOM /* = new CUSTOM(null, null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String extension = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    
    LUTFormat(java.lang.String extension, java.lang.String description) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getExtension() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.LUTFormat> getEntries() {
        return null;
    }
}