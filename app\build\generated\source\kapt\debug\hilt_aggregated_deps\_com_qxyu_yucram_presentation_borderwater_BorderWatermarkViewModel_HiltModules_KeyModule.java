package hilt_aggregated_deps;

import dagger.hilt.processor.internal.aggregateddeps.AggregatedDeps;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedDeps(
    components = "dagger.hilt.android.components.ActivityRetainedComponent",
    modules = "com.qxyu.yucram.presentation.borderwater.BorderWatermarkViewModel_HiltModules.KeyModule"
)
public class _com_qxyu_yucram_presentation_borderwater_BorderWatermarkViewModel_HiltModules_KeyModule {
}
