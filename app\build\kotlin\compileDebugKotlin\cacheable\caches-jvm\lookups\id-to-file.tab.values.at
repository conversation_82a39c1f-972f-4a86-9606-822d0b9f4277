/ Header Record For PersistentHashMapValueStorage2 1app/src/main/java/com/qxyu/yucram/MainActivity.kt/ .app/src/main/java/com/qxyu/yucram/YucramApp.kt7 6app/src/main/java/com/qxyu/yucram/YucramApplication.kt: 9app/src/main/java/com/qxyu/yucram/camera/CameraManager.kt? >app/src/main/java/com/qxyu/yucram/data/local/YucramDatabase.kt= <app/src/main/java/com/qxyu/yucram/data/local/dao/PhotoDao.kt@ ?app/src/main/java/com/qxyu/yucram/data/local/dao/SettingsDao.ktC Bapp/src/main/java/com/qxyu/yucram/data/local/entity/PhotoEntity.ktF Eapp/src/main/java/com/qxyu/yucram/data/local/entity/SettingsEntity.ktJ Iapp/src/main/java/com/qxyu/yucram/data/repository/CameraRepositoryImpl.ktI Happ/src/main/java/com/qxyu/yucram/data/repository/PhotoRepositoryImpl.ktL Kapp/src/main/java/com/qxyu/yucram/data/repository/SettingsRepositoryImpl.kt2 1app/src/main/java/com/qxyu/yucram/di/AppModule.kt7 6app/src/main/java/com/qxyu/yucram/di/DatabaseModule.kt= <app/src/main/java/com/qxyu/yucram/di/FilmSimulationModule.kt9 8app/src/main/java/com/qxyu/yucram/di/RepositoryModule.ktA @app/src/main/java/com/qxyu/yucram/domain/model/CameraSettings.ktA @app/src/main/java/com/qxyu/yucram/domain/model/FilmSimulation.kt9 8app/src/main/java/com/qxyu/yucram/domain/model/Filter.kt8 7app/src/main/java/com/qxyu/yucram/domain/model/Photo.ktH Gapp/src/main/java/com/qxyu/yucram/domain/repository/CameraRepository.ktG Fapp/src/main/java/com/qxyu/yucram/domain/repository/PhotoRepository.ktJ Iapp/src/main/java/com/qxyu/yucram/domain/repository/SettingsRepository.kt7 6app/src/main/java/com/qxyu/yucram/film/LutProcessor.ktE Dapp/src/main/java/com/qxyu/yucram/filter/DeviceCapabilityDetector.ktD Capp/src/main/java/com/qxyu/yucram/filter/ImageProcessingPipeline.kt; :app/src/main/java/com/qxyu/yucram/filter/LutFileManager.ktC Bapp/src/main/java/com/qxyu/yucram/navigation/YucramDestinations.ktA @app/src/main/java/com/qxyu/yucram/navigation/YucramNavigation.kt< ;app/src/main/java/com/qxyu/yucram/presentation/YucramApp.ktQ Papp/src/main/java/com/qxyu/yucram/presentation/animation/NavigationAnimations.ktG Fapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraPreview.ktF Eapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraScreen.ktI Happ/src/main/java/com/qxyu/yucram/presentation/camera/CameraViewModel.ktE Dapp/src/main/java/com/qxyu/yucram/presentation/camera/GridOverlay.ktL Kapp/src/main/java/com/qxyu/yucram/presentation/camera/OppoAdaptationInfo.ktQ Papp/src/main/java/com/qxyu/yucram/presentation/components/BottomNavigationBar.ktL Kapp/src/main/java/com/qxyu/yucram/presentation/components/NavigationRail.ktO Napp/src/main/java/com/qxyu/yucram/presentation/filter/FilterSelectionScreen.ktR Qapp/src/main/java/com/qxyu/yucram/presentation/filter/FilterSelectionViewModel.ktH Gapp/src/main/java/com/qxyu/yucram/presentation/gallery/GalleryScreen.ktN Mapp/src/main/java/com/qxyu/yucram/presentation/permission/PermissionScreen.ktQ Papp/src/main/java/com/qxyu/yucram/presentation/permission/PermissionViewModel.ktJ Iapp/src/main/java/com/qxyu/yucram/presentation/settings/SettingsScreen.ktI Happ/src/main/java/com/qxyu/yucram/presentation/utils/ResponsiveLayout.kt4 3app/src/main/java/com/qxyu/yucram/ui/theme/Color.kt4 3app/src/main/java/com/qxyu/yucram/ui/theme/Theme.kt3 2app/src/main/java/com/qxyu/yucram/ui/theme/Type.kt9 8app/src/main/java/com/qxyu/yucram/utils/DeviceAdapter.kt6 5app/src/main/java/com/qxyu/yucram/utils/ImageSaver.kt; :app/src/main/java/com/qxyu/yucram/utils/PermissionUtils.kt; :app/src/main/java/com/qxyu/yucram/filter/LutFileManager.ktE Dapp/src/main/java/com/qxyu/yucram/filter/DeviceCapabilityDetector.ktD Capp/src/main/java/com/qxyu/yucram/filter/ImageProcessingPipeline.kt; :app/src/main/java/com/qxyu/yucram/filter/LutFileManager.ktE Dapp/src/main/java/com/qxyu/yucram/filter/DeviceCapabilityDetector.ktD Capp/src/main/java/com/qxyu/yucram/filter/ImageProcessingPipeline.kt/ .app/src/main/java/com/qxyu/yucram/YucramApp.kt: 9app/src/main/java/com/qxyu/yucram/camera/CameraManager.kt> =app/src/main/java/com/qxyu/yucram/camera/RawCaptureManager.kt2 1app/src/main/java/com/qxyu/yucram/di/AppModule.kt= <app/src/main/java/com/qxyu/yucram/di/FilmSimulationModule.ktD Capp/src/main/java/com/qxyu/yucram/filter/ImageProcessingPipeline.ktA @app/src/main/java/com/qxyu/yucram/navigation/YucramNavigation.ktF Eapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraScreen.ktB Aapp/src/main/java/com/qxyu/yucram/processing/RawToLogProcessor.kt2 1app/src/main/java/com/qxyu/yucram/MainActivity.kt< ;app/src/main/java/com/qxyu/yucram/presentation/YucramApp.ktG Fapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraPreview.ktI Happ/src/main/java/com/qxyu/yucram/presentation/camera/CameraViewModel.ktE Dapp/src/main/java/com/qxyu/yucram/data/local/converter/Converters.kt= <app/src/main/java/com/qxyu/yucram/data/local/dao/AlbumDao.kt= <app/src/main/java/com/qxyu/yucram/data/local/dao/PhotoDao.ktI Happ/src/main/java/com/qxyu/yucram/data/repository/AlbumRepositoryImpl.ktJ Iapp/src/main/java/com/qxyu/yucram/data/repository/CameraRepositoryImpl.ktI Happ/src/main/java/com/qxyu/yucram/data/repository/PhotoRepositoryImpl.kt= <app/src/main/java/com/qxyu/yucram/di/FilmSimulationModule.kt9 8app/src/main/java/com/qxyu/yucram/di/RepositoryModule.kt8 7app/src/main/java/com/qxyu/yucram/domain/model/Album.kt8 7app/src/main/java/com/qxyu/yucram/domain/model/Photo.ktG Fapp/src/main/java/com/qxyu/yucram/domain/repository/AlbumRepository.ktG Fapp/src/main/java/com/qxyu/yucram/domain/repository/PhotoRepository.ktH Gapp/src/main/java/com/qxyu/yucram/presentation/gallery/GalleryScreen.ktK Japp/src/main/java/com/qxyu/yucram/presentation/gallery/GalleryViewModel.ktS Rapp/src/main/java/com/qxyu/yucram/presentation/gallery/components/GalleryTopBar.ktO Napp/src/main/java/com/qxyu/yucram/presentation/gallery/components/PhotoGrid.ktH Gapp/src/main/java/com/qxyu/yucram/domain/repository/CameraRepository.ktA @app/src/main/java/com/qxyu/yucram/navigation/YucramNavigation.ktO Napp/src/main/java/com/qxyu/yucram/presentation/filter/FilterSelectionScreen.kt8 7app/src/main/java/com/qxyu/yucram/domain/model/Album.kt< ;app/src/main/java/com/qxyu/yucram/domain/model/PhotoEdit.ktJ Iapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumDetailScreen.ktH Gapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumListScreen.ktK Japp/src/main/java/com/qxyu/yucram/presentation/album/AlbumListViewModel.ktI Happ/src/main/java/com/qxyu/yucram/presentation/album/SmartAlbumScreen.ktL Kapp/src/main/java/com/qxyu/yucram/presentation/album/SmartAlbumViewModel.ktP Oapp/src/main/java/com/qxyu/yucram/presentation/photodetail/PhotoDetailScreen.ktS Rapp/src/main/java/com/qxyu/yucram/presentation/photodetail/PhotoDetailViewModel.kt] \app/src/main/java/com/qxyu/yucram/presentation/photodetail/components/ExifInfoBottomSheet.ktL Kapp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditScreen.ktO Napp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditViewModel.kt\ [app/src/main/java/com/qxyu/yucram/presentation/photoedit/components/BasicAdjustmentPanel.kt\ [app/src/main/java/com/qxyu/yucram/presentation/photoedit/components/CurveAdjustmentPanel.ktU Tapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/EditToolPanel.ktZ Yapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/HSLAdjustmentPanel.ktX Wapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/PhotoEditPreview.ktT Sapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/SliderPanels.ktE Dapp/src/main/java/com/qxyu/yucram/data/local/converter/Converters.kt= <app/src/main/java/com/qxyu/yucram/data/local/dao/AlbumDao.ktI Happ/src/main/java/com/qxyu/yucram/data/repository/AlbumRepositoryImpl.ktG Fapp/src/main/java/com/qxyu/yucram/domain/repository/AlbumRepository.ktE Dapp/src/main/java/com/qxyu/yucram/domain/model/BorderAndWatermark.kt8 7app/src/main/java/com/qxyu/yucram/domain/model/Photo.kt< ;app/src/main/java/com/qxyu/yucram/domain/model/PhotoEdit.ktT Sapp/src/main/java/com/qxyu/yucram/presentation/borderwater/BorderWatermarkScreen.ktW Vapp/src/main/java/com/qxyu/yucram/presentation/borderwater/BorderWatermarkViewModel.ktY Xapp/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderEditPanel.ktb aapp/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderWatermarkEditPanel.kt` _app/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderWatermarkPreview.kt\ [app/src/main/java/com/qxyu/yucram/presentation/borderwater/components/WatermarkEditPanel.ktL Kapp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditScreen.ktU Tapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/EditToolPanel.ktE Dapp/src/main/java/com/qxyu/yucram/data/local/converter/Converters.kt] \app/src/main/java/com/qxyu/yucram/presentation/photodetail/components/ExifInfoBottomSheet.ktO Napp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditViewModel.kt\ [app/src/main/java/com/qxyu/yucram/presentation/photoedit/components/BasicAdjustmentPanel.ktX Wapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/PhotoEditPreview.kt