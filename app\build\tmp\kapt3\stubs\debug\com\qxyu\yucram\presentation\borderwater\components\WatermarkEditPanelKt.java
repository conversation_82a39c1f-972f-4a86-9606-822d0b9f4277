package com.qxyu.yucram.presentation.borderwater.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000`\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a$\u0010\b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a8\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\r\u0010\u000e\u001a.\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00132\u0012\u0010\u0014\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0017\u001a\u00020\u00182\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001aZ\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001f2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u001f0!2\u0012\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020\u00010\u00052\u0014\b\u0002\u0010#\u001a\u000e\u0012\u0004\u0012\u00020\u001f\u0012\u0004\u0012\u00020\u001d0\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010$\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010%\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a$\u0010&\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a.\u0010\'\u001a\u00020\u00012\u0006\u0010(\u001a\u00020)2\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020)\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a0\u0010+\u001a\u00020\u00012\u0006\u0010,\u001a\u00020-2\u0006\u0010.\u001a\u00020)2\f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u0001002\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u00101\u001a\u00020\u00012\u0006\u00102\u001a\u00020-2\u0012\u00103\u001a\u000e\u0012\u0004\u0012\u00020-\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00064"}, d2 = {"WatermarkAdvancedSettings", "", "watermarkSettings", "Lcom/qxyu/yucram/domain/model/WatermarkSettings;", "onSettingsChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "WatermarkCameraInfoSettings", "WatermarkColorPicker", "selectedColor", "Landroidx/compose/ui/graphics/Color;", "onColorSelected", "WatermarkColorPicker-ek8zF_U", "(JLkotlin/jvm/functions/Function1;Landroidx/compose/ui/Modifier;)V", "WatermarkContentSettings", "WatermarkEditPanel", "WatermarkFontWeightSelector", "selectedWeight", "Landroidx/compose/ui/text/font/FontWeight;", "onWeightSelected", "WatermarkLogoSettings", "WatermarkPositionGrid", "selectedPosition", "Lcom/qxyu/yucram/domain/model/WatermarkPosition;", "onPositionSelected", "WatermarkPositionSettings", "WatermarkSliderSetting", "label", "", "value", "", "valueRange", "Lkotlin/ranges/ClosedFloatingPointRange;", "onValueChanged", "formatValue", "WatermarkStyleSettings", "WatermarkTextSettings", "WatermarkTimestampSettings", "WatermarkToggleSection", "isEnabled", "", "onToggle", "WatermarkTypeItem", "type", "Lcom/qxyu/yucram/domain/model/WatermarkType;", "isSelected", "onClick", "Lkotlin/Function0;", "WatermarkTypeSection", "selectedType", "onTypeSelected", "app_debug"})
public final class WatermarkEditPanelKt {
    
    /**
     * 水印编辑面板
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkEditPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印开关区域
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkToggleSection(boolean isEnabled, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onToggle, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印类型选择
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkTypeSection(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkType selectedType, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkType, kotlin.Unit> onTypeSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印类型项
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkTypeItem(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkType type, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印内容设置
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkContentSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 文字水印设置
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkTextSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * Logo水印设置
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkLogoSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印位置设置
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkPositionSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印位置网格
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkPositionGrid(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkPosition selectedPosition, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkPosition, kotlin.Unit> onPositionSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印样式设置
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkStyleSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印高级设置
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkAdvancedSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 水印滑块设置
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkSliderSetting(@org.jetbrains.annotations.NotNull()
    java.lang.String label, float value, @org.jetbrains.annotations.NotNull()
    kotlin.ranges.ClosedFloatingPointRange<java.lang.Float> valueRange, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onValueChanged, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, java.lang.String> formatValue, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 字体粗细选择器
     */
    @androidx.compose.runtime.Composable()
    public static final void WatermarkFontWeightSelector(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.text.font.FontWeight selectedWeight, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super androidx.compose.ui.text.font.FontWeight, kotlin.Unit> onWeightSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void WatermarkTimestampSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void WatermarkCameraInfoSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.WatermarkSettings, kotlin.Unit> onSettingsChanged) {
    }
}