// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.borderwater;

import com.qxyu.yucram.domain.repository.PhotoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BorderWatermarkViewModel_Factory implements Factory<BorderWatermarkViewModel> {
  private final Provider<PhotoRepository> photoRepositoryProvider;

  public BorderWatermarkViewModel_Factory(Provider<PhotoRepository> photoRepositoryProvider) {
    this.photoRepositoryProvider = photoRepositoryProvider;
  }

  @Override
  public BorderWatermarkViewModel get() {
    return newInstance(photoRepositoryProvider.get());
  }

  public static BorderWatermarkViewModel_Factory create(
      Provider<PhotoRepository> photoRepositoryProvider) {
    return new BorderWatermarkViewModel_Factory(photoRepositoryProvider);
  }

  public static BorderWatermarkViewModel newInstance(PhotoRepository photoRepository) {
    return new BorderWatermarkViewModel(photoRepository);
  }
}
