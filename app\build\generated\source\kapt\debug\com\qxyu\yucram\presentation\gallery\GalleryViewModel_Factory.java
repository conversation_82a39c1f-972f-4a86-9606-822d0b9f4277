// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.gallery;

import com.qxyu.yucram.domain.repository.AlbumRepository;
import com.qxyu.yucram.domain.repository.PhotoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GalleryViewModel_Factory implements Factory<GalleryViewModel> {
  private final Provider<PhotoRepository> photoRepositoryProvider;

  private final Provider<AlbumRepository> albumRepositoryProvider;

  public GalleryViewModel_Factory(Provider<PhotoRepository> photoRepositoryProvider,
      Provider<AlbumRepository> albumRepositoryProvider) {
    this.photoRepositoryProvider = photoRepositoryProvider;
    this.albumRepositoryProvider = albumRepositoryProvider;
  }

  @Override
  public GalleryViewModel get() {
    return newInstance(photoRepositoryProvider.get(), albumRepositoryProvider.get());
  }

  public static GalleryViewModel_Factory create(Provider<PhotoRepository> photoRepositoryProvider,
      Provider<AlbumRepository> albumRepositoryProvider) {
    return new GalleryViewModel_Factory(photoRepositoryProvider, albumRepositoryProvider);
  }

  public static GalleryViewModel newInstance(PhotoRepository photoRepository,
      AlbumRepository albumRepository) {
    return new GalleryViewModel(photoRepository, albumRepository);
  }
}
