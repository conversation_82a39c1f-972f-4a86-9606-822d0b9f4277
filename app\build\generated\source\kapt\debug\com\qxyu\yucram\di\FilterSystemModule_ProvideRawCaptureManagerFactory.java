// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import android.content.Context;
import com.qxyu.yucram.camera.RawCaptureManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSystemModule_ProvideRawCaptureManagerFactory implements Factory<RawCaptureManager> {
  private final Provider<Context> contextProvider;

  public FilterSystemModule_ProvideRawCaptureManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public RawCaptureManager get() {
    return provideRawCaptureManager(contextProvider.get());
  }

  public static FilterSystemModule_ProvideRawCaptureManagerFactory create(
      Provider<Context> contextProvider) {
    return new FilterSystemModule_ProvideRawCaptureManagerFactory(contextProvider);
  }

  public static RawCaptureManager provideRawCaptureManager(Context context) {
    return Preconditions.checkNotNullFromProvides(FilterSystemModule.INSTANCE.provideRawCaptureManager(context));
  }
}
