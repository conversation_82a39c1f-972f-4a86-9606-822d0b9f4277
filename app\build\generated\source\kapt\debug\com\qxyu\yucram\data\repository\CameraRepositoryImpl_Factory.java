// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.data.repository;

import android.content.Context;
import android.content.SharedPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CameraRepositoryImpl_Factory implements Factory<CameraRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<SharedPreferences> sharedPreferencesProvider;

  public CameraRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<SharedPreferences> sharedPreferencesProvider) {
    this.contextProvider = contextProvider;
    this.sharedPreferencesProvider = sharedPreferencesProvider;
  }

  @Override
  public CameraRepositoryImpl get() {
    return newInstance(contextProvider.get(), sharedPreferencesProvider.get());
  }

  public static CameraRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<SharedPreferences> sharedPreferencesProvider) {
    return new CameraRepositoryImpl_Factory(contextProvider, sharedPreferencesProvider);
  }

  public static CameraRepositoryImpl newInstance(Context context,
      SharedPreferences sharedPreferences) {
    return new CameraRepositoryImpl(context, sharedPreferences);
  }
}
