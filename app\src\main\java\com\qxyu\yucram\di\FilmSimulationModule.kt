package com.qxyu.yucram.di

import android.content.Context
import com.qxyu.yucram.filter.DeviceCapabilityDetector
import com.qxyu.yucram.filter.ImageProcessingPipeline
import com.qxyu.yucram.filter.LutFileManager
import com.qxyu.yucram.film.LutProcessor
import com.qxyu.yucram.processing.RawToLogProcessor
import com.qxyu.yucram.camera.RawCaptureManager
import com.qxyu.yucram.domain.repository.PhotoRepository
import com.qxyu.yucram.domain.repository.AlbumRepository
import com.qxyu.yucram.data.repository.PhotoRepositoryImpl
import com.qxyu.yucram.data.repository.AlbumRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 滤镜系统依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object FilterSystemModule {

    @Provides
    @Singleton
    fun provideDeviceCapabilityDetector(
        @ApplicationContext context: Context
    ): DeviceCapabilityDetector {
        return DeviceCapabilityDetector(context)
    }

    @Provides
    @Singleton
    fun provideLutFileManager(
        @ApplicationContext context: Context
    ): LutFileManager {
        return LutFileManager(context)
    }

    @Provides
    @Singleton
    fun provideLutProcessor(
        @ApplicationContext context: Context
    ): LutProcessor {
        return LutProcessor(context)
    }

    @Provides
    @Singleton
    fun provideRawToLogProcessor(
        @ApplicationContext context: Context
    ): RawToLogProcessor {
        return RawToLogProcessor(context)
    }

    @Provides
    @Singleton
    fun provideRawCaptureManager(
        @ApplicationContext context: Context
    ): RawCaptureManager {
        return RawCaptureManager(context)
    }

    @Provides
    @Singleton
    fun providePhotoRepository(
        photoRepositoryImpl: PhotoRepositoryImpl
    ): PhotoRepository = photoRepositoryImpl

    @Provides
    @Singleton
    fun provideAlbumRepository(
        albumRepositoryImpl: AlbumRepositoryImpl
    ): AlbumRepository = albumRepositoryImpl

    @Provides
    @Singleton
    fun provideImageProcessingPipeline(
        @ApplicationContext context: Context,
        lutProcessor: LutProcessor,
        deviceCapabilityDetector: DeviceCapabilityDetector,
        rawToLogProcessor: RawToLogProcessor,
        rawCaptureManager: RawCaptureManager
    ): ImageProcessingPipeline {
        return ImageProcessingPipeline(context, lutProcessor, deviceCapabilityDetector, rawToLogProcessor, rawCaptureManager)
    }
}
