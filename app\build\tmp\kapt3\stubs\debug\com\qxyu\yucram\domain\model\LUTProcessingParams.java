package com.qxyu.yucram.domain.model;

/**
 * LUT处理参数
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0002\b(\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B}\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\n\u0012\b\b\u0002\u0010\f\u001a\u00020\n\u0012\b\b\u0002\u0010\r\u001a\u00020\n\u0012\b\b\u0002\u0010\u000e\u001a\u00020\n\u0012\b\b\u0002\u0010\u000f\u001a\u00020\n\u0012\b\b\u0002\u0010\u0010\u001a\u00020\b\u0012\b\b\u0002\u0010\u0011\u001a\u00020\b\u00a2\u0006\u0002\u0010\u0012J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\nH\u00c6\u0003J\t\u0010%\u001a\u00020\bH\u00c6\u0003J\t\u0010&\u001a\u00020\bH\u00c6\u0003J\t\u0010\'\u001a\u00020\u0003H\u00c6\u0003J\t\u0010(\u001a\u00020\u0006H\u00c6\u0003J\t\u0010)\u001a\u00020\bH\u00c6\u0003J\t\u0010*\u001a\u00020\nH\u00c6\u0003J\t\u0010+\u001a\u00020\nH\u00c6\u0003J\t\u0010,\u001a\u00020\nH\u00c6\u0003J\t\u0010-\u001a\u00020\nH\u00c6\u0003J\t\u0010.\u001a\u00020\nH\u00c6\u0003J\u0081\u0001\u0010/\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\n2\b\b\u0002\u0010\r\u001a\u00020\n2\b\b\u0002\u0010\u000e\u001a\u00020\n2\b\b\u0002\u0010\u000f\u001a\u00020\n2\b\b\u0002\u0010\u0010\u001a\u00020\b2\b\b\u0002\u0010\u0011\u001a\u00020\bH\u00c6\u0001J\u0013\u00100\u001a\u00020\b2\b\u00101\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00102\u001a\u000203H\u00d6\u0001J\t\u00104\u001a\u000205H\u00d6\u0001R\u0011\u0010\f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0014R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0014R\u0011\u0010\u0010\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0016R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001bR\u0011\u0010\r\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0014R\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0016R\u0011\u0010\u000e\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0014R\u0011\u0010\u000f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0014\u00a8\u00066"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTProcessingParams;", "", "inputColorSpace", "Lcom/qxyu/yucram/domain/model/ColorProfile;", "outputColorSpace", "interpolationMethod", "Lcom/qxyu/yucram/domain/model/InterpolationMethod;", "ditherEnabled", "", "gammaCorrection", "", "exposureAdjustment", "contrastAdjustment", "saturationAdjustment", "temperatureAdjustment", "tintAdjustment", "highlightProtection", "shadowRecovery", "(Lcom/qxyu/yucram/domain/model/ColorProfile;Lcom/qxyu/yucram/domain/model/ColorProfile;Lcom/qxyu/yucram/domain/model/InterpolationMethod;ZFFFFFFZZ)V", "getContrastAdjustment", "()F", "getDitherEnabled", "()Z", "getExposureAdjustment", "getGammaCorrection", "getHighlightProtection", "getInputColorSpace", "()Lcom/qxyu/yucram/domain/model/ColorProfile;", "getInterpolationMethod", "()Lcom/qxyu/yucram/domain/model/InterpolationMethod;", "getOutputColorSpace", "getSaturationAdjustment", "getShadowRecovery", "getTemperatureAdjustment", "getTintAdjustment", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class LUTProcessingParams {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ColorProfile inputColorSpace = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ColorProfile outputColorSpace = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.InterpolationMethod interpolationMethod = null;
    private final boolean ditherEnabled = false;
    private final float gammaCorrection = 0.0F;
    private final float exposureAdjustment = 0.0F;
    private final float contrastAdjustment = 0.0F;
    private final float saturationAdjustment = 0.0F;
    private final float temperatureAdjustment = 0.0F;
    private final float tintAdjustment = 0.0F;
    private final boolean highlightProtection = false;
    private final boolean shadowRecovery = false;
    
    public LUTProcessingParams(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorProfile inputColorSpace, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorProfile outputColorSpace, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.InterpolationMethod interpolationMethod, boolean ditherEnabled, float gammaCorrection, float exposureAdjustment, float contrastAdjustment, float saturationAdjustment, float temperatureAdjustment, float tintAdjustment, boolean highlightProtection, boolean shadowRecovery) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorProfile getInputColorSpace() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorProfile getOutputColorSpace() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.InterpolationMethod getInterpolationMethod() {
        return null;
    }
    
    public final boolean getDitherEnabled() {
        return false;
    }
    
    public final float getGammaCorrection() {
        return 0.0F;
    }
    
    public final float getExposureAdjustment() {
        return 0.0F;
    }
    
    public final float getContrastAdjustment() {
        return 0.0F;
    }
    
    public final float getSaturationAdjustment() {
        return 0.0F;
    }
    
    public final float getTemperatureAdjustment() {
        return 0.0F;
    }
    
    public final float getTintAdjustment() {
        return 0.0F;
    }
    
    public final boolean getHighlightProtection() {
        return false;
    }
    
    public final boolean getShadowRecovery() {
        return false;
    }
    
    public LUTProcessingParams() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorProfile component1() {
        return null;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorProfile component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.InterpolationMethod component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final float component6() {
        return 0.0F;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTProcessingParams copy(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorProfile inputColorSpace, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorProfile outputColorSpace, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.InterpolationMethod interpolationMethod, boolean ditherEnabled, float gammaCorrection, float exposureAdjustment, float contrastAdjustment, float saturationAdjustment, float temperatureAdjustment, float tintAdjustment, boolean highlightProtection, boolean shadowRecovery) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}