package com.qxyu.yucram.presentation.camera

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.DrawScope

/**
 * 网格线覆盖组件
 * 在相机预览上显示九宫格网格线
 */
@Composable
fun GridOverlay(
    isVisible: Boolean,
    modifier: Modifier = Modifier,
    color: Color = Color.White.copy(alpha = 0.5f),
    strokeWidth: Float = 1.5f
) {
    if (!isVisible) return
    
    Canvas(
        modifier = modifier.fillMaxSize()
    ) {
        drawGrid(
            color = color,
            strokeWidth = strokeWidth
        )
    }
}

/**
 * 绘制九宫格网格线
 */
private fun DrawScope.drawGrid(
    color: Color,
    strokeWidth: Float
) {
    val width = size.width
    val height = size.height
    
    // 计算网格线位置（三等分）
    val verticalLine1X = width / 3f
    val verticalLine2X = width * 2f / 3f
    val horizontalLine1Y = height / 3f
    val horizontalLine2Y = height * 2f / 3f
    
    // 设置虚线效果
    val pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 5f), 0f)
    
    // 绘制垂直线
    drawLine(
        color = color,
        start = Offset(verticalLine1X, 0f),
        end = Offset(verticalLine1X, height),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
    
    drawLine(
        color = color,
        start = Offset(verticalLine2X, 0f),
        end = Offset(verticalLine2X, height),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
    
    // 绘制水平线
    drawLine(
        color = color,
        start = Offset(0f, horizontalLine1Y),
        end = Offset(width, horizontalLine1Y),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
    
    drawLine(
        color = color,
        start = Offset(0f, horizontalLine2Y),
        end = Offset(width, horizontalLine2Y),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
}

/**
 * 黄金分割网格线覆盖组件
 */
@Composable
fun GoldenRatioGridOverlay(
    isVisible: Boolean,
    modifier: Modifier = Modifier,
    color: Color = Color.Yellow.copy(alpha = 0.4f),
    strokeWidth: Float = 1.5f
) {
    if (!isVisible) return
    
    Canvas(
        modifier = modifier.fillMaxSize()
    ) {
        drawGoldenRatioGrid(
            color = color,
            strokeWidth = strokeWidth
        )
    }
}

/**
 * 绘制黄金分割网格线
 */
private fun DrawScope.drawGoldenRatioGrid(
    color: Color,
    strokeWidth: Float
) {
    val width = size.width
    val height = size.height
    
    // 黄金比例 ≈ 0.618
    val goldenRatio = 0.618f
    
    // 计算黄金分割线位置
    val verticalLine1X = width * goldenRatio
    val verticalLine2X = width * (1f - goldenRatio)
    val horizontalLine1Y = height * goldenRatio
    val horizontalLine2Y = height * (1f - goldenRatio)
    
    // 设置点线效果
    val pathEffect = PathEffect.dashPathEffect(floatArrayOf(5f, 10f), 0f)
    
    // 绘制垂直线
    drawLine(
        color = color,
        start = Offset(verticalLine1X, 0f),
        end = Offset(verticalLine1X, height),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
    
    drawLine(
        color = color,
        start = Offset(verticalLine2X, 0f),
        end = Offset(verticalLine2X, height),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
    
    // 绘制水平线
    drawLine(
        color = color,
        start = Offset(0f, horizontalLine1Y),
        end = Offset(width, horizontalLine1Y),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
    
    drawLine(
        color = color,
        start = Offset(0f, horizontalLine2Y),
        end = Offset(width, horizontalLine2Y),
        strokeWidth = strokeWidth,
        cap = StrokeCap.Round,
        pathEffect = pathEffect
    )
}
