package com.qxyu.yucram.camera;

/**
 * RAW捕获结果数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0012\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0010\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u00a2\u0006\u0002\u0010\nJ\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\tH\u00c6\u0003J;\u0010\u0018\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u00c6\u0001J\u0013\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001H\u0096\u0002J\b\u0010\u001c\u001a\u00020\u001dH\u0016J\t\u0010\u001e\u001a\u00020\u001fH\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000eR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012\u00a8\u0006 "}, d2 = {"Lcom/qxyu/yucram/camera/RawCaptureResult;", "", "rawData", "", "dngData", "jpegData", "captureResult", "Landroid/hardware/camera2/CaptureResult;", "timestamp", "", "([B[B[BLandroid/hardware/camera2/CaptureResult;J)V", "getCaptureResult", "()Landroid/hardware/camera2/CaptureResult;", "getDngData", "()[B", "getJpegData", "getRawData", "getTimestamp", "()J", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class RawCaptureResult {
    @org.jetbrains.annotations.NotNull()
    private final byte[] rawData = null;
    @org.jetbrains.annotations.NotNull()
    private final byte[] dngData = null;
    @org.jetbrains.annotations.NotNull()
    private final byte[] jpegData = null;
    @org.jetbrains.annotations.NotNull()
    private final android.hardware.camera2.CaptureResult captureResult = null;
    private final long timestamp = 0L;
    
    public RawCaptureResult(@org.jetbrains.annotations.NotNull()
    byte[] rawData, @org.jetbrains.annotations.NotNull()
    byte[] dngData, @org.jetbrains.annotations.NotNull()
    byte[] jpegData, @org.jetbrains.annotations.NotNull()
    android.hardware.camera2.CaptureResult captureResult, long timestamp) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] getRawData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] getDngData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] getJpegData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.hardware.camera2.CaptureResult getCaptureResult() {
        return null;
    }
    
    public final long getTimestamp() {
        return 0L;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final android.hardware.camera2.CaptureResult component4() {
        return null;
    }
    
    public final long component5() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.camera.RawCaptureResult copy(@org.jetbrains.annotations.NotNull()
    byte[] rawData, @org.jetbrains.annotations.NotNull()
    byte[] dngData, @org.jetbrains.annotations.NotNull()
    byte[] jpegData, @org.jetbrains.annotations.NotNull()
    android.hardware.camera2.CaptureResult captureResult, long timestamp) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}