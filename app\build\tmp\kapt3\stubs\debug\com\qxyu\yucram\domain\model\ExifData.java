package com.qxyu.yucram.domain.model;

/**
 * EXIF数据
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b.\b\u0086\b\u0018\u00002\u00020\u0001B\u00a1\u0001\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010(\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010-\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010/\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015J\u0010\u00100\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0015J\u000b\u00101\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u00102\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003\u00a2\u0006\u0002\u0010!J\u0010\u00103\u001a\u0004\u0018\u00010\rH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001dJ\u000b\u00104\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u00aa\u0001\u00105\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001\u00a2\u0006\u0002\u00106J\u0013\u00107\u001a\u00020\r2\b\u00108\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00109\u001a\u00020\u000bH\u00d6\u0001J\t\u0010:\u001a\u00020\u0003H\u00d6\u0001R\u0015\u0010\b\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0018R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u0015\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\n\n\u0002\u0010\u001e\u001a\u0004\b\u001c\u0010\u001dR\u0015\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010\u0016\u001a\u0004\b\u001f\u0010\u0015R\u0015\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\n\n\u0002\u0010\"\u001a\u0004\b \u0010!R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0018R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0018R\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0018R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0018R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u0018\u00a8\u0006;"}, d2 = {"Lcom/qxyu/yucram/domain/model/ExifData;", "", "cameraMake", "", "cameraModel", "lensModel", "focalLength", "", "aperture", "shutterSpeed", "iso", "", "flash", "", "whiteBalance", "exposureMode", "meteringMode", "colorSpace", "software", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getAperture", "()Ljava/lang/Float;", "Ljava/lang/Float;", "getCameraMake", "()Ljava/lang/String;", "getCameraModel", "getColorSpace", "getExposureMode", "getFlash", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getFocalLength", "getIso", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getLensModel", "getMeteringMode", "getShutterSpeed", "getSoftware", "getWhiteBalance", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcom/qxyu/yucram/domain/model/ExifData;", "equals", "other", "hashCode", "toString", "app_debug"})
public final class ExifData {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String cameraMake = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String cameraModel = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String lensModel = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Float focalLength = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Float aperture = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String shutterSpeed = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer iso = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean flash = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String whiteBalance = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String exposureMode = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String meteringMode = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String colorSpace = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String software = null;
    
    public ExifData(@org.jetbrains.annotations.Nullable()
    java.lang.String cameraMake, @org.jetbrains.annotations.Nullable()
    java.lang.String cameraModel, @org.jetbrains.annotations.Nullable()
    java.lang.String lensModel, @org.jetbrains.annotations.Nullable()
    java.lang.Float focalLength, @org.jetbrains.annotations.Nullable()
    java.lang.Float aperture, @org.jetbrains.annotations.Nullable()
    java.lang.String shutterSpeed, @org.jetbrains.annotations.Nullable()
    java.lang.Integer iso, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean flash, @org.jetbrains.annotations.Nullable()
    java.lang.String whiteBalance, @org.jetbrains.annotations.Nullable()
    java.lang.String exposureMode, @org.jetbrains.annotations.Nullable()
    java.lang.String meteringMode, @org.jetbrains.annotations.Nullable()
    java.lang.String colorSpace, @org.jetbrains.annotations.Nullable()
    java.lang.String software) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCameraMake() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCameraModel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLensModel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float getFocalLength() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float getAperture() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getShutterSpeed() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getIso() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean getFlash() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getWhiteBalance() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getExposureMode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMeteringMode() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getColorSpace() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSoftware() {
        return null;
    }
    
    public ExifData() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ExifData copy(@org.jetbrains.annotations.Nullable()
    java.lang.String cameraMake, @org.jetbrains.annotations.Nullable()
    java.lang.String cameraModel, @org.jetbrains.annotations.Nullable()
    java.lang.String lensModel, @org.jetbrains.annotations.Nullable()
    java.lang.Float focalLength, @org.jetbrains.annotations.Nullable()
    java.lang.Float aperture, @org.jetbrains.annotations.Nullable()
    java.lang.String shutterSpeed, @org.jetbrains.annotations.Nullable()
    java.lang.Integer iso, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean flash, @org.jetbrains.annotations.Nullable()
    java.lang.String whiteBalance, @org.jetbrains.annotations.Nullable()
    java.lang.String exposureMode, @org.jetbrains.annotations.Nullable()
    java.lang.String meteringMode, @org.jetbrains.annotations.Nullable()
    java.lang.String colorSpace, @org.jetbrains.annotations.Nullable()
    java.lang.String software) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}