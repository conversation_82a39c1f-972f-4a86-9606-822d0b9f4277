package com.qxyu.yucram;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;

@OriginatingElement(
    topLevelClass = YucramApplication.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
public interface YucramApplication_GeneratedInjector {
  void injectYucramApplication(YucramApplication yucramApplication);
}
