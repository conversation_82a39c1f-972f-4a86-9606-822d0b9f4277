package com.qxyu.yucram.di

import com.qxyu.yucram.data.repository.CameraRepositoryImpl
import com.qxyu.yucram.data.repository.CropRepositoryImpl
import com.qxyu.yucram.data.repository.SettingsRepositoryImpl
import com.qxyu.yucram.domain.repository.CameraRepository
import com.qxyu.yucram.domain.repository.CropRepository
import com.qxyu.yucram.domain.repository.SettingsRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt模块 - Repository相关依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    @Singleton
    abstract fun bindCameraRepository(
        cameraRepositoryImpl: CameraRepositoryImpl
    ): CameraRepository

    @Binds
    @Singleton
    abstract fun bindSettingsRepository(
        settingsRepositoryImpl: SettingsRepositoryImpl
    ): SettingsRepository

    @Binds
    @Singleton
    abstract fun bindCropRepository(
        cropRepositoryImpl: CropRepositoryImpl
    ): CropRepository
}
