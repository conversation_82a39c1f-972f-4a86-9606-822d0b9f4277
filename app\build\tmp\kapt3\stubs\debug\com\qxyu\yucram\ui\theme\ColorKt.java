package com.qxyu.yucram.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b0\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\u00a8\u00061"}, d2 = {"AccentBlue", "Landroidx/compose/ui/graphics/Color;", "getAccentBlue", "()J", "J", "AccentGreen", "getAccentGreen", "AccentRed", "getAccentRed", "AccentYellow", "getAccentYellow", "BackgroundDark", "getBackgroundDark", "BackgroundLight", "getBackgroundLight", "BackgroundMedium", "getBackgroundMedium", "Black30", "getBlack30", "Black50", "getBlack50", "PrimaryOrange", "getPrimaryOrange", "PrimaryOrangeDark", "getPrimaryOrangeDark", "PrimaryOrangeLight", "getPrimaryOrangeLight", "SecondaryBlack", "getSecondaryBlack", "SecondaryGray", "getSecondaryGray", "SecondaryLightGray", "getSecondaryLightGray", "SurfaceDark", "getSurfaceDark", "SurfaceLight", "getSurfaceLight", "SurfaceMedium", "getSurfaceMedium", "TextPrimary", "getTextPrimary", "TextSecondary", "getTextSecondary", "TextTertiary", "getTextTertiary", "White30", "getWhite30", "White50", "getWhite50", "app_debug"})
public final class ColorKt {
    private static final long PrimaryOrange = 0L;
    private static final long PrimaryOrangeDark = 0L;
    private static final long PrimaryOrangeLight = 0L;
    private static final long SecondaryBlack = 0L;
    private static final long SecondaryGray = 0L;
    private static final long SecondaryLightGray = 0L;
    private static final long BackgroundDark = 0L;
    private static final long BackgroundMedium = 0L;
    private static final long BackgroundLight = 0L;
    private static final long SurfaceDark = 0L;
    private static final long SurfaceMedium = 0L;
    private static final long SurfaceLight = 0L;
    private static final long TextPrimary = 0L;
    private static final long TextSecondary = 0L;
    private static final long TextTertiary = 0L;
    private static final long AccentGreen = 0L;
    private static final long AccentRed = 0L;
    private static final long AccentBlue = 0L;
    private static final long AccentYellow = 0L;
    private static final long Black50 = 0L;
    private static final long Black30 = 0L;
    private static final long White50 = 0L;
    private static final long White30 = 0L;
    
    public static final long getPrimaryOrange() {
        return 0L;
    }
    
    public static final long getPrimaryOrangeDark() {
        return 0L;
    }
    
    public static final long getPrimaryOrangeLight() {
        return 0L;
    }
    
    public static final long getSecondaryBlack() {
        return 0L;
    }
    
    public static final long getSecondaryGray() {
        return 0L;
    }
    
    public static final long getSecondaryLightGray() {
        return 0L;
    }
    
    public static final long getBackgroundDark() {
        return 0L;
    }
    
    public static final long getBackgroundMedium() {
        return 0L;
    }
    
    public static final long getBackgroundLight() {
        return 0L;
    }
    
    public static final long getSurfaceDark() {
        return 0L;
    }
    
    public static final long getSurfaceMedium() {
        return 0L;
    }
    
    public static final long getSurfaceLight() {
        return 0L;
    }
    
    public static final long getTextPrimary() {
        return 0L;
    }
    
    public static final long getTextSecondary() {
        return 0L;
    }
    
    public static final long getTextTertiary() {
        return 0L;
    }
    
    public static final long getAccentGreen() {
        return 0L;
    }
    
    public static final long getAccentRed() {
        return 0L;
    }
    
    public static final long getAccentBlue() {
        return 0L;
    }
    
    public static final long getAccentYellow() {
        return 0L;
    }
    
    public static final long getBlack50() {
        return 0L;
    }
    
    public static final long getBlack30() {
        return 0L;
    }
    
    public static final long getWhite50() {
        return 0L;
    }
    
    public static final long getWhite30() {
        return 0L;
    }
}