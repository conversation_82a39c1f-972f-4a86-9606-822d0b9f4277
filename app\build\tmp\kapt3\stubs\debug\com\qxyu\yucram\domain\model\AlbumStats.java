package com.qxyu.yucram.domain.model;

/**
 * 相册统计信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0019\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BS\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\b\u0010\t\u001a\u0004\u0018\u00010\b\u0012\u0006\u0010\n\u001a\u00020\u0006\u0012\u0006\u0010\u000b\u001a\u00020\u0003\u0012\u0006\u0010\f\u001a\u00020\u0003\u0012\b\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\t\u0010\"\u001a\u00020\u0006H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003Ji\u0010&\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00032\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001J\u0013\u0010\'\u001a\u00020(2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020\u0003H\u00d6\u0001J\t\u0010+\u001a\u00020\u000eH\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\t\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0013R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0013\u00a8\u0006,"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumStats;", "", "photoCount", "", "videoCount", "totalSize", "", "oldestPhoto", "Ljava/time/LocalDateTime;", "newestPhoto", "averageFileSize", "yucramPhotoCount", "favoriteCount", "mostUsedFilter", "", "(IIJLjava/time/LocalDateTime;Ljava/time/LocalDateTime;JIILjava/lang/String;)V", "getAverageFileSize", "()J", "getFavoriteCount", "()I", "getMostUsedFilter", "()Ljava/lang/String;", "getNewestPhoto", "()Ljava/time/LocalDateTime;", "getOldestPhoto", "getPhotoCount", "getTotalSize", "getVideoCount", "getYucramPhotoCount", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class AlbumStats {
    private final int photoCount = 0;
    private final int videoCount = 0;
    private final long totalSize = 0L;
    @org.jetbrains.annotations.Nullable()
    private final java.time.LocalDateTime oldestPhoto = null;
    @org.jetbrains.annotations.Nullable()
    private final java.time.LocalDateTime newestPhoto = null;
    private final long averageFileSize = 0L;
    private final int yucramPhotoCount = 0;
    private final int favoriteCount = 0;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String mostUsedFilter = null;
    
    public AlbumStats(int photoCount, int videoCount, long totalSize, @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime oldestPhoto, @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime newestPhoto, long averageFileSize, int yucramPhotoCount, int favoriteCount, @org.jetbrains.annotations.Nullable()
    java.lang.String mostUsedFilter) {
        super();
    }
    
    public final int getPhotoCount() {
        return 0;
    }
    
    public final int getVideoCount() {
        return 0;
    }
    
    public final long getTotalSize() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime getOldestPhoto() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime getNewestPhoto() {
        return null;
    }
    
    public final long getAverageFileSize() {
        return 0L;
    }
    
    public final int getYucramPhotoCount() {
        return 0;
    }
    
    public final int getFavoriteCount() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMostUsedFilter() {
        return null;
    }
    
    public final int component1() {
        return 0;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final long component3() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime component5() {
        return null;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.AlbumStats copy(int photoCount, int videoCount, long totalSize, @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime oldestPhoto, @org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime newestPhoto, long averageFileSize, int yucramPhotoCount, int favoriteCount, @org.jetbrains.annotations.Nullable()
    java.lang.String mostUsedFilter) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}