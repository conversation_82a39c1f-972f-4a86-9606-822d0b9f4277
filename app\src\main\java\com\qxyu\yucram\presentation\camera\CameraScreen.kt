package com.qxyu.yucram.presentation.camera

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material.icons.outlined.*
import androidx.compose.ui.unit.Dp

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import com.qxyu.yucram.camera.CameraManager
import com.qxyu.yucram.camera.CameraState
import com.qxyu.yucram.domain.model.FlashMode
import com.qxyu.yucram.domain.model.FilterPreset
import com.qxyu.yucram.presentation.filter.FilterSelectionScreen

/**
 * 相机主界面
 */
@Composable
fun CameraScreen(
    modifier: Modifier = Modifier,
    viewModel: CameraViewModel = hiltViewModel()
) {
    // 临时使用直接创建，后续优化为Hilt注入
    val context = LocalContext.current
    val cameraManager = remember {
        com.qxyu.yucram.camera.CameraManager(context, null)
    }
    val cameraSettings by viewModel.cameraSettings.collectAsState()
    val uiState by viewModel.uiState.collectAsState()
    val cameraState by cameraManager.cameraState.collectAsState()

    val isCapturing = cameraState == CameraState.CAPTURING

    // 滤镜选择状态
    var showFilterSelection by remember { mutableStateOf(false) }
    var selectedFilter by remember { mutableStateOf<FilterPreset?>(null) }

    // 三段式全屏布局
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 1. 顶部功能区
        TopFunctionBar(
            flashMode = cameraSettings.flashMode,
            isHdrEnabled = cameraSettings.isHdrEnabled,
            isRawEnabled = cameraSettings.isRawEnabled,
            onFlashToggle = viewModel::toggleFlashMode,
            onHdrToggle = viewModel::toggleHdrMode,
            onRawToggle = viewModel::toggleRawFormat,
            onSettingsClick = { /* TODO: 导航到设置 */ },
            modifier = Modifier.fillMaxWidth()
        )

        // 2. 中部预览区域 (主要区域)
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f) // 占据剩余空间
        ) {
            // 相机预览 (全屏)
            CameraPreview(
                cameraManager = cameraManager,
                modifier = Modifier.fillMaxSize()
            )

            // 网格线覆盖
            GridOverlay(
                isVisible = cameraSettings.isGridEnabled,
                modifier = Modifier.fillMaxSize()
            )

            // 右侧参数控制 (半透明悬浮)
            RightSideControls(
                onCameraSwitchClick = { /* TODO: 切换摄像头 */ },
                onZoomClick = { /* TODO: 变焦控制 */ },
                onExposureClick = { /* TODO: 曝光补偿 */ },
                onWhiteBalanceClick = { /* TODO: 白平衡 */ },
                onIsoClick = { /* TODO: ISO调节 */ },
                modifier = Modifier.align(Alignment.CenterEnd)
            )
        }

        // 3. 底部操作区
        BottomActionBar(
            isCapturing = isCapturing,
            onFilterClick = { showFilterSelection = true },
            onCaptureClick = {
                // 如果选择了滤镜，使用专业RAW拍摄
                selectedFilter?.let { filter ->
                    cameraManager.capturePhotoWithFilter(filter)
                } ?: run {
                    // 否则使用普通拍摄
                    cameraManager.capturePhoto()
                }
                viewModel.capturePhoto()
            },
            onGalleryClick = { /* TODO: 打开图库 */ },
            modifier = Modifier.fillMaxWidth()
        )
    }

    // 滤镜选择弹窗
    if (showFilterSelection) {
        FilterSelectionScreen(
            onFilterSelected = { filter ->
                // 保存选中的滤镜，用于专业RAW拍摄
                selectedFilter = filter
                showFilterSelection = false
            },
            onDismiss = {
                showFilterSelection = false
            }
        )
    }
}

/**
 * 顶部功能栏 (闪光灯、HDR、RAW、设置)
 */
@Composable
private fun TopFunctionBar(
    flashMode: FlashMode,
    isHdrEnabled: Boolean,
    isRawEnabled: Boolean,
    onFlashToggle: () -> Unit,
    onHdrToggle: () -> Unit,
    onRawToggle: () -> Unit,
    onSettingsClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp)
            .background(
                Color.Black.copy(alpha = 0.7f),
                RoundedCornerShape(24.dp)
            )
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 闪光灯按钮
        FunctionButton(
            icon = when (flashMode) {
                FlashMode.ON -> Icons.Filled.FlashOn
                FlashMode.AUTO -> Icons.Filled.FlashAuto
                FlashMode.TORCH -> Icons.Filled.FlashOn
                else -> Icons.Filled.FlashOff
            },
            isActive = flashMode != FlashMode.OFF,
            onClick = onFlashToggle,
            contentDescription = "闪光灯"
        )

        // HDR按钮
        FunctionButton(
            icon = if (isHdrEnabled) Icons.Filled.Hd else Icons.Outlined.Hd,
            isActive = isHdrEnabled,
            onClick = onHdrToggle,
            contentDescription = "HDR"
        )

        // RAW按钮
        FunctionButton(
            icon = if (isRawEnabled) Icons.Filled.HighQuality else Icons.Outlined.HighQuality,
            isActive = isRawEnabled,
            onClick = onRawToggle,
            contentDescription = "RAW格式"
        )

        // 设置按钮
        FunctionButton(
            icon = Icons.Filled.Settings,
            isActive = false,
            onClick = onSettingsClick,
            contentDescription = "设置"
        )
    }
}

/**
 * 功能按钮组件
 */
@Composable
private fun FunctionButton(
    icon: ImageVector,
    isActive: Boolean,
    onClick: () -> Unit,
    contentDescription: String,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        modifier = modifier.size(48.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = if (isActive) MaterialTheme.colorScheme.primary else Color.White,
            modifier = Modifier.size(24.dp)
        )
    }
}

/**
 * 右侧参数控制栏 (半透明悬浮)
 */
@Composable
private fun RightSideControls(
    onCameraSwitchClick: () -> Unit,
    onZoomClick: () -> Unit,
    onExposureClick: () -> Unit,
    onWhiteBalanceClick: () -> Unit,
    onIsoClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .padding(end = 16.dp)
            .background(
                Color.Black.copy(alpha = 0.6f),
                RoundedCornerShape(20.dp)
            )
            .padding(vertical = 8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 前后摄像头切换
        SideControlButton(
            icon = Icons.Filled.FlipCameraAndroid,
            onClick = onCameraSwitchClick,
            contentDescription = "切换摄像头"
        )

        // 变焦控制
        SideControlButton(
            icon = Icons.Filled.ZoomIn,
            onClick = onZoomClick,
            contentDescription = "变焦"
        )

        // 曝光补偿
        SideControlButton(
            icon = Icons.Filled.Brightness6,
            onClick = onExposureClick,
            contentDescription = "曝光"
        )

        // 白平衡
        SideControlButton(
            icon = Icons.Filled.WbSunny,
            onClick = onWhiteBalanceClick,
            contentDescription = "白平衡"
        )

        // ISO调节
        SideControlButton(
            icon = Icons.Filled.Tune,
            onClick = onIsoClick,
            contentDescription = "ISO"
        )
    }
}

/**
 * 侧边控制按钮
 */
@Composable
private fun SideControlButton(
    icon: ImageVector,
    onClick: () -> Unit,
    contentDescription: String,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        modifier = modifier.size(44.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = Color.White.copy(alpha = 0.9f),
            modifier = Modifier.size(20.dp)
        )
    }
}

/**
 * 底部操作栏 (滤镜、拍摄、图库)
 */
@Composable
private fun BottomActionBar(
    isCapturing: Boolean,
    onFilterClick: () -> Unit,
    onCaptureClick: () -> Unit,
    onGalleryClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp, vertical = 16.dp)
            .background(
                Color.Black.copy(alpha = 0.7f),
                RoundedCornerShape(28.dp)
            )
            .padding(horizontal = 24.dp, vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图库按钮
        ActionButton(
            icon = Icons.Filled.PhotoLibrary,
            onClick = onGalleryClick,
            contentDescription = "图库",
            size = 56.dp
        )

        // 拍摄按钮 (超大)
        SuperCaptureButton(
            isCapturing = isCapturing,
            onClick = onCaptureClick
        )

        // 滤镜按钮
        ActionButton(
            icon = Icons.Filled.Palette,
            onClick = onFilterClick,
            contentDescription = "滤镜",
            size = 56.dp
        )
    }
}

/**
 * 操作按钮组件
 */
@Composable
private fun ActionButton(
    icon: ImageVector,
    onClick: () -> Unit,
    contentDescription: String,
    size: Dp = 48.dp,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        modifier = modifier.size(size)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = Color.White,
            modifier = Modifier.size(size * 0.5f)
        )
    }
}

/**
 * 超大拍摄按钮
 */
@Composable
private fun SuperCaptureButton(
    isCapturing: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val scale by animateFloatAsState(
        targetValue = if (isCapturing) 0.9f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "capture_button_scale"
    )

    FloatingActionButton(
        onClick = {
            if (!isCapturing) {
                onClick()
            }
        },
        modifier = modifier
            .size(80.dp)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            },
        containerColor = Color.White,
        contentColor = Color.Black,
        elevation = FloatingActionButtonDefaults.elevation(
            defaultElevation = if (isCapturing) 2.dp else 8.dp
        )
    ) {
        if (isCapturing) {
            CircularProgressIndicator(
                modifier = Modifier.size(32.dp),
                color = Color.Black,
                strokeWidth = 3.dp
            )
        } else {
            Icon(
                imageVector = Icons.Filled.CameraAlt,
                contentDescription = "拍摄",
                modifier = Modifier.size(36.dp)
            )
        }
    }
}

/**
 * 顶部控制栏
 */
@Composable
private fun TopControlBar(
    flashMode: FlashMode,
    isGridEnabled: Boolean,
    isRawEnabled: Boolean,
    isHdrEnabled: Boolean,
    onFlashToggle: () -> Unit,
    onGridToggle: () -> Unit,
    onRawToggle: () -> Unit,
    onHdrToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 闪光灯按钮
        IconButton(
            onClick = onFlashToggle,
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(Color.Black.copy(alpha = 0.3f))
        ) {
            Icon(
                imageVector = when (flashMode) {
                    FlashMode.ON -> Icons.Filled.FlashOn
                    FlashMode.AUTO -> Icons.Filled.FlashAuto
                    FlashMode.TORCH -> Icons.Filled.FlashOn
                    else -> Icons.Filled.FlashOff
                },
                contentDescription = "闪光灯",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // 网格线按钮
        IconButton(
            onClick = onGridToggle,
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(
                    if (isGridEnabled) MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    else Color.Black.copy(alpha = 0.3f)
                )
        ) {
            Icon(
                imageVector = if (isGridEnabled) Icons.Filled.GridOn else Icons.Outlined.GridOn,
                contentDescription = "网格线",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        // RAW格式按钮
        IconButton(
            onClick = onRawToggle,
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(
                    if (isRawEnabled) MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    else Color.Black.copy(alpha = 0.3f)
                )
        ) {
            Icon(
                imageVector = if (isRawEnabled) Icons.Filled.HighQuality else Icons.Outlined.HighQuality,
                contentDescription = "RAW格式",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        // HDR按钮
        IconButton(
            onClick = onHdrToggle,
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(
                    if (isHdrEnabled) MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
                    else Color.Black.copy(alpha = 0.3f)
                )
        ) {
            Icon(
                imageVector = if (isHdrEnabled) Icons.Filled.Hd else Icons.Outlined.Hd,
                contentDescription = "HDR模式",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * 底部控制栏
 */
@Composable
private fun BottomControlBar(
    isCapturing: Boolean,
    onCaptureClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 图库按钮（占位）
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(CircleShape)
                .background(Color.Black.copy(alpha = 0.3f)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "图库",
                color = Color.White,
                style = MaterialTheme.typography.labelSmall
            )
        }

        // 拍摄按钮
        CaptureButton(
            isCapturing = isCapturing,
            onCaptureClick = onCaptureClick,
            modifier = Modifier.size(80.dp)
        )

        // 滤镜按钮（占位）
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(CircleShape)
                .background(Color.Black.copy(alpha = 0.3f)),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "滤镜",
                color = Color.White,
                style = MaterialTheme.typography.labelSmall
            )
        }
    }
}

/**
 * 拍摄按钮组件
 * 带有动画效果和视觉反馈
 */
@Composable
private fun CaptureButton(
    isCapturing: Boolean,
    onCaptureClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 按钮缩放动画
    val scale by animateFloatAsState(
        targetValue = if (isCapturing) 0.9f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "capture_button_scale"
    )

    // 旋转动画（拍摄时）
    val rotation by animateFloatAsState(
        targetValue = if (isCapturing) 360f else 0f,
        animationSpec = tween(
            durationMillis = 1000,
            easing = LinearEasing
        ),
        label = "capture_button_rotation"
    )

    FloatingActionButton(
        onClick = {
            if (!isCapturing) {
                onCaptureClick()
            }
        },
        modifier = modifier
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
                rotationZ = if (isCapturing) rotation else 0f
            },
        containerColor = if (isCapturing)
            MaterialTheme.colorScheme.primary.copy(alpha = 0.8f)
        else MaterialTheme.colorScheme.primary,
        contentColor = Color.White,
        elevation = FloatingActionButtonDefaults.elevation(
            defaultElevation = if (isCapturing) 2.dp else 6.dp
        )
    ) {
        if (isCapturing) {
            CircularProgressIndicator(
                modifier = Modifier.size(24.dp),
                color = Color.White,
                strokeWidth = 3.dp
            )
        } else {
            Icon(
                imageVector = Icons.Filled.CameraAlt,
                contentDescription = "拍摄",
                modifier = Modifier.size(32.dp)
            )
        }
    }
}
