1app/src/main/java/com/qxyu/yucram/MainActivity.kt.app/src/main/java/com/qxyu/yucram/YucramApp.kt6app/src/main/java/com/qxyu/yucram/YucramApplication.kt9app/src/main/java/com/qxyu/yucram/camera/CameraManager.kt>app/src/main/java/com/qxyu/yucram/data/local/YucramDatabase.kt<app/src/main/java/com/qxyu/yucram/data/local/dao/PhotoDao.kt?app/src/main/java/com/qxyu/yucram/data/local/dao/SettingsDao.ktBapp/src/main/java/com/qxyu/yucram/data/local/entity/PhotoEntity.ktEapp/src/main/java/com/qxyu/yucram/data/local/entity/SettingsEntity.ktIapp/src/main/java/com/qxyu/yucram/data/repository/CameraRepositoryImpl.ktHapp/src/main/java/com/qxyu/yucram/data/repository/PhotoRepositoryImpl.ktKapp/src/main/java/com/qxyu/yucram/data/repository/SettingsRepositoryImpl.kt1app/src/main/java/com/qxyu/yucram/di/AppModule.kt6app/src/main/java/com/qxyu/yucram/di/DatabaseModule.kt<app/src/main/java/com/qxyu/yucram/di/FilmSimulationModule.kt8app/src/main/java/com/qxyu/yucram/di/RepositoryModule.kt@app/src/main/java/com/qxyu/yucram/domain/model/CameraSettings.kt@app/src/main/java/com/qxyu/yucram/domain/model/FilmSimulation.kt8app/src/main/java/com/qxyu/yucram/domain/model/Filter.kt7app/src/main/java/com/qxyu/yucram/domain/model/Photo.ktGapp/src/main/java/com/qxyu/yucram/domain/repository/CameraRepository.ktFapp/src/main/java/com/qxyu/yucram/domain/repository/PhotoRepository.ktIapp/src/main/java/com/qxyu/yucram/domain/repository/SettingsRepository.kt6app/src/main/java/com/qxyu/yucram/film/LutProcessor.ktDapp/src/main/java/com/qxyu/yucram/filter/DeviceCapabilityDetector.ktCapp/src/main/java/com/qxyu/yucram/filter/ImageProcessingPipeline.kt:app/src/main/java/com/qxyu/yucram/filter/LutFileManager.ktBapp/src/main/java/com/qxyu/yucram/navigation/YucramDestinations.kt@app/src/main/java/com/qxyu/yucram/navigation/YucramNavigation.kt;app/src/main/java/com/qxyu/yucram/presentation/YucramApp.ktPapp/src/main/java/com/qxyu/yucram/presentation/animation/NavigationAnimations.ktFapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraPreview.ktEapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraScreen.ktHapp/src/main/java/com/qxyu/yucram/presentation/camera/CameraViewModel.ktDapp/src/main/java/com/qxyu/yucram/presentation/camera/GridOverlay.ktKapp/src/main/java/com/qxyu/yucram/presentation/camera/OppoAdaptationInfo.ktPapp/src/main/java/com/qxyu/yucram/presentation/components/BottomNavigationBar.ktKapp/src/main/java/com/qxyu/yucram/presentation/components/NavigationRail.ktNapp/src/main/java/com/qxyu/yucram/presentation/filter/FilterSelectionScreen.ktQapp/src/main/java/com/qxyu/yucram/presentation/filter/FilterSelectionViewModel.ktGapp/src/main/java/com/qxyu/yucram/presentation/gallery/GalleryScreen.ktMapp/src/main/java/com/qxyu/yucram/presentation/permission/PermissionScreen.ktPapp/src/main/java/com/qxyu/yucram/presentation/permission/PermissionViewModel.ktIapp/src/main/java/com/qxyu/yucram/presentation/settings/SettingsScreen.ktHapp/src/main/java/com/qxyu/yucram/presentation/utils/ResponsiveLayout.kt3app/src/main/java/com/qxyu/yucram/ui/theme/Color.kt3app/src/main/java/com/qxyu/yucram/ui/theme/Theme.kt2app/src/main/java/com/qxyu/yucram/ui/theme/Type.kt8app/src/main/java/com/qxyu/yucram/utils/DeviceAdapter.kt5app/src/main/java/com/qxyu/yucram/utils/ImageSaver.kt:app/src/main/java/com/qxyu/yucram/utils/PermissionUtils.kt=app/src/main/java/com/qxyu/yucram/camera/RawCaptureManager.ktAapp/src/main/java/com/qxyu/yucram/processing/RawToLogProcessor.ktDapp/src/main/java/com/qxyu/yucram/data/local/converter/Converters.kt<app/src/main/java/com/qxyu/yucram/data/local/dao/AlbumDao.ktHapp/src/main/java/com/qxyu/yucram/data/repository/AlbumRepositoryImpl.kt7app/src/main/java/com/qxyu/yucram/domain/model/Album.ktFapp/src/main/java/com/qxyu/yucram/domain/repository/AlbumRepository.ktJapp/src/main/java/com/qxyu/yucram/presentation/gallery/GalleryViewModel.ktRapp/src/main/java/com/qxyu/yucram/presentation/gallery/components/GalleryTopBar.ktNapp/src/main/java/com/qxyu/yucram/presentation/gallery/components/PhotoGrid.kt;app/src/main/java/com/qxyu/yucram/domain/model/PhotoEdit.ktIapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumDetailScreen.ktGapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumListScreen.ktJapp/src/main/java/com/qxyu/yucram/presentation/album/AlbumListViewModel.ktHapp/src/main/java/com/qxyu/yucram/presentation/album/SmartAlbumScreen.ktKapp/src/main/java/com/qxyu/yucram/presentation/album/SmartAlbumViewModel.ktOapp/src/main/java/com/qxyu/yucram/presentation/photodetail/PhotoDetailScreen.ktRapp/src/main/java/com/qxyu/yucram/presentation/photodetail/PhotoDetailViewModel.kt\app/src/main/java/com/qxyu/yucram/presentation/photodetail/components/ExifInfoBottomSheet.ktKapp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditScreen.ktNapp/src/main/java/com/qxyu/yucram/presentation/photoedit/PhotoEditViewModel.kt[app/src/main/java/com/qxyu/yucram/presentation/photoedit/components/BasicAdjustmentPanel.kt[app/src/main/java/com/qxyu/yucram/presentation/photoedit/components/CurveAdjustmentPanel.ktTapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/EditToolPanel.ktYapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/HSLAdjustmentPanel.ktWapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/PhotoEditPreview.ktSapp/src/main/java/com/qxyu/yucram/presentation/photoedit/components/SliderPanels.ktDapp/src/main/java/com/qxyu/yucram/domain/model/BorderAndWatermark.ktSapp/src/main/java/com/qxyu/yucram/presentation/borderwater/BorderWatermarkScreen.ktVapp/src/main/java/com/qxyu/yucram/presentation/borderwater/BorderWatermarkViewModel.ktXapp/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderEditPanel.ktaapp/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderWatermarkEditPanel.kt_app/src/main/java/com/qxyu/yucram/presentation/borderwater/components/BorderWatermarkPreview.kt[app/src/main/java/com/qxyu/yucram/presentation/borderwater/components/WatermarkEditPanel.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     