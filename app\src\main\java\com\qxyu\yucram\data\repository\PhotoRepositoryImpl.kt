package com.qxyu.yucram.data.repository

import androidx.paging.PagingData
import com.qxyu.yucram.data.local.dao.PhotoDao
import com.qxyu.yucram.data.local.entity.PhotoEntity
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.PhotoRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.time.LocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 照片Repository实现（简化版本，兼容现有数据库）
 */
@Singleton
class PhotoRepositoryImpl @Inject constructor(
    private val photoDao: PhotoDao
) : PhotoRepository {
    
    // ========== 基础操作 ==========
    
    override fun getAllPhotosPaged(): Flow<PagingData<Photo>> {
        // TODO: 实现分页功能
        throw NotImplementedError("分页功能待实现")
    }
    
    override fun getAllPhotos(): Flow<List<Photo>> {
        return photoDao.getAllPhotos().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override suspend fun getPhotoById(photoId: String): Photo? {
        val id = photoId.toLongOrNull() ?: return null
        return photoDao.getPhotoById(id)?.toDomainModel()
    }
    
    override suspend fun getPhotosByIds(photoIds: List<String>): List<Photo> {
        return emptyList()
    }
    
    override suspend fun savePhoto(photo: Photo): Result<Photo> {
        return try {
            val entity = photo.toEntity()
            val id = photoDao.insertPhoto(entity)
            Result.success(photo.copy(id = id.toString()))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun savePhotos(photos: List<Photo>): Result<List<Photo>> {
        return try {
            val entities = photos.map { it.toEntity() }
            photoDao.insertPhotos(entities)
            Result.success(photos)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updatePhoto(photo: Photo): Result<Photo> {
        return try {
            val entity = photo.toEntity()
            photoDao.updatePhoto(entity)
            Result.success(photo)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deletePhoto(photoId: String): Result<Unit> {
        return try {
            val id = photoId.toLongOrNull() ?: return Result.failure(Exception("Invalid photo ID"))
            val photo = photoDao.getPhotoById(id)
            if (photo != null) {
                photoDao.deletePhoto(photo)
                Result.success(Unit)
            } else {
                Result.failure(Exception("Photo not found"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deletePhotos(photoIds: List<String>): Result<Unit> {
        return try {
            photoIds.forEach { photoId ->
                deletePhoto(photoId)
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // ========== 收藏操作 ==========
    
    override suspend fun setFavorite(photoId: String, isFavorite: Boolean): Result<Unit> {
        return try {
            val id = photoId.toLongOrNull() ?: return Result.failure(Exception("Invalid photo ID"))
            val photo = photoDao.getPhotoById(id)
            if (photo != null) {
                val updatedPhoto = photo.copy(isFavorite = isFavorite)
                photoDao.updatePhoto(updatedPhoto)
                Result.success(Unit)
            } else {
                Result.failure(Exception("Photo not found"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override fun getFavoritePhotos(): Flow<List<Photo>> {
        return photoDao.getFavoritePhotos().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    // ========== 筛选和搜索 ==========
    
    override fun getPhotosWithFilter(filter: PhotoFilter): Flow<List<Photo>> {
        return getAllPhotos()
    }
    
    override fun searchPhotos(query: String): Flow<List<Photo>> {
        return photoDao.searchPhotos(query).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    override fun getPhotosByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): Flow<List<Photo>> {
        return getAllPhotos()
    }
    
    override fun getYucramPhotos(): Flow<List<Photo>> {
        return getAllPhotos()
    }
    
    override fun getPhotosByFilter(filterName: String): Flow<List<Photo>> {
        return getAllPhotos()
    }
    
    override fun getPhotosByTag(tag: String): Flow<List<Photo>> {
        return photoDao.searchPhotosByTag(tag).map { entities ->
            entities.map { it.toDomainModel() }
        }
    }
    
    // ========== 统计信息 ==========
    
    override suspend fun getPhotoStats(): PhotoStats {
        val count = photoDao.getPhotoCount()
        return PhotoStats(
            totalCount = count,
            yucramPhotoCount = 0,
            favoriteCount = 0,
            totalSize = 0L,
            oldestPhoto = null,
            newestPhoto = null,
            mostUsedFilter = null,
            averageFileSize = 0L
        )
    }
    
    override suspend fun getPhotoCount(): Int {
        return photoDao.getPhotoCount()
    }
    
    override suspend fun getRecentPhotos(limit: Int): List<Photo> {
        return emptyList()
    }
    
    // ========== 文件操作 ==========
    
    override suspend fun scanDevicePhotos(): Result<List<Photo>> {
        return Result.success(emptyList())
    }
    
    override suspend fun isPhotoFileExists(photoId: String): Boolean {
        return false
    }
    
    override suspend fun generateThumbnail(photoId: String): Result<String> {
        return Result.failure(Exception("Not implemented"))
    }
    
    override suspend fun cleanupDeletedPhotos(): Result<Int> {
        return Result.success(0)
    }
    
    // ========== 导入导出 ==========
    
    override suspend fun importPhoto(filePath: String): Result<Photo> {
        return Result.failure(Exception("Not implemented"))
    }
    
    override suspend fun importPhotos(filePaths: List<String>): Result<List<Photo>> {
        return Result.failure(Exception("Not implemented"))
    }
    
    override suspend fun exportPhoto(photoId: String, destinationPath: String): Result<String> {
        return Result.failure(Exception("Not implemented"))
    }
    
    override suspend fun exportPhotos(photoIds: List<String>, destinationPath: String): Result<List<String>> {
        return Result.failure(Exception("Not implemented"))
    }
}

/**
 * PhotoEntity转换为Photo领域模型
 */
private fun PhotoEntity.toDomainModel(): Photo {
    return Photo(
        id = this.id.toString(),
        fileName = this.fileName,
        filePath = this.filePath,
        thumbnailPath = null,
        mimeType = this.mimeType,
        fileSize = this.size,
        width = this.width,
        height = this.height,
        dateAdded = LocalDateTime.now(),
        dateTaken = null,
        dateModified = LocalDateTime.now(),
        orientation = 0,
        location = if (this.latitude != null && this.longitude != null) {
            PhotoLocation(this.latitude, this.longitude)
        } else null,
        exifData = null,
        isYucramPhoto = this.hasFilter,
        filterUsed = this.filterName,
        processingInfo = null,
        isFavorite = this.isFavorite,
        isDeleted = false,
        albumIds = emptyList(),
        tags = if (!this.tags.isNullOrEmpty()) {
            listOf(this.tags)
        } else emptyList()
    )
}

/**
 * Photo领域模型转换为PhotoEntity
 */
private fun Photo.toEntity(): PhotoEntity {
    return PhotoEntity(
        id = this.id.toLongOrNull() ?: 0L,
        filePath = this.filePath,
        fileName = this.fileName,
        dateCreated = System.currentTimeMillis(),
        dateModified = System.currentTimeMillis(),
        size = this.fileSize,
        width = this.width,
        height = this.height,
        mimeType = this.mimeType,
        isRaw = this.processingInfo?.sourceFormat == ImageSourceFormat.RAW,
        hasFilter = this.filterUsed != null,
        filterName = this.filterUsed,
        latitude = this.location?.latitude,
        longitude = this.location?.longitude,
        isFavorite = this.isFavorite,
        tags = this.tags.joinToString(",")
    )
}
