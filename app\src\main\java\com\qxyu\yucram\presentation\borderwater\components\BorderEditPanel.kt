package com.qxyu.yucram.presentation.borderwater.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.*

/**
 * 边框编辑面板
 */
@Composable
fun BorderEditPanel(
    borderSettings: BorderSettings,
    onSettingsChanged: (BorderSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 边框开关
        item {
            BorderToggleSection(
                isEnabled = borderSettings.isEnabled,
                onToggle = { enabled ->
                    onSettingsChanged(borderSettings.copy(isEnabled = enabled))
                }
            )
        }
        
        if (borderSettings.isEnabled) {
            // 边框类型选择
            item {
                BorderTypeSection(
                    selectedType = borderSettings.borderType,
                    onTypeSelected = { type ->
                        onSettingsChanged(borderSettings.copy(borderType = type))
                    }
                )
            }
            
            // 边框样式选择
            item {
                BorderStyleSection(
                    selectedStyle = borderSettings.borderStyle,
                    onStyleSelected = { style ->
                        onSettingsChanged(borderSettings.copy(borderStyle = style))
                    }
                )
            }
            
            // 基础设置
            item {
                BorderBasicSettings(
                    borderSettings = borderSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            // 颜色设置
            item {
                BorderColorSettings(
                    borderSettings = borderSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            // 高级设置
            item {
                BorderAdvancedSettings(
                    borderSettings = borderSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
        }
    }
}

/**
 * 边框开关区域
 */
@Composable
fun BorderToggleSection(
    isEnabled: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column {
            Text(
                text = "边框",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            Text(
                text = if (isEnabled) "已启用" else "已禁用",
                style = MaterialTheme.typography.bodySmall,
                color = if (isEnabled) MaterialTheme.colorScheme.primary else Color.White.copy(alpha = 0.7f)
            )
        }
        
        Switch(
            checked = isEnabled,
            onCheckedChange = onToggle,
            colors = SwitchDefaults.colors(
                checkedThumbColor = MaterialTheme.colorScheme.primary,
                checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
            )
        )
    }
}

/**
 * 边框类型选择
 */
@Composable
fun BorderTypeSection(
    selectedType: BorderType,
    onTypeSelected: (BorderType) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "边框类型",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(BorderType.values()) { type ->
                BorderTypeItem(
                    type = type,
                    isSelected = selectedType == type,
                    onClick = { onTypeSelected(type) }
                )
            }
        }
    }
}

/**
 * 边框类型项
 */
@Composable
fun BorderTypeItem(
    type: BorderType,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(
                    if (isSelected) MaterialTheme.colorScheme.primary
                    else Color.White.copy(alpha = 0.1f)
                )
                .border(
                    width = if (isSelected) 2.dp else 1.dp,
                    color = if (isSelected) MaterialTheme.colorScheme.primary
                    else Color.White.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(8.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = when (type) {
                    BorderType.SOLID -> Icons.Filled.CropFree
                    BorderType.GRADIENT -> Icons.Filled.Gradient
                    BorderType.PATTERN -> Icons.Filled.Pattern
                    BorderType.DECORATIVE -> Icons.Filled.AutoAwesome
                },
                contentDescription = null,
                tint = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(24.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = when (type) {
                BorderType.SOLID -> "纯色"
                BorderType.GRADIENT -> "渐变"
                BorderType.PATTERN -> "图案"
                BorderType.DECORATIVE -> "装饰"
            },
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * 边框样式选择
 */
@Composable
fun BorderStyleSection(
    selectedStyle: BorderStyle,
    onStyleSelected: (BorderStyle) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "边框样式",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(BorderStyle.values()) { style ->
                BorderStyleChip(
                    style = style,
                    isSelected = selectedStyle == style,
                    onClick = { onStyleSelected(style) }
                )
            }
        }
    }
}

/**
 * 边框样式芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BorderStyleChip(
    style: BorderStyle,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = {
            Text(
                text = when (style) {
                    BorderStyle.CLASSIC -> "经典"
                    BorderStyle.MODERN -> "现代"
                    BorderStyle.VINTAGE -> "复古"
                    BorderStyle.ARTISTIC -> "艺术"
                    BorderStyle.POLAROID -> "宝丽来"
                    BorderStyle.FILM -> "胶片"
                    BorderStyle.DIGITAL -> "数字"
                    BorderStyle.ELEGANT -> "优雅"
                },
                style = MaterialTheme.typography.bodySmall
            )
        },
        selected = isSelected,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = Color.White
        ),
        modifier = modifier
    )
}

/**
 * 边框基础设置
 */
@Composable
fun BorderBasicSettings(
    borderSettings: BorderSettings,
    onSettingsChanged: (BorderSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "基础设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        // 边框宽度
        BorderSliderSetting(
            label = "边框宽度",
            value = borderSettings.width,
            valueRange = 1f..100f,
            onValueChanged = { width ->
                onSettingsChanged(borderSettings.copy(width = width))
            },
            formatValue = { "${it.toInt()}dp" }
        )
        
        // 圆角半径
        BorderSliderSetting(
            label = "圆角半径",
            value = borderSettings.cornerRadius,
            valueRange = 0f..50f,
            onValueChanged = { radius ->
                onSettingsChanged(borderSettings.copy(cornerRadius = radius))
            },
            formatValue = { "${it.toInt()}dp" }
        )
    }
}

/**
 * 边框颜色设置
 */
@Composable
fun BorderColorSettings(
    borderSettings: BorderSettings,
    onSettingsChanged: (BorderSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "颜色设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        when (borderSettings.borderType) {
            BorderType.SOLID -> {
                BorderColorPicker(
                    selectedColor = borderSettings.color,
                    onColorSelected = { color ->
                        onSettingsChanged(borderSettings.copy(color = color))
                    }
                )
            }
            
            BorderType.GRADIENT -> {
                BorderGradientSettings(
                    borderSettings = borderSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            BorderType.PATTERN -> {
                BorderPatternSettings(
                    borderSettings = borderSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
            
            BorderType.DECORATIVE -> {
                BorderDecorativeSettings(
                    borderSettings = borderSettings,
                    onSettingsChanged = onSettingsChanged
                )
            }
        }
    }
}

/**
 * 边框高级设置
 */
@Composable
fun BorderAdvancedSettings(
    borderSettings: BorderSettings,
    onSettingsChanged: (BorderSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "高级设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        // 阴影开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "阴影效果",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            Switch(
                checked = borderSettings.shadowEnabled,
                onCheckedChange = { enabled ->
                    onSettingsChanged(borderSettings.copy(shadowEnabled = enabled))
                }
            )
        }
        
        // 阴影设置
        if (borderSettings.shadowEnabled) {
            BorderShadowSettings(
                borderSettings = borderSettings,
                onSettingsChanged = onSettingsChanged
            )
        }
    }
}

/**
 * 边框滑块设置
 */
@Composable
fun BorderSliderSetting(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChanged: (Float) -> Unit,
    formatValue: (Float) -> String = { it.toString() },
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            Text(
                text = formatValue(value),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Slider(
            value = value,
            onValueChange = onValueChanged,
            valueRange = valueRange,
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary,
                activeTrackColor = MaterialTheme.colorScheme.primary,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            )
        )
    }
}

/**
 * 边框颜色选择器
 */
@Composable
fun BorderColorPicker(
    selectedColor: Color,
    onColorSelected: (Color) -> Unit,
    modifier: Modifier = Modifier
) {
    val colors = listOf(
        Color.White, Color.Black, Color.Red, Color.Green, Color.Blue,
        Color.Yellow, Color.Cyan, Color.Magenta, Color.Gray,
        Color(0xFFFF9800), Color(0xFF9C27B0), Color(0xFF607D8B)
    )
    
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(colors) { color ->
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(color)
                    .border(
                        width = if (selectedColor == color) 3.dp else 1.dp,
                        color = if (selectedColor == color) MaterialTheme.colorScheme.primary
                        else Color.White.copy(alpha = 0.3f),
                        shape = CircleShape
                    )
                    .clickable { onColorSelected(color) }
            )
        }
    }
}

// TODO: 实现其他设置组件
@Composable
fun BorderGradientSettings(borderSettings: BorderSettings, onSettingsChanged: (BorderSettings) -> Unit) {}

@Composable
fun BorderPatternSettings(borderSettings: BorderSettings, onSettingsChanged: (BorderSettings) -> Unit) {}

@Composable
fun BorderDecorativeSettings(borderSettings: BorderSettings, onSettingsChanged: (BorderSettings) -> Unit) {}

@Composable
fun BorderShadowSettings(borderSettings: BorderSettings, onSettingsChanged: (BorderSettings) -> Unit) {}
