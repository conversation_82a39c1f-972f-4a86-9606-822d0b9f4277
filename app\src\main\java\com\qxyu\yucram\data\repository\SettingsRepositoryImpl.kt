package com.qxyu.yucram.data.repository

import com.qxyu.yucram.data.local.dao.SettingsDao
import com.qxyu.yucram.data.local.entity.SettingsEntity
import com.qxyu.yucram.domain.repository.SettingsRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设置Repository实现
 */
@Singleton
class SettingsRepositoryImpl @Inject constructor(
    private val settingsDao: SettingsDao
) : SettingsRepository {
    
    companion object {
        private const val KEY_THEME_COLOR = "theme_color"
        private const val KEY_VIBRATION_INTENSITY = "vibration_intensity"
        private const val KEY_LANGUAGE = "language"
        private const val KEY_GPS_WATERMARK = "gps_watermark"
        private const val KEY_VIBRATION_ENABLED = "vibration_enabled"
        
        private const val DEFAULT_THEME_COLOR = "#FF6600"
        private const val DEFAULT_VIBRATION_INTENSITY = "5"
        private const val DEFAULT_LANGUAGE = "zh"
        private const val DEFAULT_GPS_WATERMARK = "false"
        private const val DEFAULT_VIBRATION_ENABLED = "true"
    }
    
    override fun getThemeColor(): Flow<String> {
        return settingsDao.getSettingByKey(KEY_THEME_COLOR).map { entity ->
            entity?.value ?: DEFAULT_THEME_COLOR
        }
    }
    
    override suspend fun setThemeColor(color: String) {
        settingsDao.insertSetting(SettingsEntity(KEY_THEME_COLOR, color))
    }
    
    override fun getVibrationIntensity(): Flow<Int> {
        return settingsDao.getSettingByKey(KEY_VIBRATION_INTENSITY).map { entity ->
            entity?.value?.toIntOrNull() ?: DEFAULT_VIBRATION_INTENSITY.toInt()
        }
    }
    
    override suspend fun setVibrationIntensity(intensity: Int) {
        settingsDao.insertSetting(SettingsEntity(KEY_VIBRATION_INTENSITY, intensity.toString()))
    }
    
    override fun getLanguage(): Flow<String> {
        return settingsDao.getSettingByKey(KEY_LANGUAGE).map { entity ->
            entity?.value ?: DEFAULT_LANGUAGE
        }
    }
    
    override suspend fun setLanguage(language: String) {
        settingsDao.insertSetting(SettingsEntity(KEY_LANGUAGE, language))
    }
    
    override fun isGpsWatermarkEnabled(): Flow<Boolean> {
        return settingsDao.getSettingByKey(KEY_GPS_WATERMARK).map { entity ->
            entity?.value?.toBoolean() ?: DEFAULT_GPS_WATERMARK.toBoolean()
        }
    }
    
    override suspend fun setGpsWatermarkEnabled(enabled: Boolean) {
        settingsDao.insertSetting(SettingsEntity(KEY_GPS_WATERMARK, enabled.toString()))
    }
    
    override fun isVibrationEnabled(): Flow<Boolean> {
        return settingsDao.getSettingByKey(KEY_VIBRATION_ENABLED).map { entity ->
            entity?.value?.toBoolean() ?: DEFAULT_VIBRATION_ENABLED.toBoolean()
        }
    }
    
    override suspend fun setVibrationEnabled(enabled: Boolean) {
        settingsDao.insertSetting(SettingsEntity(KEY_VIBRATION_ENABLED, enabled.toString()))
    }
    
    override suspend fun resetAllSettings() {
        settingsDao.deleteAllSettings()
        // 重新插入默认设置
        settingsDao.insertSetting(SettingsEntity(KEY_THEME_COLOR, DEFAULT_THEME_COLOR))
        settingsDao.insertSetting(SettingsEntity(KEY_VIBRATION_INTENSITY, DEFAULT_VIBRATION_INTENSITY))
        settingsDao.insertSetting(SettingsEntity(KEY_LANGUAGE, DEFAULT_LANGUAGE))
        settingsDao.insertSetting(SettingsEntity(KEY_GPS_WATERMARK, DEFAULT_GPS_WATERMARK))
        settingsDao.insertSetting(SettingsEntity(KEY_VIBRATION_ENABLED, DEFAULT_VIBRATION_ENABLED))
    }
}
