package com.qxyu.yucram.film;

/**
 * LUT处理器
 * 负责LUT文件的加载、解析和应用
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u000e\n\u0002\u0010\u0014\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0010\b\u0007\u0018\u0000 O2\u00020\u0001:\u0001OB\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J.\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00190\u00182\u0006\u0010\u001a\u001a\u00020\u00132\b\b\u0002\u0010\u001b\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ6\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001f0\u00182\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u00142\b\b\u0002\u0010\u001b\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\"\u0010#J \u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020%2\u0006\u0010!\u001a\u00020\u00142\u0006\u0010\u001b\u001a\u00020\u000bH\u0002J\u0010\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020*H\u0002J\"\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070,0\u0018H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b-\u0010.J&\u0010/\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u00182\u0006\u0010\u001a\u001a\u00020\u0013H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b0\u00101J$\u00102\u001a\b\u0012\u0004\u0012\u00020\u00190\u00182\u0006\u00103\u001a\u00020\u0013H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b4\u00101J\u001c\u00105\u001a\b\u0012\u0004\u0012\u00020\t0\u0018H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u0010.J$\u00107\u001a\b\u0012\u0004\u0012\u00020\u00140\u00182\u0006\u00108\u001a\u00020\u0013H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b9\u00101J(\u0010:\u001a\u00020;2\u0006\u0010<\u001a\u00020\u000b2\u0006\u0010=\u001a\u00020\u000b2\u0006\u0010>\u001a\u00020\u000b2\u0006\u0010!\u001a\u00020\u0014H\u0002J\u000e\u0010?\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070@J\f\u0010A\u001a\b\u0012\u0004\u0012\u00020\u000b0@J\u0010\u0010B\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*H\u0002J\u0010\u0010C\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*H\u0002J\u0010\u0010D\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*H\u0002J\u0010\u0010E\u001a\u00020\u00142\u0006\u0010)\u001a\u00020*H\u0002J \u0010F\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\u00142\u0006\u0010\u001b\u001a\u00020\u000bH\u0002J\u001c\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00190\u0018H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bH\u0010.J$\u0010I\u001a\b\u0012\u0004\u0012\u00020\u00190\u00182\u0006\u0010\u001b\u001a\u00020\u000bH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bJ\u0010KJ0\u0010L\u001a\u00020;2\u0006\u0010<\u001a\u00020\u000b2\u0006\u0010=\u001a\u00020\u000b2\u0006\u0010>\u001a\u00020\u000b2\u0006\u0010M\u001a\u00020%2\u0006\u0010N\u001a\u00020;H\u0002R\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000fR\u001a\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020\u00140\u0012X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u000f\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006P"}, d2 = {"Lcom/qxyu/yucram/film/LutProcessor;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_currentLut", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/domain/model/LutInfo;", "_isProcessing", "", "_lutIntensity", "", "currentLut", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentLut", "()Lkotlinx/coroutines/flow/StateFlow;", "isProcessing", "lutCache", "", "", "Lcom/qxyu/yucram/domain/model/LutData;", "lutIntensity", "getLutIntensity", "applyLut", "Lkotlin/Result;", "", "lutId", "intensity", "applyLut-0E7RQCE", "(Ljava/lang/String;FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyLutToImage", "Landroid/graphics/Bitmap;", "bitmap", "lutData", "applyLutToImage-BWLJW6A", "(Landroid/graphics/Bitmap;Lcom/qxyu/yucram/domain/model/LutData;FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyLutToPixel", "", "pixel", "detectLutFormat", "Lcom/qxyu/yucram/domain/model/LutFormat;", "file", "Ljava/io/File;", "getInstalledLuts", "", "getInstalledLuts-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLutInfo", "getLutInfo-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "installLutFromAssets", "assetPath", "installLutFromAssets-gIAlu-s", "isLutApplied", "isLutApplied-IoAF18A", "loadLutFile", "filePath", "loadLutFile-gIAlu-s", "lookupLut", "", "r", "g", "b", "observeCurrentLut", "Lkotlinx/coroutines/flow/Flow;", "observeLutIntensity", "parse3DLLut", "parseCubeLut", "parseLutFile", "parsePngLut", "processImageWithLut", "removeLut", "removeLut-IoAF18A", "setLutIntensity", "setLutIntensity-gIAlu-s", "(FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "trilinearInterpolation", "size", "data", "Companion", "app_debug"})
public final class LutProcessor {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "LutProcessor";
    private static final int DEFAULT_LUT_SIZE = 33;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.LutInfo> _currentLut = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LutInfo> currentLut = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Float> _lutIntensity = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Float> lutIntensity = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isProcessing = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isProcessing = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.qxyu.yucram.domain.model.LutData> lutCache = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.film.LutProcessor.Companion Companion = null;
    
    @javax.inject.Inject()
    public LutProcessor(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.LutInfo> getCurrentLut() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Float> getLutIntensity() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isProcessing() {
        return null;
    }
    
    /**
     * 观察当前LUT
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.qxyu.yucram.domain.model.LutInfo> observeCurrentLut() {
        return null;
    }
    
    /**
     * 观察LUT强度
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.lang.Float> observeLutIntensity() {
        return null;
    }
    
    /**
     * 检测LUT文件格式
     */
    private final com.qxyu.yucram.domain.model.LutFormat detectLutFormat(java.io.File file) {
        return null;
    }
    
    /**
     * 解析CUBE格式LUT
     */
    private final com.qxyu.yucram.domain.model.LutData parseCubeLut(java.io.File file) {
        return null;
    }
    
    /**
     * 解析3DL格式LUT
     */
    private final com.qxyu.yucram.domain.model.LutData parse3DLLut(java.io.File file) {
        return null;
    }
    
    /**
     * 解析LUT文件
     */
    private final com.qxyu.yucram.domain.model.LutData parseLutFile(java.io.File file) {
        return null;
    }
    
    /**
     * 解析PNG格式LUT
     */
    private final com.qxyu.yucram.domain.model.LutData parsePngLut(java.io.File file) {
        return null;
    }
    
    /**
     * 使用LUT处理图像
     */
    private final android.graphics.Bitmap processImageWithLut(android.graphics.Bitmap bitmap, com.qxyu.yucram.domain.model.LutData lutData, float intensity) {
        return null;
    }
    
    /**
     * 对单个像素应用LUT
     */
    private final int applyLutToPixel(int pixel, com.qxyu.yucram.domain.model.LutData lutData, float intensity) {
        return 0;
    }
    
    /**
     * LUT查找
     */
    private final float[] lookupLut(float r, float g, float b, com.qxyu.yucram.domain.model.LutData lutData) {
        return null;
    }
    
    /**
     * 三线性插值
     */
    private final float[] trilinearInterpolation(float r, float g, float b, int size, float[] data) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/qxyu/yucram/film/LutProcessor$Companion;", "", "()V", "DEFAULT_LUT_SIZE", "", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}