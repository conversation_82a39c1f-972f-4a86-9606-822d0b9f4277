package com.qxyu.yucram.data.local.dao

import androidx.room.*
import com.qxyu.yucram.data.local.entity.SettingsEntity
import kotlinx.coroutines.flow.Flow

/**
 * 设置数据访问对象
 */
@Dao
interface SettingsDao {
    
    @Query("SELECT * FROM settings WHERE key = :key")
    fun getSettingByKey(key: String): Flow<SettingsEntity?>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSetting(setting: SettingsEntity)
    
    @Update
    suspend fun updateSetting(setting: SettingsEntity)
    
    @Query("DELETE FROM settings WHERE key = :key")
    suspend fun deleteSettingByKey(key: String)
    
    @Query("DELETE FROM settings")
    suspend fun deleteAllSettings()
    
    @Query("SELECT * FROM settings")
    fun getAllSettings(): Flow<List<SettingsEntity>>
}
