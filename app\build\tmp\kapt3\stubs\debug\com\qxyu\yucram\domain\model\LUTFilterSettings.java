package com.qxyu.yucram.domain.model;

/**
 * LUT滤镜设置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b#\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bm\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\t\u0012\b\b\u0002\u0010\r\u001a\u00020\t\u0012\b\b\u0002\u0010\u000e\u001a\u00020\t\u0012\b\b\u0002\u0010\u000f\u001a\u00020\t\u0012\b\b\u0002\u0010\u0010\u001a\u00020\t\u00a2\u0006\u0002\u0010\u0011J\u000b\u0010!\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\tH\u00c6\u0003J\t\u0010#\u001a\u00020\u0005H\u00c6\u0003J\t\u0010$\u001a\u00020\u0007H\u00c6\u0003J\t\u0010%\u001a\u00020\tH\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\t\u0010\'\u001a\u00020\tH\u00c6\u0003J\t\u0010(\u001a\u00020\tH\u00c6\u0003J\t\u0010)\u001a\u00020\tH\u00c6\u0003J\t\u0010*\u001a\u00020\tH\u00c6\u0003Jq\u0010+\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\t2\b\b\u0002\u0010\r\u001a\u00020\t2\b\b\u0002\u0010\u000e\u001a\u00020\t2\b\b\u0002\u0010\u000f\u001a\u00020\t2\b\b\u0002\u0010\u0010\u001a\u00020\tH\u00c6\u0001J\u0013\u0010,\u001a\u00020\t2\b\u0010-\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010.\u001a\u00020/H\u00d6\u0001J\t\u00100\u001a\u00020\u000bH\u00d6\u0001R\u0011\u0010\r\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0010\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u0011\u0010\f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0013R\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u000e\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0013R\u0011\u0010\u000f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0013R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 \u00a8\u00061"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "", "selectedFilter", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "intensity", "", "blendMode", "Lcom/qxyu/yucram/domain/model/LUTBlendMode;", "maskEnabled", "", "maskPath", "", "maskInvert", "beforeAfterMode", "previewEnabled", "realTimePreview", "highQualityMode", "(Lcom/qxyu/yucram/domain/model/LUTFilter;FLcom/qxyu/yucram/domain/model/LUTBlendMode;ZLjava/lang/String;ZZZZZ)V", "getBeforeAfterMode", "()Z", "getBlendMode", "()Lcom/qxyu/yucram/domain/model/LUTBlendMode;", "getHighQualityMode", "getIntensity", "()F", "getMaskEnabled", "getMaskInvert", "getMaskPath", "()Ljava/lang/String;", "getPreviewEnabled", "getRealTimePreview", "getSelectedFilter", "()Lcom/qxyu/yucram/domain/model/LUTFilter;", "component1", "component10", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class LUTFilterSettings {
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.LUTFilter selectedFilter = null;
    private final float intensity = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LUTBlendMode blendMode = null;
    private final boolean maskEnabled = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String maskPath = null;
    private final boolean maskInvert = false;
    private final boolean beforeAfterMode = false;
    private final boolean previewEnabled = false;
    private final boolean realTimePreview = false;
    private final boolean highQualityMode = false;
    
    public LUTFilterSettings(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter selectedFilter, float intensity, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTBlendMode blendMode, boolean maskEnabled, @org.jetbrains.annotations.Nullable()
    java.lang.String maskPath, boolean maskInvert, boolean beforeAfterMode, boolean previewEnabled, boolean realTimePreview, boolean highQualityMode) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilter getSelectedFilter() {
        return null;
    }
    
    public final float getIntensity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTBlendMode getBlendMode() {
        return null;
    }
    
    public final boolean getMaskEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMaskPath() {
        return null;
    }
    
    public final boolean getMaskInvert() {
        return false;
    }
    
    public final boolean getBeforeAfterMode() {
        return false;
    }
    
    public final boolean getPreviewEnabled() {
        return false;
    }
    
    public final boolean getRealTimePreview() {
        return false;
    }
    
    public final boolean getHighQualityMode() {
        return false;
    }
    
    public LUTFilterSettings() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilter component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTBlendMode component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFilterSettings copy(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter selectedFilter, float intensity, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTBlendMode blendMode, boolean maskEnabled, @org.jetbrains.annotations.Nullable()
    java.lang.String maskPath, boolean maskInvert, boolean beforeAfterMode, boolean previewEnabled, boolean realTimePreview, boolean highQualityMode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}