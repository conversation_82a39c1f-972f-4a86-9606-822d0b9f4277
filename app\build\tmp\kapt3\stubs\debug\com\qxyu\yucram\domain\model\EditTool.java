package com.qxyu.yucram.domain.model;

/**
 * 编辑工具类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0018\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018\u00a8\u0006\u0019"}, d2 = {"Lcom/qxyu/yucram/domain/model/EditTool;", "", "(Ljava/lang/String;I)V", "EXPOSURE", "HIGHLIGHTS_SHADOWS", "WHITES_BLACKS", "CONTRAST_BRIGHTNESS", "VIBRANCE_SATURATION", "TEMPERATURE_TINT", "HSL", "CURVES", "SHARPNESS", "NOISE_REDUCTION", "CLARITY_DEHAZE", "VIGNETTE", "GRAIN", "FILM_EMULATION", "CROP_ROTATE", "PERSPECTIVE", "RADIAL_FILTER", "GRADUATED_FILTER", "MASKING", "BRUSH", "LUT_FILTERS", "BORDER_WATERMARK", "app_debug"})
public enum EditTool {
    /*public static final*/ EXPOSURE /* = new EXPOSURE() */,
    /*public static final*/ HIGHLIGHTS_SHADOWS /* = new HIGHLIGHTS_SHADOWS() */,
    /*public static final*/ WHITES_BLACKS /* = new WHITES_BLACKS() */,
    /*public static final*/ CONTRAST_BRIGHTNESS /* = new CONTRAST_BRIGHTNESS() */,
    /*public static final*/ VIBRANCE_SATURATION /* = new VIBRANCE_SATURATION() */,
    /*public static final*/ TEMPERATURE_TINT /* = new TEMPERATURE_TINT() */,
    /*public static final*/ HSL /* = new HSL() */,
    /*public static final*/ CURVES /* = new CURVES() */,
    /*public static final*/ SHARPNESS /* = new SHARPNESS() */,
    /*public static final*/ NOISE_REDUCTION /* = new NOISE_REDUCTION() */,
    /*public static final*/ CLARITY_DEHAZE /* = new CLARITY_DEHAZE() */,
    /*public static final*/ VIGNETTE /* = new VIGNETTE() */,
    /*public static final*/ GRAIN /* = new GRAIN() */,
    /*public static final*/ FILM_EMULATION /* = new FILM_EMULATION() */,
    /*public static final*/ CROP_ROTATE /* = new CROP_ROTATE() */,
    /*public static final*/ PERSPECTIVE /* = new PERSPECTIVE() */,
    /*public static final*/ RADIAL_FILTER /* = new RADIAL_FILTER() */,
    /*public static final*/ GRADUATED_FILTER /* = new GRADUATED_FILTER() */,
    /*public static final*/ MASKING /* = new MASKING() */,
    /*public static final*/ BRUSH /* = new BRUSH() */,
    /*public static final*/ LUT_FILTERS /* = new LUT_FILTERS() */,
    /*public static final*/ BORDER_WATERMARK /* = new BORDER_WATERMARK() */;
    
    EditTool() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.EditTool> getEntries() {
        return null;
    }
}