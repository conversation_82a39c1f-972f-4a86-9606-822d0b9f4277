# Yucram V2.0.0 文件格式详细说明

## 📁 保存位置

所有照片都保存在Android相册系统中：
- **路径**: `/DCIM/Yucram/`
- **访问方式**: 通过系统相册应用可直接查看
- **兼容性**: 支持所有Android相册和图片查看器

## 📸 支持的文件格式

### 1. JPEG格式 ✅
- **文件扩展名**: `.jpg`
- **命名规则**: `YUCRAM_yyyyMMdd_HHmmss.jpg`
- **MIME类型**: `image/jpeg`
- **质量设置**: 100%（无损压缩）
- **用途**: 标准照片拍摄
- **示例**: `YUCRAM_20241228_143052.jpg`

### 2. RAW格式（DNG）✅
- **文件扩展名**: `.dng`
- **命名规则**: `YUCRAM_RAW_yyyyMMdd_HHmmss.dng`
- **MIME类型**: `image/x-adobe-dng`
- **标准**: Adobe Digital Negative 1.4
- **用途**: 专业后期处理，保留完整图像信息
- **示例**: `YUCRAM_RAW_20241228_143052.dng`

### 3. HDR格式 ✅
- **文件扩展名**: `.jpg`
- **命名规则**: `YUCRAM_HDR_yyyyMMdd_HHmmss.jpg`
- **MIME类型**: `image/jpeg`
- **处理**: 硬件HDR合成
- **用途**: 高动态范围摄影
- **示例**: `YUCRAM_HDR_20241228_143052.jpg`

## 🔧 技术实现细节

### RAW/DNG文件创建
```kotlin
// 使用Android官方DngCreator
val dngCreator = DngCreator(cameraCharacteristics, captureResult)
dngCreator.setOrientation(ExifInterface.ORIENTATION_NORMAL)
dngCreator.writeImage(outputStream, rawImage)
```

**DNG文件包含的信息**:
- ✅ 原始传感器数据（未处理）
- ✅ 相机元数据（ISO、快门、光圈等）
- ✅ 色彩配置文件
- ✅ 镜头校正信息
- ✅ 白平衡数据
- ✅ EXIF信息

### HDR处理
```kotlin
// 启用硬件HDR模式
requestBuilder.set(CaptureRequest.CONTROL_SCENE_MODE, 
    CaptureRequest.CONTROL_SCENE_MODE_HDR)
```

**HDR处理特性**:
- ✅ 自动多重曝光
- ✅ 色调映射
- ✅ 高光恢复
- ✅ 阴影提升
- ✅ 色彩增强

### 文件保存流程
```kotlin
// 保存到Android MediaStore
val contentValues = ContentValues().apply {
    put(MediaStore.Images.Media.DISPLAY_NAME, filename)
    put(MediaStore.Images.Media.MIME_TYPE, mimeType)
    put(MediaStore.Images.Media.RELATIVE_PATH, "DCIM/Yucram")
}
val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
```

## 📱 文件兼容性

### DNG文件支持
- ✅ **Adobe Lightroom** - 完全支持
- ✅ **Adobe Photoshop** - 完全支持
- ✅ **RawTherapee** - 开源RAW处理器
- ✅ **darktable** - Linux/Mac RAW处理器
- ✅ **Snapseed** - Google移动端编辑器
- ✅ **VSCO** - 移动端专业编辑器

### JPEG文件支持
- ✅ **所有图片查看器** - 通用格式
- ✅ **所有编辑软件** - 标准支持
- ✅ **社交媒体平台** - 直接上传
- ✅ **打印服务** - 高质量输出

## 🎯 拍摄模式对应的文件输出

### 标准拍摄
- **输出**: 1个JPEG文件
- **文件**: `YUCRAM_yyyyMMdd_HHmmss.jpg`

### RAW开启
- **输出**: 2个文件（JPEG + DNG）
- **文件**: 
  - `YUCRAM_yyyyMMdd_HHmmss.jpg`
  - `YUCRAM_RAW_yyyyMMdd_HHmmss.dng`

### HDR开启
- **输出**: 1个HDR处理的JPEG文件
- **文件**: `YUCRAM_HDR_yyyyMMdd_HHmmss.jpg`

### RAW + HDR开启
- **输出**: 2个文件（HDR JPEG + RAW DNG）
- **文件**:
  - `YUCRAM_HDR_yyyyMMdd_HHmmss.jpg`
  - `YUCRAM_RAW_yyyyMMdd_HHmmss.dng`

## 📊 文件大小参考

### 典型文件大小（基于12MP传感器）
- **JPEG (100%质量)**: 3-8 MB
- **DNG (RAW)**: 15-25 MB
- **HDR JPEG**: 4-10 MB

### 存储空间建议
- **仅JPEG**: 1GB ≈ 200-300张照片
- **JPEG + RAW**: 1GB ≈ 40-60张照片
- **HDR**: 1GB ≈ 150-250张照片

## 🔍 质量对比

### JPEG vs RAW
| 特性 | JPEG | RAW (DNG) |
|------|------|-----------|
| 文件大小 | 小 | 大 |
| 处理速度 | 快 | 慢 |
| 后期空间 | 有限 | 极大 |
| 色彩深度 | 8-bit | 12/14-bit |
| 动态范围 | 标准 | 扩展 |
| 兼容性 | 通用 | 专业软件 |

### 标准 vs HDR
| 特性 | 标准JPEG | HDR JPEG |
|------|----------|----------|
| 动态范围 | 标准 | 扩展 |
| 高光细节 | 可能过曝 | 保留 |
| 阴影细节 | 可能欠曝 | 提升 |
| 色彩饱和度 | 自然 | 增强 |
| 处理时间 | 快 | 稍慢 |

---

**总结**: Yucram V2.0.0 提供完整的专业级文件格式支持，满足从日常拍摄到专业后期的所有需求。
