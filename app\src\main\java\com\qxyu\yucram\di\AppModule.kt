package com.qxyu.yucram.di

import android.content.Context
import android.content.SharedPreferences
import com.qxyu.yucram.camera.CameraManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt模块 - 应用程序级别依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideSharedPreferences(
        @ApplicationContext context: Context
    ): SharedPreferences {
        return context.getSharedPreferences("yucram_preferences", Context.MODE_PRIVATE)
    }

    @Provides
    @Singleton
    fun provideCameraManager(
        @ApplicationContext context: Context,
        imageProcessingPipeline: com.qxyu.yucram.filter.ImageProcessingPipeline
    ): CameraManager {
        return CameraManager(context, imageProcessingPipeline)
    }
}
