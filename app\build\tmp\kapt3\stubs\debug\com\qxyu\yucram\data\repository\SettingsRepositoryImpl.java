package com.qxyu.yucram.data.repository;

/**
 * 设置Repository实现
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u000f\b\u0007\u0018\u0000 \u001d2\u00020\u0001:\u0001\u001dB\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0016J\u000e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0016J\u000e\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0006H\u0016J\u000e\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006H\u0016J\u000e\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u0006H\u0016J\u000e\u0010\u000e\u001a\u00020\u000fH\u0096@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\fH\u0096@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\u000f2\u0006\u0010\u0015\u001a\u00020\u0007H\u0096@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u0018\u001a\u00020\u0007H\u0096@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0019\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\fH\u0096@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u001a\u001a\u00020\u000f2\u0006\u0010\u001b\u001a\u00020\nH\u0096@\u00a2\u0006\u0002\u0010\u001cR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/qxyu/yucram/data/repository/SettingsRepositoryImpl;", "Lcom/qxyu/yucram/domain/repository/SettingsRepository;", "settingsDao", "Lcom/qxyu/yucram/data/local/dao/SettingsDao;", "(Lcom/qxyu/yucram/data/local/dao/SettingsDao;)V", "getLanguage", "Lkotlinx/coroutines/flow/Flow;", "", "getThemeColor", "getVibrationIntensity", "", "isGpsWatermarkEnabled", "", "isVibrationEnabled", "resetAllSettings", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setGpsWatermarkEnabled", "enabled", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setLanguage", "language", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setThemeColor", "color", "setVibrationEnabled", "setVibrationIntensity", "intensity", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class SettingsRepositoryImpl implements com.qxyu.yucram.domain.repository.SettingsRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.data.local.dao.SettingsDao settingsDao = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_THEME_COLOR = "theme_color";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_VIBRATION_INTENSITY = "vibration_intensity";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_LANGUAGE = "language";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_GPS_WATERMARK = "gps_watermark";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_VIBRATION_ENABLED = "vibration_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_THEME_COLOR = "#FF6600";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_VIBRATION_INTENSITY = "5";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_LANGUAGE = "zh";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_GPS_WATERMARK = "false";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String DEFAULT_VIBRATION_ENABLED = "true";
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.data.repository.SettingsRepositoryImpl.Companion Companion = null;
    
    @javax.inject.Inject()
    public SettingsRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.local.dao.SettingsDao settingsDao) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.String> getThemeColor() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object setThemeColor(@org.jetbrains.annotations.NotNull()
    java.lang.String color, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Integer> getVibrationIntensity() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object setVibrationIntensity(int intensity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.String> getLanguage() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object setLanguage(@org.jetbrains.annotations.NotNull()
    java.lang.String language, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Boolean> isGpsWatermarkEnabled() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object setGpsWatermarkEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.lang.Boolean> isVibrationEnabled() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object setVibrationEnabled(boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object resetAllSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\n\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/qxyu/yucram/data/repository/SettingsRepositoryImpl$Companion;", "", "()V", "DEFAULT_GPS_WATERMARK", "", "DEFAULT_LANGUAGE", "DEFAULT_THEME_COLOR", "DEFAULT_VIBRATION_ENABLED", "DEFAULT_VIBRATION_INTENSITY", "KEY_GPS_WATERMARK", "KEY_LANGUAGE", "KEY_THEME_COLOR", "KEY_VIBRATION_ENABLED", "KEY_VIBRATION_INTENSITY", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}