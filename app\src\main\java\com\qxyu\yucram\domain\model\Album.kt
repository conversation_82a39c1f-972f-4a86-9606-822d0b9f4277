package com.qxyu.yucram.domain.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDateTime

/**
 * 相册数据模型
 */
@Entity(tableName = "albums")
data class Album(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String? = null,
    val coverPhotoId: String? = null, // 封面照片ID
    val dateCreated: LocalDateTime,
    val dateModified: LocalDateTime,
    val photoCount: Int = 0,
    val isSystemAlbum: Boolean = false, // 是否为系统相册（如"最近项目"、"收藏"等）
    val albumType: AlbumType = AlbumType.USER_CREATED,
    val sortOrder: PhotoSortOrder = PhotoSortOrder.DATE_TAKEN_DESC,
    val isPrivate: Boolean = false, // 是否为私密相册
    val tags: List<String> = emptyList(),
    val color: AlbumColor? = null // 相册主题色
)

/**
 * 相册类型
 */
enum class AlbumType {
    USER_CREATED,    // 用户创建
    RECENT,          // 最近项目
    FAVORITES,       // 收藏
    YUCRAM_PHOTOS,   // Yucram拍摄
    CAMERA_ROLL,     // 相机胶卷
    SCREENSHOTS,     // 截图
    VIDEOS,          // 视频
    RAW_PHOTOS,      // RAW照片
    FILTERED_PHOTOS, // 滤镜照片
    LOCATION_BASED,  // 基于位置
    DATE_BASED,      // 基于日期
    SMART_ALBUM      // 智能相册
}

/**
 * 相册主题色
 */
enum class AlbumColor(val color: androidx.compose.ui.graphics.Color) {
    RED(androidx.compose.ui.graphics.Color.Red),
    PINK(androidx.compose.ui.graphics.Color(0xFFE91E63)),
    PURPLE(androidx.compose.ui.graphics.Color(0xFF9C27B0)),
    DEEP_PURPLE(androidx.compose.ui.graphics.Color(0xFF673AB7)),
    INDIGO(androidx.compose.ui.graphics.Color(0xFF3F51B5)),
    BLUE(androidx.compose.ui.graphics.Color.Blue),
    LIGHT_BLUE(androidx.compose.ui.graphics.Color(0xFF03A9F4)),
    CYAN(androidx.compose.ui.graphics.Color.Cyan),
    TEAL(androidx.compose.ui.graphics.Color(0xFF009688)),
    GREEN(androidx.compose.ui.graphics.Color.Green),
    LIGHT_GREEN(androidx.compose.ui.graphics.Color(0xFF8BC34A)),
    LIME(androidx.compose.ui.graphics.Color(0xFFCDDC39)),
    YELLOW(androidx.compose.ui.graphics.Color.Yellow),
    AMBER(androidx.compose.ui.graphics.Color(0xFFFFC107)),
    ORANGE(androidx.compose.ui.graphics.Color(0xFFFF9800)),
    DEEP_ORANGE(androidx.compose.ui.graphics.Color(0xFFFF5722)),
    BROWN(androidx.compose.ui.graphics.Color(0xFF795548)),
    GREY(androidx.compose.ui.graphics.Color.Gray),
    BLUE_GREY(androidx.compose.ui.graphics.Color(0xFF607D8B))
}

/**
 * 相册统计信息
 */
data class AlbumStats(
    val photoCount: Int,
    val videoCount: Int,
    val totalSize: Long,
    val oldestPhoto: LocalDateTime?,
    val newestPhoto: LocalDateTime?,
    val averageFileSize: Long,
    val yucramPhotoCount: Int,
    val favoriteCount: Int,
    val mostUsedFilter: String?
)

/**
 * 相册操作结果
 */
sealed class AlbumOperationResult {
    object Success : AlbumOperationResult()
    data class Error(val message: String, val exception: Throwable? = null) : AlbumOperationResult()
    data class AlbumCreated(val album: Album) : AlbumOperationResult()
    data class AlbumUpdated(val album: Album) : AlbumOperationResult()
    data class AlbumDeleted(val albumId: String) : AlbumOperationResult()
}

/**
 * 相册加载状态
 */
sealed class AlbumLoadState {
    object Loading : AlbumLoadState()
    data class Success(val albums: List<Album>) : AlbumLoadState()
    data class Error(val message: String, val exception: Throwable? = null) : AlbumLoadState()
    object Empty : AlbumLoadState()
}

/**
 * 相册创建请求
 */
data class CreateAlbumRequest(
    val name: String,
    val description: String? = null,
    val coverPhotoId: String? = null,
    val isPrivate: Boolean = false,
    val color: AlbumColor? = null,
    val albumType: AlbumType = AlbumType.USER_CREATED,
    val initialPhotoIds: List<String> = emptyList()
)

/**
 * 相册更新请求
 */
data class UpdateAlbumRequest(
    val name: String? = null,
    val description: String? = null,
    val coverPhotoId: String? = null,
    val isPrivate: Boolean? = null,
    val color: AlbumColor? = null,
    val sortOrder: PhotoSortOrder? = null
)

/**
 * 相册照片关联
 */
@Entity(
    tableName = "album_photos",
    primaryKeys = ["albumId", "photoId"]
)
data class AlbumPhoto(
    val albumId: String,
    val photoId: String,
    val dateAdded: LocalDateTime,
    val order: Int = 0 // 在相册中的排序
)

/**
 * 智能相册规则
 */
@Entity(tableName = "smart_album_rules")
data class SmartAlbumRule(
    @PrimaryKey
    val id: String,
    val albumId: String,
    val ruleType: SmartAlbumRuleType,
    val value: String, // 规则值
    val operator: SmartAlbumOperator = SmartAlbumOperator.EQUALS,
    val parameter: String = "", // JSON格式的参数
    val isEnabled: Boolean = true
)

/**
 * 智能相册规则类型
 */
enum class SmartAlbumRuleType(val displayName: String) {
    HAS_TAG("包含标签"),
    DATE_RANGE("日期范围"),
    LOCATION("位置"),
    CAMERA_MODEL("相机型号"),
    IS_FAVORITE("收藏照片"),
    IS_YUCRAM_PHOTO("Yucram照片"),
    HAS_FILTER("使用滤镜"),
    FILE_SIZE("文件大小"),
    FILTER_USED("使用的滤镜"),
    ORIENTATION("方向"),
    TAGS("标签"),
    FAVORITES("收藏"),
    YUCRAM_PHOTOS("Yucram拍摄"),
    FACE_COUNT("人脸数量"),
    SCENE_TYPE("场景类型")
}

/**
 * 智能相册操作符
 */
enum class SmartAlbumOperator {
    EQUALS,         // 等于
    CONTAINS,       // 包含
    GREATER_THAN,   // 大于
    LESS_THAN,      // 小于
    BETWEEN         // 之间
}

/**
 * 相册视图配置
 */
data class AlbumViewConfig(
    val displayMode: PhotoDisplayMode = PhotoDisplayMode.GRID_MEDIUM,
    val sortOrder: PhotoSortOrder = PhotoSortOrder.DATE_TAKEN_DESC,
    val showMetadata: Boolean = false,
    val groupByDate: Boolean = true,
    val columnsInPortrait: Int = 3,
    val columnsInLandscape: Int = 5
)

/**
 * 相册分享信息
 */
data class AlbumShareInfo(
    val shareId: String,
    val albumId: String,
    val shareUrl: String,
    val isPublic: Boolean,
    val password: String? = null,
    val expiryDate: LocalDateTime? = null,
    val allowDownload: Boolean = true,
    val allowComments: Boolean = false,
    val viewCount: Int = 0,
    val dateCreated: LocalDateTime,
    val createdBy: String
)


