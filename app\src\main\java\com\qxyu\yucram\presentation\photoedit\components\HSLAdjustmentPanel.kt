package com.qxyu.yucram.presentation.photoedit.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.HSLAdjustments

/**
 * HSL颜色调整面板
 */
@Composable
fun HSLAdjustmentPanel(
    hslAdjustments: HSLAdjustments,
    onAdjustmentsChanged: (HSLAdjustments) -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedColor by remember { mutableStateOf(HSLColor.RED) }
    var selectedProperty by remember { mutableStateOf(HSLProperty.HUE) }
    
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "HSL调整",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 颜色选择器
        ColorSelector(
            selectedColor = selectedColor,
            onColorSelected = { selectedColor = it },
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 属性选择器
        PropertySelector(
            selectedProperty = selectedProperty,
            onPropertySelected = { selectedProperty = it },
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 调整滑块
        HSLSlider(
            color = selectedColor,
            property = selectedProperty,
            value = getCurrentValue(hslAdjustments, selectedColor, selectedProperty),
            onValueChanged = { newValue ->
                val newAdjustments = updateHSLValue(
                    hslAdjustments, 
                    selectedColor, 
                    selectedProperty, 
                    newValue
                )
                onAdjustmentsChanged(newAdjustments)
            }
        )
        
        // 重置按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(
                onClick = {
                    val resetAdjustments = resetColorAdjustments(hslAdjustments, selectedColor)
                    onAdjustmentsChanged(resetAdjustments)
                }
            ) {
                Text(
                    text = "重置${selectedColor.displayName}",
                    color = Color.White
                )
            }
        }
    }
}

/**
 * 颜色选择器
 */
@Composable
fun ColorSelector(
    selectedColor: HSLColor,
    onColorSelected: (HSLColor) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(HSLColor.values()) { color ->
            ColorSelectorItem(
                color = color,
                isSelected = selectedColor == color,
                onClick = { onColorSelected(color) }
            )
        }
    }
}

/**
 * 颜色选择项
 */
@Composable
fun ColorSelectorItem(
    color: HSLColor,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(color.color)
                .then(
                    if (isSelected) {
                        Modifier.background(
                            Color.White,
                            CircleShape
                        ).padding(2.dp).background(
                            color.color,
                            CircleShape
                        )
                    } else {
                        Modifier
                    }
                )
        ) {
            // 点击区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .clip(CircleShape)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = color.displayName,
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * 属性选择器
 */
@Composable
fun PropertySelector(
    selectedProperty: HSLProperty,
    onPropertySelected: (HSLProperty) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        HSLProperty.values().forEach { property ->
            PropertySelectorButton(
                property = property,
                isSelected = selectedProperty == property,
                onClick = { onPropertySelected(property) },
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 属性选择按钮
 */
@Composable
fun PropertySelectorButton(
    property: HSLProperty,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primary
            } else {
                Color.Transparent
            },
            contentColor = if (isSelected) {
                MaterialTheme.colorScheme.onPrimary
            } else {
                Color.White
            }
        ),
        shape = RoundedCornerShape(8.dp),
        modifier = modifier
    ) {
        Text(
            text = property.displayName,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * HSL调整滑块
 */
@Composable
fun HSLSlider(
    color: HSLColor,
    property: HSLProperty,
    value: Float,
    onValueChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 标签和数值
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${color.displayName} ${property.displayName}",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            Text(
                text = formatSliderValue(value),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 滑块
        Slider(
            value = value,
            onValueChange = onValueChanged,
            valueRange = -100f..100f,
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary,
                activeTrackColor = MaterialTheme.colorScheme.primary,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            )
        )
    }
}

/**
 * HSL颜色枚举
 */
enum class HSLColor(val displayName: String, val color: Color) {
    RED("红色", Color(0xFFFF0000)),
    ORANGE("橙色", Color(0xFFFF8000)),
    YELLOW("黄色", Color(0xFFFFFF00)),
    GREEN("绿色", Color(0xFF00FF00)),
    CYAN("青色", Color(0xFF00FFFF)),
    BLUE("蓝色", Color(0xFF0000FF)),
    PURPLE("紫色", Color(0xFF8000FF)),
    MAGENTA("洋红", Color(0xFFFF00FF))
}

/**
 * HSL属性枚举
 */
enum class HSLProperty(val displayName: String) {
    HUE("色相"),
    SATURATION("饱和度"),
    LUMINANCE("明度")
}

/**
 * 获取当前HSL值
 */
private fun getCurrentValue(
    adjustments: HSLAdjustments,
    color: HSLColor,
    property: HSLProperty
): Float {
    return when (color) {
        HSLColor.RED -> when (property) {
            HSLProperty.HUE -> adjustments.redHue
            HSLProperty.SATURATION -> adjustments.redSaturation
            HSLProperty.LUMINANCE -> adjustments.redLuminance
        }
        HSLColor.ORANGE -> when (property) {
            HSLProperty.HUE -> adjustments.orangeHue
            HSLProperty.SATURATION -> adjustments.orangeSaturation
            HSLProperty.LUMINANCE -> adjustments.orangeLuminance
        }
        HSLColor.YELLOW -> when (property) {
            HSLProperty.HUE -> adjustments.yellowHue
            HSLProperty.SATURATION -> adjustments.yellowSaturation
            HSLProperty.LUMINANCE -> adjustments.yellowLuminance
        }
        HSLColor.GREEN -> when (property) {
            HSLProperty.HUE -> adjustments.greenHue
            HSLProperty.SATURATION -> adjustments.greenSaturation
            HSLProperty.LUMINANCE -> adjustments.greenLuminance
        }
        HSLColor.CYAN -> when (property) {
            HSLProperty.HUE -> adjustments.cyanHue
            HSLProperty.SATURATION -> adjustments.cyanSaturation
            HSLProperty.LUMINANCE -> adjustments.cyanLuminance
        }
        HSLColor.BLUE -> when (property) {
            HSLProperty.HUE -> adjustments.blueHue
            HSLProperty.SATURATION -> adjustments.blueSaturation
            HSLProperty.LUMINANCE -> adjustments.blueLuminance
        }
        HSLColor.PURPLE -> when (property) {
            HSLProperty.HUE -> adjustments.purpleHue
            HSLProperty.SATURATION -> adjustments.purpleSaturation
            HSLProperty.LUMINANCE -> adjustments.purpleLuminance
        }
        HSLColor.MAGENTA -> when (property) {
            HSLProperty.HUE -> adjustments.magentaHue
            HSLProperty.SATURATION -> adjustments.magentaSaturation
            HSLProperty.LUMINANCE -> adjustments.magentaLuminance
        }
    }
}

/**
 * 更新HSL值
 */
private fun updateHSLValue(
    adjustments: HSLAdjustments,
    color: HSLColor,
    property: HSLProperty,
    value: Float
): HSLAdjustments {
    return when (color) {
        HSLColor.RED -> when (property) {
            HSLProperty.HUE -> adjustments.copy(redHue = value)
            HSLProperty.SATURATION -> adjustments.copy(redSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(redLuminance = value)
        }
        HSLColor.ORANGE -> when (property) {
            HSLProperty.HUE -> adjustments.copy(orangeHue = value)
            HSLProperty.SATURATION -> adjustments.copy(orangeSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(orangeLuminance = value)
        }
        HSLColor.YELLOW -> when (property) {
            HSLProperty.HUE -> adjustments.copy(yellowHue = value)
            HSLProperty.SATURATION -> adjustments.copy(yellowSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(yellowLuminance = value)
        }
        HSLColor.GREEN -> when (property) {
            HSLProperty.HUE -> adjustments.copy(greenHue = value)
            HSLProperty.SATURATION -> adjustments.copy(greenSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(greenLuminance = value)
        }
        HSLColor.CYAN -> when (property) {
            HSLProperty.HUE -> adjustments.copy(cyanHue = value)
            HSLProperty.SATURATION -> adjustments.copy(cyanSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(cyanLuminance = value)
        }
        HSLColor.BLUE -> when (property) {
            HSLProperty.HUE -> adjustments.copy(blueHue = value)
            HSLProperty.SATURATION -> adjustments.copy(blueSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(blueLuminance = value)
        }
        HSLColor.PURPLE -> when (property) {
            HSLProperty.HUE -> adjustments.copy(purpleHue = value)
            HSLProperty.SATURATION -> adjustments.copy(purpleSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(purpleLuminance = value)
        }
        HSLColor.MAGENTA -> when (property) {
            HSLProperty.HUE -> adjustments.copy(magentaHue = value)
            HSLProperty.SATURATION -> adjustments.copy(magentaSaturation = value)
            HSLProperty.LUMINANCE -> adjustments.copy(magentaLuminance = value)
        }
    }
}

/**
 * 重置颜色调整
 */
private fun resetColorAdjustments(
    adjustments: HSLAdjustments,
    color: HSLColor
): HSLAdjustments {
    return when (color) {
        HSLColor.RED -> adjustments.copy(
            redHue = 0f,
            redSaturation = 0f,
            redLuminance = 0f
        )
        HSLColor.ORANGE -> adjustments.copy(
            orangeHue = 0f,
            orangeSaturation = 0f,
            orangeLuminance = 0f
        )
        HSLColor.YELLOW -> adjustments.copy(
            yellowHue = 0f,
            yellowSaturation = 0f,
            yellowLuminance = 0f
        )
        HSLColor.GREEN -> adjustments.copy(
            greenHue = 0f,
            greenSaturation = 0f,
            greenLuminance = 0f
        )
        HSLColor.CYAN -> adjustments.copy(
            cyanHue = 0f,
            cyanSaturation = 0f,
            cyanLuminance = 0f
        )
        HSLColor.BLUE -> adjustments.copy(
            blueHue = 0f,
            blueSaturation = 0f,
            blueLuminance = 0f
        )
        HSLColor.PURPLE -> adjustments.copy(
            purpleHue = 0f,
            purpleSaturation = 0f,
            purpleLuminance = 0f
        )
        HSLColor.MAGENTA -> adjustments.copy(
            magentaHue = 0f,
            magentaSaturation = 0f,
            magentaLuminance = 0f
        )
    }
}

/**
 * 格式化滑块数值
 */
private fun formatSliderValue(value: Float): String {
    return when {
        value > 0 -> "+${value.toInt()}"
        value < 0 -> value.toInt().toString()
        else -> "0"
    }
}
