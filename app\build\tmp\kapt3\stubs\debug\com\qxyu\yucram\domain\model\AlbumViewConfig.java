package com.qxyu.yucram.domain.model;

/**
 * 相册视图配置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0017\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\n\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\nH\u00c6\u0003J\t\u0010\u001c\u001a\u00020\nH\u00c6\u0003JE\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u00072\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020\nH\u00d6\u0001J\t\u0010!\u001a\u00020\"H\u00d6\u0001R\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006#"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumViewConfig;", "", "displayMode", "Lcom/qxyu/yucram/domain/model/PhotoDisplayMode;", "sortOrder", "Lcom/qxyu/yucram/domain/model/PhotoSortOrder;", "showMetadata", "", "groupByDate", "columnsInPortrait", "", "columnsInLandscape", "(Lcom/qxyu/yucram/domain/model/PhotoDisplayMode;Lcom/qxyu/yucram/domain/model/PhotoSortOrder;ZZII)V", "getColumnsInLandscape", "()I", "getColumnsInPortrait", "getDisplayMode", "()Lcom/qxyu/yucram/domain/model/PhotoDisplayMode;", "getGroupByDate", "()Z", "getShowMetadata", "getSortOrder", "()Lcom/qxyu/yucram/domain/model/PhotoSortOrder;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "toString", "", "app_debug"})
public final class AlbumViewConfig {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.PhotoDisplayMode displayMode = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.PhotoSortOrder sortOrder = null;
    private final boolean showMetadata = false;
    private final boolean groupByDate = false;
    private final int columnsInPortrait = 0;
    private final int columnsInLandscape = 0;
    
    public AlbumViewConfig(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoDisplayMode displayMode, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoSortOrder sortOrder, boolean showMetadata, boolean groupByDate, int columnsInPortrait, int columnsInLandscape) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoDisplayMode getDisplayMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoSortOrder getSortOrder() {
        return null;
    }
    
    public final boolean getShowMetadata() {
        return false;
    }
    
    public final boolean getGroupByDate() {
        return false;
    }
    
    public final int getColumnsInPortrait() {
        return 0;
    }
    
    public final int getColumnsInLandscape() {
        return 0;
    }
    
    public AlbumViewConfig() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoDisplayMode component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoSortOrder component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.AlbumViewConfig copy(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoDisplayMode displayMode, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoSortOrder sortOrder, boolean showMetadata, boolean groupByDate, int columnsInPortrait, int columnsInLandscape) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}