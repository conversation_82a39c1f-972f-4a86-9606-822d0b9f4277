# Yucram 全屏沉浸式相机布局设计 (仿苹果相机)

## 📱 整体布局草图 (全屏沉浸式)

```
┌─────────────────────────────────────────┐
│ ⚡ 📐 🎯 🌈 ⚙️                          │ ← 顶部控制栏 (半透明悬浮)
│                                         │
│                                      🔄 │
│                                         │
│                                      🔍 │
│           📷 全屏预览区域                │
│              (边到边显示)                │   ← 右侧悬浮控制
│                                      ☀️ │
│                                         │
│                                         │
│                                         │
│     照片  视频  人像  专业               │ ← 拍摄模式切换
│                                         │
│ 📂      ⚪ 拍摄按钮 (超大)      🎨       │ ← 底部操作栏 (仿苹果)
└─────────────────────────────────────────┘
```

## 🎯 详细区域说明

### 1. 顶部控制栏 (Top Control Bar)
```
┌─────────────────────────────────────────┐
│ ⚡ 闪光灯  📐 网格线  🎯 RAW  🌈 HDR     │
│ [OFF/AUTO] [ON/OFF]  [ON/OFF] [ON/OFF]  │
└─────────────────────────────────────────┘
```
- **位置**: 屏幕顶部，状态栏下方
- **样式**: 半透明黑色背景，圆角矩形
- **功能**: 快速切换相机设置
- **动画**: 点击时图标缩放和颜色变化

### 2. 中央预览区域 (Camera Preview)
```
┌─────────────────────────────────────────┐
│                                         │
│     ┌─────┬─────┬─────┐  ← 九宫格网格    │
│     │     │     │     │                 │
│     ├─────┼─────┼─────┤                 │
│     │  📷 │ 👁️  │     │  ← 实时预览     │
│     ├─────┼─────┼─────┤                 │
│     │     │     │     │                 │
│     └─────┴─────┴─────┘                 │
│                                         │
│        🟡 对焦框 (触摸显示)              │
└─────────────────────────────────────────┘
```
- **位置**: 屏幕中央主要区域
- **功能**: 60fps实时预览，触摸对焦
- **覆盖**: 网格线、对焦框、曝光指示器

### 3. 右侧控制栏 (Side Controls)
```
┌───┐
│🔄 │ ← 前后摄像头切换
├───┤
│🔍 │ ← 变焦控制 (1x, 2x, 5x)
├───┤
│☀️ │ ← 曝光补偿 (-2 ~ +2)
├───┤
│🎨 │ ← 滤镜选择
└───┘
```
- **位置**: 屏幕右侧边缘
- **样式**: 垂直排列的圆形按钮
- **动画**: 悬浮效果，点击反馈

### 4. 底部操作栏 (Bottom Controls)
```
┌─────────────────────────────────────────┐
│  📂              ⚪              ⚙️      │
│ 图库缩略图      拍摄按钮        设置菜单   │
│ (最新照片)      (80dp圆形)     (模式切换) │
└─────────────────────────────────────────┘
```
- **位置**: 屏幕底部，导航栏上方
- **样式**: 半透明背景，三等分布局
- **拍摄按钮**: 最大最显眼，带动画效果

### 5. 底部导航栏 (Navigation Bar)
```
┌─────────────────────────────────────────┐
│     📷 相机      🖼️ 图库      ⚙️ 设置    │
│   [当前页面]    [照片管理]   [应用设置]   │
└─────────────────────────────────────────┘
```
- **位置**: 屏幕最底部
- **样式**: Material 3 导航栏
- **状态**: 当前页面高亮显示

## 🎨 视觉设计特点

### 颜色方案
- **背景**: 全黑 (#000000) - 突出预览内容
- **控制栏**: 半透明黑 (#000000, 70% opacity)
- **按钮激活**: 主题橙色 (#FF6B35)
- **文字**: 白色 (#FFFFFF)
- **网格线**: 半透明白 (#FFFFFF, 50% opacity)

### 动画效果
- **页面切换**: 300ms 滑动动画
- **按钮点击**: 150ms 缩放动画
- **状态变化**: 200ms 颜色过渡
- **拍摄按钮**: 弹性动画反馈

### 响应式适配
```
手机竖屏 (360dp+)     平板横屏 (600dp+)
┌─────────────┐      ┌─────────────────────┐
│  顶部控制栏  │      │ 侧边  │   预览区域   │
│             │      │ 导航  │             │
│   预览区域   │  →   │ 栏    │   (更宽)    │
│             │      │       │             │
│  底部操作   │      │       │  底部操作   │
│  导航栏     │      └─────────────────────┘
└─────────────┘
```

## 🔧 交互设计

### 手势操作
- **单击**: 对焦
- **双击**: 快速变焦 (1x ↔ 2x)
- **捏合**: 连续变焦
- **长按**: 锁定曝光/对焦
- **滑动**: 调整曝光补偿

### 按钮反馈
- **视觉**: 颜色变化 + 缩放动画
- **触觉**: 轻微震动反馈
- **音效**: 可选的按键音

### 状态指示
- **拍摄中**: 按钮旋转动画
- **处理中**: 进度指示器
- **错误状态**: 红色闪烁提示
- **成功状态**: 绿色确认动画

## 📐 尺寸规范

### 按钮尺寸
- **拍摄按钮**: 80dp 圆形
- **控制按钮**: 48dp 圆形
- **导航按钮**: 56dp 高度
- **图标**: 24dp 标准尺寸

### 间距规范
- **边距**: 16dp 标准间距
- **按钮间距**: 8dp 紧密间距
- **区域间距**: 24dp 分组间距
- **安全区域**: 遵循系统安全区域

这个布局设计确保了：
✅ **专业性** - 类似专业相机应用的布局
✅ **易用性** - 重要功能触手可及
✅ **美观性** - 现代化的Material 3设计
✅ **响应性** - 适配不同屏幕尺寸
✅ **沉浸性** - 全屏预览体验
