package com.qxyu.yucram.presentation.lutfilter;

/**
 * LUT滤镜UI状态
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u001d\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0083\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0012\u00a2\u0006\u0002\u0010\u0013J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003J\u000f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0012H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\bH\u00c6\u0003J\u000b\u0010(\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010)\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010*\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u0087\u0001\u0010,\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0012H\u00c6\u0001J\u0013\u0010-\u001a\u00020\u00032\b\u0010.\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010/\u001a\u000200H\u00d6\u0001J\t\u00101\u001a\u00020\nH\u00d6\u0001R\u0013\u0010\f\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0016R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0016R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0016R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 \u00a8\u00062"}, d2 = {"Lcom/qxyu/yucram/presentation/lutfilter/LUTFilterUiState;", "", "isLoading", "", "isProcessing", "isGeneratingPreview", "isApplied", "processingProgress", "", "processingMessage", "", "previewPath", "error", "selectedFilterDetails", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "selectedFilterStats", "Lcom/qxyu/yucram/domain/model/LUTFilterStats;", "recentFilters", "", "(ZZZZFLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LUTFilter;Lcom/qxyu/yucram/domain/model/LUTFilterStats;Ljava/util/List;)V", "getError", "()Ljava/lang/String;", "()Z", "getPreviewPath", "getProcessingMessage", "getProcessingProgress", "()F", "getRecentFilters", "()Ljava/util/List;", "getSelectedFilterDetails", "()Lcom/qxyu/yucram/domain/model/LUTFilter;", "getSelectedFilterStats", "()Lcom/qxyu/yucram/domain/model/LUTFilterStats;", "component1", "component10", "component11", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class LUTFilterUiState {
    private final boolean isLoading = false;
    private final boolean isProcessing = false;
    private final boolean isGeneratingPreview = false;
    private final boolean isApplied = false;
    private final float processingProgress = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String processingMessage = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String previewPath = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.LUTFilter selectedFilterDetails = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.LUTFilterStats selectedFilterStats = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.qxyu.yucram.domain.model.LUTFilter> recentFilters = null;
    
    public LUTFilterUiState(boolean isLoading, boolean isProcessing, boolean isGeneratingPreview, boolean isApplied, float processingProgress, @org.jetbrains.annotations.Nullable()
    java.lang.String processingMessage, @org.jetbrains.annotations.Nullable()
    java.lang.String previewPath, @org.jetbrains.annotations.Nullable()
    java.lang.String error, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter selectedFilterDetails, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilterStats selectedFilterStats, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LUTFilter> recentFilters) {
        super();
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isProcessing() {
        return false;
    }
    
    public final boolean isGeneratingPreview() {
        return false;
    }
    
    public final boolean isApplied() {
        return false;
    }
    
    public final float getProcessingProgress() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getProcessingMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPreviewPath() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getError() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilter getSelectedFilterDetails() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilterStats getSelectedFilterStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LUTFilter> getRecentFilters() {
        return null;
    }
    
    public LUTFilterUiState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilterStats component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LUTFilter> component11() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilter component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.lutfilter.LUTFilterUiState copy(boolean isLoading, boolean isProcessing, boolean isGeneratingPreview, boolean isApplied, float processingProgress, @org.jetbrains.annotations.Nullable()
    java.lang.String processingMessage, @org.jetbrains.annotations.Nullable()
    java.lang.String previewPath, @org.jetbrains.annotations.Nullable()
    java.lang.String error, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter selectedFilterDetails, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilterStats selectedFilterStats, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LUTFilter> recentFilters) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}