package com.qxyu.yucram.domain.model;

/**
 * LUT表大小
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTSize;", "", "size", "", "description", "", "(Ljava/lang/String;IILjava/lang/String;)V", "getDescription", "()Ljava/lang/String;", "getSize", "()I", "SIZE_16", "SIZE_32", "SIZE_64", "SIZE_128", "app_debug"})
public enum LUTSize {
    /*public static final*/ SIZE_16 /* = new SIZE_16(0, null) */,
    /*public static final*/ SIZE_32 /* = new SIZE_32(0, null) */,
    /*public static final*/ SIZE_64 /* = new SIZE_64(0, null) */,
    /*public static final*/ SIZE_128 /* = new SIZE_128(0, null) */;
    private final int size = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String description = null;
    
    LUTSize(int size, java.lang.String description) {
    }
    
    public final int getSize() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.LUTSize> getEntries() {
        return null;
    }
}