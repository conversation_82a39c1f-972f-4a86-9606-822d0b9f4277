package com.qxyu.yucram.data.lutfilter

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * CUBE LUT处理器
 * 负责将CUBE LUT应用到图像上
 */
@Singleton
class CubeProcessor @Inject constructor() {
    
    /**
     * 应用CUBE LUT到图像
     */
    suspend fun applyCubeLut(
        inputImagePath: String,
        cubeLutPath: String,
        intensity: Float = 1.0f,
        outputPath: String
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            // 加载原始图像
            val originalBitmap = BitmapFactory.decodeFile(inputImagePath)
                ?: return@withContext Result.failure(Exception("Failed to load image: $inputImagePath"))
            
            // 解析CUBE LUT
            val cubeData = parseCubeLut(cubeLutPath)
                ?: return@withContext Result.failure(Exception("Failed to parse CUBE LUT: $cubeLutPath"))
            
            // 应用LUT到图像
            val processedBitmap = applyLutToBitmap(originalBitmap, cubeData, intensity)
            
            // 保存处理后的图像
            val outputFile = File(outputPath)
            outputFile.parentFile?.mkdirs()
            
            val success = outputFile.outputStream().use { outputStream ->
                processedBitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
            }
            
            // 清理资源
            if (processedBitmap != originalBitmap) {
                processedBitmap.recycle()
            }
            originalBitmap.recycle()
            
            if (success) {
                Result.success(outputPath)
            } else {
                Result.failure(Exception("Failed to save processed image"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 生成预览图像
     */
    suspend fun generatePreview(
        inputImagePath: String,
        cubeLutPath: String,
        intensity: Float = 1.0f,
        previewSize: Int = 512
    ): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            // 加载并缩放原始图像
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(inputImagePath, options)
            
            val scaleFactor = maxOf(
                options.outWidth / previewSize,
                options.outHeight / previewSize
            ).coerceAtLeast(1)
            
            options.inJustDecodeBounds = false
            options.inSampleSize = scaleFactor
            
            val originalBitmap = BitmapFactory.decodeFile(inputImagePath, options)
                ?: return@withContext Result.failure(Exception("Failed to load image for preview"))
            
            // 解析CUBE LUT
            val cubeData = parseCubeLut(cubeLutPath)
                ?: return@withContext Result.failure(Exception("Failed to parse CUBE LUT"))
            
            // 应用LUT
            val previewBitmap = applyLutToBitmap(originalBitmap, cubeData, intensity)
            
            // 清理原始图像
            if (previewBitmap != originalBitmap) {
                originalBitmap.recycle()
            }
            
            Result.success(previewBitmap)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 解析CUBE LUT文件
     */
    private fun parseCubeLut(cubePath: String): CubeLutData? {
        try {
            val file = File(cubePath)
            if (!file.exists()) return null
            
            val lines = file.readLines()
            var size = 0
            var domainMin = floatArrayOf(0f, 0f, 0f)
            var domainMax = floatArrayOf(1f, 1f, 1f)
            val lutData = mutableListOf<FloatArray>()
            
            for (line in lines) {
                val trimmedLine = line.trim()
                
                when {
                    trimmedLine.startsWith("LUT_3D_SIZE") -> {
                        size = trimmedLine.substringAfter("LUT_3D_SIZE").trim().toIntOrNull() ?: 0
                    }
                    
                    trimmedLine.startsWith("DOMAIN_MIN") -> {
                        val values = trimmedLine.substringAfter("DOMAIN_MIN").trim().split("\\s+".toRegex())
                        if (values.size >= 3) {
                            domainMin = floatArrayOf(
                                values[0].toFloatOrNull() ?: 0f,
                                values[1].toFloatOrNull() ?: 0f,
                                values[2].toFloatOrNull() ?: 0f
                            )
                        }
                    }
                    
                    trimmedLine.startsWith("DOMAIN_MAX") -> {
                        val values = trimmedLine.substringAfter("DOMAIN_MAX").trim().split("\\s+".toRegex())
                        if (values.size >= 3) {
                            domainMax = floatArrayOf(
                                values[0].toFloatOrNull() ?: 1f,
                                values[1].toFloatOrNull() ?: 1f,
                                values[2].toFloatOrNull() ?: 1f
                            )
                        }
                    }
                    
                    trimmedLine.matches("^[0-9.-]+\\s+[0-9.-]+\\s+[0-9.-]+$".toRegex()) -> {
                        val values = trimmedLine.split("\\s+".toRegex())
                        if (values.size >= 3) {
                            val rgb = floatArrayOf(
                                values[0].toFloatOrNull() ?: 0f,
                                values[1].toFloatOrNull() ?: 0f,
                                values[2].toFloatOrNull() ?: 0f
                            )
                            lutData.add(rgb)
                        }
                    }
                }
            }
            
            val expectedSize = size * size * size
            if (size > 0 && lutData.size == expectedSize) {
                return CubeLutData(
                    size = size,
                    domainMin = domainMin,
                    domainMax = domainMax,
                    data = lutData.toTypedArray()
                )
            }
            
            return null
        } catch (e: Exception) {
            println("Error parsing CUBE LUT: ${e.message}")
            return null
        }
    }
    
    /**
     * 应用LUT到Bitmap
     */
    private fun applyLutToBitmap(
        bitmap: Bitmap,
        lutData: CubeLutData,
        intensity: Float
    ): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)
        
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        for (i in pixels.indices) {
            val pixel = pixels[i]
            
            // 提取RGB分量
            val r = (pixel shr 16) and 0xFF
            val g = (pixel shr 8) and 0xFF
            val b = pixel and 0xFF
            val a = (pixel shr 24) and 0xFF
            
            // 归一化到0-1范围
            val normalizedR = r / 255f
            val normalizedG = g / 255f
            val normalizedB = b / 255f
            
            // 应用LUT查找
            val lutResult = lookupLut(
                normalizedR, normalizedG, normalizedB,
                lutData
            )
            
            // 混合原始颜色和LUT结果
            val finalR = lerp(normalizedR, lutResult[0], intensity)
            val finalG = lerp(normalizedG, lutResult[1], intensity)
            val finalB = lerp(normalizedB, lutResult[2], intensity)
            
            // 转换回0-255范围并重新组合像素
            val newR = (finalR * 255f).roundToInt().coerceIn(0, 255)
            val newG = (finalG * 255f).roundToInt().coerceIn(0, 255)
            val newB = (finalB * 255f).roundToInt().coerceIn(0, 255)
            
            pixels[i] = (a shl 24) or (newR shl 16) or (newG shl 8) or newB
        }
        
        val resultBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        resultBitmap.setPixels(pixels, 0, width, 0, 0, width, height)
        
        return resultBitmap
    }
    
    /**
     * LUT查找（三线性插值）
     */
    private fun lookupLut(
        r: Float, g: Float, b: Float,
        lutData: CubeLutData
    ): FloatArray {
        val size = lutData.size
        val sizeMinusOne = size - 1f
        
        // 将输入值映射到LUT索引空间
        val rIndex = r * sizeMinusOne
        val gIndex = g * sizeMinusOne
        val bIndex = b * sizeMinusOne
        
        // 获取整数部分和小数部分
        val r0 = floor(rIndex).toInt().coerceIn(0, size - 1)
        val g0 = floor(gIndex).toInt().coerceIn(0, size - 1)
        val b0 = floor(bIndex).toInt().coerceIn(0, size - 1)
        
        val r1 = (r0 + 1).coerceIn(0, size - 1)
        val g1 = (g0 + 1).coerceIn(0, size - 1)
        val b1 = (b0 + 1).coerceIn(0, size - 1)
        
        val rFrac = rIndex - r0
        val gFrac = gIndex - g0
        val bFrac = bIndex - b0
        
        // 三线性插值
        val c000 = lutData.data[r0 * size * size + g0 * size + b0]
        val c001 = lutData.data[r0 * size * size + g0 * size + b1]
        val c010 = lutData.data[r0 * size * size + g1 * size + b0]
        val c011 = lutData.data[r0 * size * size + g1 * size + b1]
        val c100 = lutData.data[r1 * size * size + g0 * size + b0]
        val c101 = lutData.data[r1 * size * size + g0 * size + b1]
        val c110 = lutData.data[r1 * size * size + g1 * size + b0]
        val c111 = lutData.data[r1 * size * size + g1 * size + b1]
        
        val result = FloatArray(3)
        for (i in 0..2) {
            val c00 = lerp(c000[i], c001[i], bFrac)
            val c01 = lerp(c010[i], c011[i], bFrac)
            val c10 = lerp(c100[i], c101[i], bFrac)
            val c11 = lerp(c110[i], c111[i], bFrac)
            
            val c0 = lerp(c00, c01, gFrac)
            val c1 = lerp(c10, c11, gFrac)
            
            result[i] = lerp(c0, c1, rFrac)
        }
        
        return result
    }
    
    /**
     * 线性插值
     */
    private fun lerp(a: Float, b: Float, t: Float): Float {
        return a + t * (b - a)
    }
}

/**
 * CUBE LUT数据
 */
data class CubeLutData(
    val size: Int,
    val domainMin: FloatArray,
    val domainMax: FloatArray,
    val data: Array<FloatArray>
)
