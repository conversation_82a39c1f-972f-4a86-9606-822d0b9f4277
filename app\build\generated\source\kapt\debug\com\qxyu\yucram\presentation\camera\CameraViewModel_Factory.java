// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.camera;

import com.qxyu.yucram.camera.CameraManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CameraViewModel_Factory implements Factory<CameraViewModel> {
  private final Provider<CameraManager> cameraManagerProvider;

  public CameraViewModel_Factory(Provider<CameraManager> cameraManagerProvider) {
    this.cameraManagerProvider = cameraManagerProvider;
  }

  @Override
  public CameraViewModel get() {
    return newInstance(cameraManagerProvider.get());
  }

  public static CameraViewModel_Factory create(Provider<CameraManager> cameraManagerProvider) {
    return new CameraViewModel_Factory(cameraManagerProvider);
  }

  public static CameraViewModel newInstance(CameraManager cameraManager) {
    return new CameraViewModel(cameraManager);
  }
}
