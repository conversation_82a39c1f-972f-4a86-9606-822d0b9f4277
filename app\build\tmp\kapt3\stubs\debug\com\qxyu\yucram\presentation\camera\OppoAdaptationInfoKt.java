package com.qxyu.yucram.presentation.camera;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000*\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0003\u001a(\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a\u001a\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0003\u00a8\u0006\u000f"}, d2 = {"DeviceInfoRow", "", "label", "", "value", "modifier", "Landroidx/compose/ui/Modifier;", "OppoAdaptationInfo", "deviceInfo", "Lcom/qxyu/yucram/utils/DeviceInfo;", "proprietaryFormats", "", "Lcom/qxyu/yucram/utils/ProprietaryFormat;", "ProprietaryFormatRow", "format", "app_debug"})
public final class OppoAdaptationInfoKt {
    
    /**
     * OPPO设备适配信息显示组件
     */
    @androidx.compose.runtime.Composable()
    public static final void OppoAdaptationInfo(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.utils.DeviceInfo deviceInfo, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.utils.ProprietaryFormat> proprietaryFormats, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 设备信息行
     */
    @androidx.compose.runtime.Composable()
    private static final void DeviceInfoRow(java.lang.String label, java.lang.String value, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 专有格式行
     */
    @androidx.compose.runtime.Composable()
    private static final void ProprietaryFormatRow(com.qxyu.yucram.utils.ProprietaryFormat format, androidx.compose.ui.Modifier modifier) {
    }
}