package com.qxyu.yucram.domain.model;

/**
 * 边框设置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b7\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u00c1\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\t\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000b\u0012\b\b\u0002\u0010\u000f\u001a\u00020\t\u0012\u0014\b\u0002\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t0\u0011\u0012\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0013\u0012\b\b\u0002\u0010\u0014\u001a\u00020\t\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0016\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u000b\u0012\b\b\u0002\u0010\u0018\u001a\u00020\t\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u001a\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\u001cJ\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\u0015\u00108\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t0\u0011H\u00c6\u0003J\u000f\u00109\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0013H\u00c6\u0003J\t\u0010:\u001a\u00020\tH\u00c6\u0003J\t\u0010;\u001a\u00020\u0016H\u00c6\u0003J\u0016\u0010<\u001a\u00020\u000bH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b=\u0010\"J\t\u0010>\u001a\u00020\tH\u00c6\u0003J\t\u0010?\u001a\u00020\u001aH\u00c6\u0003J\u0016\u0010@\u001a\u00020\u000bH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bA\u0010\"J\t\u0010B\u001a\u00020\u0005H\u00c6\u0003J\t\u0010C\u001a\u00020\u0007H\u00c6\u0003J\t\u0010D\u001a\u00020\tH\u00c6\u0003J\u0016\u0010E\u001a\u00020\u000bH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bF\u0010\"J\t\u0010G\u001a\u00020\tH\u00c6\u0003J\t\u0010H\u001a\u00020\u0003H\u00c6\u0003J\u0016\u0010I\u001a\u00020\u000bH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bJ\u0010\"J\t\u0010K\u001a\u00020\tH\u00c6\u0003J\u00cf\u0001\u0010L\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\t2\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u000b2\b\b\u0002\u0010\u000f\u001a\u00020\t2\u0014\b\u0002\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t0\u00112\u000e\b\u0002\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00132\b\b\u0002\u0010\u0014\u001a\u00020\t2\b\b\u0002\u0010\u0015\u001a\u00020\u00162\b\b\u0002\u0010\u0017\u001a\u00020\u000b2\b\b\u0002\u0010\u0018\u001a\u00020\t2\b\b\u0002\u0010\u0019\u001a\u00020\u001a2\b\b\u0002\u0010\u001b\u001a\u00020\u000bH\u00c6\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bM\u0010NJ\u0013\u0010O\u001a\u00020\u00032\b\u0010P\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010Q\u001a\u00020RH\u00d6\u0001J\t\u0010S\u001a\u00020TH\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0019\u0010\n\u001a\u00020\u000b\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b!\u0010\"R\u0011\u0010\f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0019\u0010\u001b\u001a\u00020\u000b\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b&\u0010\"R\u0011\u0010\u0019\u001a\u00020\u001a\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0011\u0010\u0014\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010%R\u0017\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010,R\u0019\u0010\u0017\u001a\u00020\u000b\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b-\u0010\"R\u0011\u0010\u0018\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010%R\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0011\u0010\u000f\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010%R\u0019\u0010\u000e\u001a\u00020\u000b\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b2\u0010\"R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010,R\u001d\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00105R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010%\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006U"}, d2 = {"Lcom/qxyu/yucram/domain/model/BorderSettings;", "", "isEnabled", "", "borderType", "Lcom/qxyu/yucram/domain/model/BorderType;", "borderStyle", "Lcom/qxyu/yucram/domain/model/BorderStyle;", "width", "", "color", "Landroidx/compose/ui/graphics/Color;", "cornerRadius", "shadowEnabled", "shadowColor", "shadowBlur", "shadowOffset", "Lkotlin/Pair;", "gradientColors", "", "gradientAngle", "patternType", "Lcom/qxyu/yucram/domain/model/BorderPattern;", "patternColor", "patternSize", "decorativeStyle", "Lcom/qxyu/yucram/domain/model/DecorativeStyle;", "decorativeColor", "(ZLcom/qxyu/yucram/domain/model/BorderType;Lcom/qxyu/yucram/domain/model/BorderStyle;FJFZJFLkotlin/Pair;Ljava/util/List;FLcom/qxyu/yucram/domain/model/BorderPattern;JFLcom/qxyu/yucram/domain/model/DecorativeStyle;JLkotlin/jvm/internal/DefaultConstructorMarker;)V", "getBorderStyle", "()Lcom/qxyu/yucram/domain/model/BorderStyle;", "getBorderType", "()Lcom/qxyu/yucram/domain/model/BorderType;", "getColor-0d7_KjU", "()J", "J", "getCornerRadius", "()F", "getDecorativeColor-0d7_KjU", "getDecorativeStyle", "()Lcom/qxyu/yucram/domain/model/DecorativeStyle;", "getGradientAngle", "getGradientColors", "()Ljava/util/List;", "()Z", "getPatternColor-0d7_KjU", "getPatternSize", "getPatternType", "()Lcom/qxyu/yucram/domain/model/BorderPattern;", "getShadowBlur", "getShadowColor-0d7_KjU", "getShadowEnabled", "getShadowOffset", "()Lkotlin/Pair;", "getWidth", "component1", "component10", "component11", "component12", "component13", "component14", "component14-0d7_KjU", "component15", "component16", "component17", "component17-0d7_KjU", "component2", "component3", "component4", "component5", "component5-0d7_KjU", "component6", "component7", "component8", "component8-0d7_KjU", "component9", "copy", "copy-oQ1KY6c", "(ZLcom/qxyu/yucram/domain/model/BorderType;Lcom/qxyu/yucram/domain/model/BorderStyle;FJFZJFLkotlin/Pair;Ljava/util/List;FLcom/qxyu/yucram/domain/model/BorderPattern;JFLcom/qxyu/yucram/domain/model/DecorativeStyle;J)Lcom/qxyu/yucram/domain/model/BorderSettings;", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class BorderSettings {
    private final boolean isEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.BorderType borderType = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.BorderStyle borderStyle = null;
    private final float width = 0.0F;
    private final long color = 0L;
    private final float cornerRadius = 0.0F;
    private final boolean shadowEnabled = false;
    private final long shadowColor = 0L;
    private final float shadowBlur = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Pair<java.lang.Float, java.lang.Float> shadowOffset = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<androidx.compose.ui.graphics.Color> gradientColors = null;
    private final float gradientAngle = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.BorderPattern patternType = null;
    private final long patternColor = 0L;
    private final float patternSize = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.DecorativeStyle decorativeStyle = null;
    private final long decorativeColor = 0L;
    
    private BorderSettings(boolean isEnabled, com.qxyu.yucram.domain.model.BorderType borderType, com.qxyu.yucram.domain.model.BorderStyle borderStyle, float width, long color, float cornerRadius, boolean shadowEnabled, long shadowColor, float shadowBlur, kotlin.Pair<java.lang.Float, java.lang.Float> shadowOffset, java.util.List<androidx.compose.ui.graphics.Color> gradientColors, float gradientAngle, com.qxyu.yucram.domain.model.BorderPattern patternType, long patternColor, float patternSize, com.qxyu.yucram.domain.model.DecorativeStyle decorativeStyle, long decorativeColor) {
        super();
    }
    
    public final boolean isEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderType getBorderType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderStyle getBorderStyle() {
        return null;
    }
    
    public final float getWidth() {
        return 0.0F;
    }
    
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    public final boolean getShadowEnabled() {
        return false;
    }
    
    public final float getShadowBlur() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Float, java.lang.Float> getShadowOffset() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<androidx.compose.ui.graphics.Color> getGradientColors() {
        return null;
    }
    
    public final float getGradientAngle() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderPattern getPatternType() {
        return null;
    }
    
    public final float getPatternSize() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.DecorativeStyle getDecorativeStyle() {
        return null;
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Float, java.lang.Float> component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<androidx.compose.ui.graphics.Color> component11() {
        return null;
    }
    
    public final float component12() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderPattern component13() {
        return null;
    }
    
    public final float component15() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.DecorativeStyle component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderType component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderStyle component3() {
        return null;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float component6() {
        return 0.0F;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}