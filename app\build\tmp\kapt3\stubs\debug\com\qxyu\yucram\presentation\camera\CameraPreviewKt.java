package com.qxyu.yucram.presentation.camera;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0014\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u001a\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u00a8\u0006\u0006"}, d2 = {"CameraPreview", "", "cameraManager", "Lcom/qxyu/yucram/camera/CameraManager;", "modifier", "Landroidx/compose/ui/Modifier;", "app_debug"})
public final class CameraPreviewKt {
    
    /**
     * 相机预览组件
     * 使用SurfaceView显示相机预览
     */
    @androidx.compose.runtime.Composable()
    public static final void CameraPreview(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.camera.CameraManager cameraManager, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}