// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import android.content.Context;
import com.qxyu.yucram.filter.DeviceCapabilityDetector;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSystemModule_ProvideDeviceCapabilityDetectorFactory implements Factory<DeviceCapabilityDetector> {
  private final Provider<Context> contextProvider;

  public FilterSystemModule_ProvideDeviceCapabilityDetectorFactory(
      Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DeviceCapabilityDetector get() {
    return provideDeviceCapabilityDetector(contextProvider.get());
  }

  public static FilterSystemModule_ProvideDeviceCapabilityDetectorFactory create(
      Provider<Context> contextProvider) {
    return new FilterSystemModule_ProvideDeviceCapabilityDetectorFactory(contextProvider);
  }

  public static DeviceCapabilityDetector provideDeviceCapabilityDetector(Context context) {
    return Preconditions.checkNotNullFromProvides(FilterSystemModule.INSTANCE.provideDeviceCapabilityDetector(context));
  }
}
