package com.qxyu.yucram.domain.model;

/**
 * LUT排序方式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTSortBy;", "", "(Ljava/lang/String;I)V", "NAME", "CATEGORY", "RATING", "USAGE_COUNT", "FILE_SIZE", "CREATED_AT", "UPDATED_AT", "POPULARITY", "app_debug"})
public enum LUTSortBy {
    /*public static final*/ NAME /* = new NAME() */,
    /*public static final*/ CATEGORY /* = new CATEGORY() */,
    /*public static final*/ RATING /* = new RATING() */,
    /*public static final*/ USAGE_COUNT /* = new USAGE_COUNT() */,
    /*public static final*/ FILE_SIZE /* = new FILE_SIZE() */,
    /*public static final*/ CREATED_AT /* = new CREATED_AT() */,
    /*public static final*/ UPDATED_AT /* = new UPDATED_AT() */,
    /*public static final*/ POPULARITY /* = new POPULARITY() */;
    
    LUTSortBy() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.LUTSortBy> getEntries() {
        return null;
    }
}