package com.qxyu.yucram.domain.model;

/**
 * HSL调整参数
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0007\n\u0002\bK\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u00f5\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\u0003\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u001bJ\t\u00105\u001a\u00020\u0003H\u00c6\u0003J\t\u00106\u001a\u00020\u0003H\u00c6\u0003J\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\t\u00108\u001a\u00020\u0003H\u00c6\u0003J\t\u00109\u001a\u00020\u0003H\u00c6\u0003J\t\u0010:\u001a\u00020\u0003H\u00c6\u0003J\t\u0010;\u001a\u00020\u0003H\u00c6\u0003J\t\u0010<\u001a\u00020\u0003H\u00c6\u0003J\t\u0010=\u001a\u00020\u0003H\u00c6\u0003J\t\u0010>\u001a\u00020\u0003H\u00c6\u0003J\t\u0010?\u001a\u00020\u0003H\u00c6\u0003J\t\u0010@\u001a\u00020\u0003H\u00c6\u0003J\t\u0010A\u001a\u00020\u0003H\u00c6\u0003J\t\u0010B\u001a\u00020\u0003H\u00c6\u0003J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\t\u0010D\u001a\u00020\u0003H\u00c6\u0003J\t\u0010E\u001a\u00020\u0003H\u00c6\u0003J\t\u0010F\u001a\u00020\u0003H\u00c6\u0003J\t\u0010G\u001a\u00020\u0003H\u00c6\u0003J\t\u0010H\u001a\u00020\u0003H\u00c6\u0003J\t\u0010I\u001a\u00020\u0003H\u00c6\u0003J\t\u0010J\u001a\u00020\u0003H\u00c6\u0003J\t\u0010K\u001a\u00020\u0003H\u00c6\u0003J\t\u0010L\u001a\u00020\u0003H\u00c6\u0003J\u00f9\u0001\u0010M\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00032\b\b\u0002\u0010\u0011\u001a\u00020\u00032\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0013\u001a\u00020\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u00032\b\b\u0002\u0010\u0016\u001a\u00020\u00032\b\b\u0002\u0010\u0017\u001a\u00020\u00032\b\b\u0002\u0010\u0018\u001a\u00020\u00032\b\b\u0002\u0010\u0019\u001a\u00020\u00032\b\b\u0002\u0010\u001a\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010N\u001a\u00020O2\b\u0010P\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010Q\u001a\u00020RH\u00d6\u0001J\t\u0010S\u001a\u00020TH\u00d6\u0001R\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001dR\u0011\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001dR\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001dR\u0011\u0010\u0011\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001dR\u0011\u0010\u0010\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001dR\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001dR\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001dR\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001dR\u0011\u0010\u0018\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001dR\u0011\u0010\u001a\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u001dR\u0011\u0010\u0019\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\u001dR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\u001dR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u001dR\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001dR\u0011\u0010\u0015\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\u001dR\u0011\u0010\u0017\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u001dR\u0011\u0010\u0016\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\u001dR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u001dR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\u001dR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010\u001dR\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010\u001dR\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010\u001dR\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010\u001d\u00a8\u0006U"}, d2 = {"Lcom/qxyu/yucram/domain/model/HSLAdjustments;", "", "redHue", "", "redSaturation", "redLuminance", "orangeHue", "orangeSaturation", "orangeLuminance", "yellowHue", "yellowSaturation", "yellowLuminance", "greenHue", "greenSaturation", "greenLuminance", "cyanHue", "cyanSaturation", "cyanLuminance", "blueHue", "blueSaturation", "blueLuminance", "purpleHue", "purpleSaturation", "purpleLuminance", "magentaHue", "magentaSaturation", "magentaLuminance", "(FFFFFFFFFFFFFFFFFFFFFFFF)V", "getBlueHue", "()F", "getBlueLuminance", "getBlueSaturation", "getCyanHue", "getCyanLuminance", "getCyanSaturation", "getGreenHue", "getGreenLuminance", "getGreenSaturation", "getMagentaHue", "getMagentaLuminance", "getMagentaSaturation", "getOrangeHue", "getOrangeLuminance", "getOrangeSaturation", "getPurpleHue", "getPurpleLuminance", "getPurpleSaturation", "getRedHue", "getRedLuminance", "getRedSaturation", "getYellowHue", "getYellowLuminance", "getYellowSaturation", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
public final class HSLAdjustments {
    private final float redHue = 0.0F;
    private final float redSaturation = 0.0F;
    private final float redLuminance = 0.0F;
    private final float orangeHue = 0.0F;
    private final float orangeSaturation = 0.0F;
    private final float orangeLuminance = 0.0F;
    private final float yellowHue = 0.0F;
    private final float yellowSaturation = 0.0F;
    private final float yellowLuminance = 0.0F;
    private final float greenHue = 0.0F;
    private final float greenSaturation = 0.0F;
    private final float greenLuminance = 0.0F;
    private final float cyanHue = 0.0F;
    private final float cyanSaturation = 0.0F;
    private final float cyanLuminance = 0.0F;
    private final float blueHue = 0.0F;
    private final float blueSaturation = 0.0F;
    private final float blueLuminance = 0.0F;
    private final float purpleHue = 0.0F;
    private final float purpleSaturation = 0.0F;
    private final float purpleLuminance = 0.0F;
    private final float magentaHue = 0.0F;
    private final float magentaSaturation = 0.0F;
    private final float magentaLuminance = 0.0F;
    
    public HSLAdjustments(float redHue, float redSaturation, float redLuminance, float orangeHue, float orangeSaturation, float orangeLuminance, float yellowHue, float yellowSaturation, float yellowLuminance, float greenHue, float greenSaturation, float greenLuminance, float cyanHue, float cyanSaturation, float cyanLuminance, float blueHue, float blueSaturation, float blueLuminance, float purpleHue, float purpleSaturation, float purpleLuminance, float magentaHue, float magentaSaturation, float magentaLuminance) {
        super();
    }
    
    public final float getRedHue() {
        return 0.0F;
    }
    
    public final float getRedSaturation() {
        return 0.0F;
    }
    
    public final float getRedLuminance() {
        return 0.0F;
    }
    
    public final float getOrangeHue() {
        return 0.0F;
    }
    
    public final float getOrangeSaturation() {
        return 0.0F;
    }
    
    public final float getOrangeLuminance() {
        return 0.0F;
    }
    
    public final float getYellowHue() {
        return 0.0F;
    }
    
    public final float getYellowSaturation() {
        return 0.0F;
    }
    
    public final float getYellowLuminance() {
        return 0.0F;
    }
    
    public final float getGreenHue() {
        return 0.0F;
    }
    
    public final float getGreenSaturation() {
        return 0.0F;
    }
    
    public final float getGreenLuminance() {
        return 0.0F;
    }
    
    public final float getCyanHue() {
        return 0.0F;
    }
    
    public final float getCyanSaturation() {
        return 0.0F;
    }
    
    public final float getCyanLuminance() {
        return 0.0F;
    }
    
    public final float getBlueHue() {
        return 0.0F;
    }
    
    public final float getBlueSaturation() {
        return 0.0F;
    }
    
    public final float getBlueLuminance() {
        return 0.0F;
    }
    
    public final float getPurpleHue() {
        return 0.0F;
    }
    
    public final float getPurpleSaturation() {
        return 0.0F;
    }
    
    public final float getPurpleLuminance() {
        return 0.0F;
    }
    
    public final float getMagentaHue() {
        return 0.0F;
    }
    
    public final float getMagentaSaturation() {
        return 0.0F;
    }
    
    public final float getMagentaLuminance() {
        return 0.0F;
    }
    
    public HSLAdjustments() {
        super();
    }
    
    public final float component1() {
        return 0.0F;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    public final float component12() {
        return 0.0F;
    }
    
    public final float component13() {
        return 0.0F;
    }
    
    public final float component14() {
        return 0.0F;
    }
    
    public final float component15() {
        return 0.0F;
    }
    
    public final float component16() {
        return 0.0F;
    }
    
    public final float component17() {
        return 0.0F;
    }
    
    public final float component18() {
        return 0.0F;
    }
    
    public final float component19() {
        return 0.0F;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    public final float component20() {
        return 0.0F;
    }
    
    public final float component21() {
        return 0.0F;
    }
    
    public final float component22() {
        return 0.0F;
    }
    
    public final float component23() {
        return 0.0F;
    }
    
    public final float component24() {
        return 0.0F;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final float component6() {
        return 0.0F;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.HSLAdjustments copy(float redHue, float redSaturation, float redLuminance, float orangeHue, float orangeSaturation, float orangeLuminance, float yellowHue, float yellowSaturation, float yellowLuminance, float greenHue, float greenSaturation, float greenLuminance, float cyanHue, float cyanSaturation, float cyanLuminance, float blueHue, float blueSaturation, float blueLuminance, float purpleHue, float purpleSaturation, float purpleLuminance, float magentaHue, float magentaSaturation, float magentaLuminance) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}