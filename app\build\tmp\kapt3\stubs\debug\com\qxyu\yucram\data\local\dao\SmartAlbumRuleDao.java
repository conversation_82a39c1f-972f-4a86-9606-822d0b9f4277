package com.qxyu.yucram.data.local.dao;

/**
 * 智能相册规则DAO
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00050\f2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\r\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u000e\u001a\u00020\u00032\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u001e\u0010\u0011\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\t2\u0006\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u0016\u0010\u0016\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006\u0017"}, d2 = {"Lcom/qxyu/yucram/data/local/dao/SmartAlbumRuleDao;", "", "deleteRule", "", "rule", "Lcom/qxyu/yucram/domain/model/SmartAlbumRule;", "(Lcom/qxyu/yucram/domain/model/SmartAlbumRule;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteRulesForAlbum", "albumId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRulesForAlbum", "", "insertRule", "insertRules", "rules", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "setRuleEnabled", "ruleId", "enabled", "", "(Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateRule", "app_debug"})
@androidx.room.Dao()
public abstract interface SmartAlbumRuleDao {
    
    /**
     * 插入规则
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertRule(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SmartAlbumRule rule, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 批量插入规则
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertRules(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule> rules, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 更新规则
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateRule(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SmartAlbumRule rule, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 删除规则
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteRule(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SmartAlbumRule rule, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取相册的所有规则
     */
    @androidx.room.Query(value = "SELECT * FROM smart_album_rules WHERE albumId = :albumId AND isEnabled = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRulesForAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.qxyu.yucram.domain.model.SmartAlbumRule>> $completion);
    
    /**
     * 删除相册的所有规则
     */
    @androidx.room.Query(value = "DELETE FROM smart_album_rules WHERE albumId = :albumId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteRulesForAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 启用/禁用规则
     */
    @androidx.room.Query(value = "UPDATE smart_album_rules SET isEnabled = :enabled WHERE id = :ruleId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object setRuleEnabled(@org.jetbrains.annotations.NotNull()
    java.lang.String ruleId, boolean enabled, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}