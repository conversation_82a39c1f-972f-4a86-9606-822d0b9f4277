package com.qxyu.yucram.data.local.dao

import androidx.room.*
import com.qxyu.yucram.data.local.entity.PhotoEntity
import kotlinx.coroutines.flow.Flow

/**
 * 照片数据访问对象
 */
@Dao
interface PhotoDao {

    // ========== 基础查询 ==========

    /**
     * 获取所有照片
     */
    @Query("SELECT * FROM photos ORDER BY dateCreated DESC")
    fun getAllPhotos(): Flow<List<PhotoEntity>>

    /**
     * 获取所有照片（Flow）
     */
    @Query("SELECT * FROM photos ORDER BY dateCreated DESC")
    fun getAllPhotosFlow(): Flow<List<PhotoEntity>>

    /**
     * 根据ID获取照片
     */
    @Query("SELECT * FROM photos WHERE id = :id")
    suspend fun getPhotoById(id: Long): PhotoEntity?

    /**
     * 获取照片数量
     */
    @Query("SELECT COUNT(*) FROM photos")
    suspend fun getPhotoCount(): Int

    // ========== 插入和更新 ==========

    /**
     * 插入照片
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPhoto(photo: PhotoEntity): Long

    /**
     * 批量插入照片
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPhotos(photos: List<PhotoEntity>)

    /**
     * 更新照片
     */
    @Update
    suspend fun updatePhoto(photo: PhotoEntity)

    /**
     * 删除照片
     */
    @Delete
    suspend fun deletePhoto(photo: PhotoEntity)

    // ========== 收藏操作 ==========

    /**
     * 获取收藏照片
     */
    @Query("SELECT * FROM photos WHERE isFavorite = 1 ORDER BY dateCreated DESC")
    fun getFavoritePhotos(): Flow<List<PhotoEntity>>

    /**
     * 根据标签搜索照片
     */
    @Query("SELECT * FROM photos WHERE tags LIKE '%' || :tag || '%' ORDER BY dateCreated DESC")
    fun searchPhotosByTag(tag: String): Flow<List<PhotoEntity>>

    /**
     * 搜索照片（文件名）
     */
    @Query("SELECT * FROM photos WHERE fileName LIKE '%' || :query || '%' ORDER BY dateCreated DESC")
    fun searchPhotos(query: String): Flow<List<PhotoEntity>>

    /**
     * 根据文件路径查询照片
     */
    @Query("SELECT * FROM photos WHERE filePath = :filePath")
    suspend fun getPhotoByPath(filePath: String): PhotoEntity?

    /**
     * 检查文件是否已存在
     */
    @Query("SELECT COUNT(*) > 0 FROM photos WHERE filePath = :filePath")
    suspend fun isPhotoExists(filePath: String): Boolean

    /**
     * 删除所有照片
     */
    @Query("DELETE FROM photos")
    suspend fun deleteAllPhotos()
}
