// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import com.qxyu.yucram.data.repository.AlbumRepositoryImpl;
import com.qxyu.yucram.domain.repository.AlbumRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSystemModule_ProvideAlbumRepositoryFactory implements Factory<AlbumRepository> {
  private final Provider<AlbumRepositoryImpl> albumRepositoryImplProvider;

  public FilterSystemModule_ProvideAlbumRepositoryFactory(
      Provider<AlbumRepositoryImpl> albumRepositoryImplProvider) {
    this.albumRepositoryImplProvider = albumRepositoryImplProvider;
  }

  @Override
  public AlbumRepository get() {
    return provideAlbumRepository(albumRepositoryImplProvider.get());
  }

  public static FilterSystemModule_ProvideAlbumRepositoryFactory create(
      Provider<AlbumRepositoryImpl> albumRepositoryImplProvider) {
    return new FilterSystemModule_ProvideAlbumRepositoryFactory(albumRepositoryImplProvider);
  }

  public static AlbumRepository provideAlbumRepository(AlbumRepositoryImpl albumRepositoryImpl) {
    return Preconditions.checkNotNullFromProvides(FilterSystemModule.INSTANCE.provideAlbumRepository(albumRepositoryImpl));
  }
}
