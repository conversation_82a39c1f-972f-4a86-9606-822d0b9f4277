package com.qxyu.yucram.presentation.utils

import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 屏幕尺寸类型
 */
enum class ScreenSize {
    COMPACT,    // 手机竖屏
    MEDIUM,     // 手机横屏/小平板
    EXPANDED    // 大平板
}

/**
 * 屏幕方向
 */
enum class ScreenOrientation {
    PORTRAIT,
    LANDSCAPE
}

/**
 * 设备类型
 */
enum class DeviceType {
    PHONE,
    TABLET,
    DESKTOP
}

/**
 * 响应式布局信息
 */
data class ResponsiveLayoutInfo(
    val screenSize: ScreenSize,
    val orientation: ScreenOrientation,
    val deviceType: DeviceType,
    val screenWidth: Dp,
    val screenHeight: Dp,
    val isCompact: <PERSON><PERSON><PERSON>,
    val isMedium: <PERSON>olean,
    val isExpanded: <PERSON>olean,
    val isPortrait: <PERSON>olean,
    val isLandscape: Boolean,
    val isPhone: Boolean,
    val isTablet: Boolean
)

/**
 * 响应式布局组件
 */
@Composable
fun ResponsiveLayout(
    modifier: Modifier = Modifier,
    content: @Composable (ResponsiveLayoutInfo) -> Unit
) {
    BoxWithConstraints(
        modifier = modifier.fillMaxSize()
    ) {
        val configuration = LocalConfiguration.current
        val density = LocalDensity.current
        
        val screenWidth = maxWidth
        val screenHeight = maxHeight
        
        // 确定屏幕尺寸
        val screenSize = when {
            screenWidth < 600.dp -> ScreenSize.COMPACT
            screenWidth < 840.dp -> ScreenSize.MEDIUM
            else -> ScreenSize.EXPANDED
        }
        
        // 确定屏幕方向
        val orientation = if (screenWidth > screenHeight) {
            ScreenOrientation.LANDSCAPE
        } else {
            ScreenOrientation.PORTRAIT
        }
        
        // 确定设备类型
        val deviceType = when {
            screenWidth < 600.dp -> DeviceType.PHONE
            screenWidth < 1200.dp -> DeviceType.TABLET
            else -> DeviceType.DESKTOP
        }
        
        val layoutInfo = ResponsiveLayoutInfo(
            screenSize = screenSize,
            orientation = orientation,
            deviceType = deviceType,
            screenWidth = screenWidth,
            screenHeight = screenHeight,
            isCompact = screenSize == ScreenSize.COMPACT,
            isMedium = screenSize == ScreenSize.MEDIUM,
            isExpanded = screenSize == ScreenSize.EXPANDED,
            isPortrait = orientation == ScreenOrientation.PORTRAIT,
            isLandscape = orientation == ScreenOrientation.LANDSCAPE,
            isPhone = deviceType == DeviceType.PHONE,
            isTablet = deviceType == DeviceType.TABLET
        )
        
        content(layoutInfo)
    }
}

/**
 * 获取响应式布局信息的Composable函数
 */
@Composable
fun rememberResponsiveLayoutInfo(): ResponsiveLayoutInfo {
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current
    
    return remember(configuration.screenWidthDp, configuration.screenHeightDp) {
        val screenWidth = configuration.screenWidthDp.dp
        val screenHeight = configuration.screenHeightDp.dp
        
        val screenSize = when {
            screenWidth < 600.dp -> ScreenSize.COMPACT
            screenWidth < 840.dp -> ScreenSize.MEDIUM
            else -> ScreenSize.EXPANDED
        }
        
        val orientation = if (screenWidth > screenHeight) {
            ScreenOrientation.LANDSCAPE
        } else {
            ScreenOrientation.PORTRAIT
        }
        
        val deviceType = when {
            screenWidth < 600.dp -> DeviceType.PHONE
            screenWidth < 1200.dp -> DeviceType.TABLET
            else -> DeviceType.DESKTOP
        }
        
        ResponsiveLayoutInfo(
            screenSize = screenSize,
            orientation = orientation,
            deviceType = deviceType,
            screenWidth = screenWidth,
            screenHeight = screenHeight,
            isCompact = screenSize == ScreenSize.COMPACT,
            isMedium = screenSize == ScreenSize.MEDIUM,
            isExpanded = screenSize == ScreenSize.EXPANDED,
            isPortrait = orientation == ScreenOrientation.PORTRAIT,
            isLandscape = orientation == ScreenOrientation.LANDSCAPE,
            isPhone = deviceType == DeviceType.PHONE,
            isTablet = deviceType == DeviceType.TABLET
        )
    }
}

/**
 * 响应式值选择器
 */
@Composable
fun <T> responsiveValue(
    compact: T,
    medium: T = compact,
    expanded: T = medium
): T {
    val layoutInfo = rememberResponsiveLayoutInfo()
    return when (layoutInfo.screenSize) {
        ScreenSize.COMPACT -> compact
        ScreenSize.MEDIUM -> medium
        ScreenSize.EXPANDED -> expanded
    }
}

/**
 * 响应式Padding值
 */
@Composable
fun responsivePadding(): Dp {
    return responsiveValue(
        compact = 16.dp,
        medium = 24.dp,
        expanded = 32.dp
    )
}

/**
 * 响应式网格列数
 */
@Composable
fun responsiveGridColumns(): Int {
    return responsiveValue(
        compact = 2,
        medium = 3,
        expanded = 4
    )
}

/**
 * 响应式字体缩放
 */
@Composable
fun responsiveFontScale(): Float {
    return responsiveValue(
        compact = 1.0f,
        medium = 1.1f,
        expanded = 1.2f
    )
}

/**
 * 判断是否应该使用导航抽屉
 */
@Composable
fun shouldUseNavigationDrawer(): Boolean {
    val layoutInfo = rememberResponsiveLayoutInfo()
    return layoutInfo.isExpanded && layoutInfo.isLandscape
}

/**
 * 判断是否应该使用导航栏
 */
@Composable
fun shouldUseNavigationRail(): Boolean {
    val layoutInfo = rememberResponsiveLayoutInfo()
    return layoutInfo.isMedium && layoutInfo.isLandscape
}

/**
 * 判断是否应该使用底部导航
 */
@Composable
fun shouldUseBottomNavigation(): Boolean {
    val layoutInfo = rememberResponsiveLayoutInfo()
    return layoutInfo.isCompact || (layoutInfo.isMedium && layoutInfo.isPortrait)
}
