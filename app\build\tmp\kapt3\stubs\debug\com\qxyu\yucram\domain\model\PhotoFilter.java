package com.qxyu.yucram.domain.model;

/**
 * 照片筛选条件
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b$\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u009d\u0001\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\r\u0012\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\r\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\u0002\u0010\u0014J\u000b\u0010\'\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010(\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010!J\u0010\u0010)\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010!J\u000b\u0010*\u001a\u0004\u0018\u00010\u0013H\u00c6\u0003J\u000b\u0010+\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010,\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010-\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010.\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010/\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001dJ\u0010\u00100\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001dJ\u000f\u00101\u001a\b\u0012\u0004\u0012\u00020\u00050\rH\u00c6\u0003J\u000f\u00102\u001a\b\u0012\u0004\u0012\u00020\u00050\rH\u00c6\u0003J\u00a6\u0001\u00103\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n2\u000e\b\u0002\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\r2\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\r2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00102\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00102\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0013H\u00c6\u0001\u00a2\u0006\u0002\u00104J\u0013\u00105\u001a\u00020\n2\b\u00106\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00107\u001a\u000208H\u00d6\u0001J\t\u00109\u001a\u00020\u0005H\u00d6\u0001R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0018R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0013\u0010\b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0018R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u0010\u001e\u001a\u0004\b\u000b\u0010\u001dR\u0015\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u0010\u001e\u001a\u0004\b\t\u0010\u001dR\u0013\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0018R\u0015\u0010\u0011\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\n\n\u0002\u0010\"\u001a\u0004\b \u0010!R\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\n\n\u0002\u0010\"\u001a\u0004\b#\u0010!R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\r\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0016\u00a8\u0006:"}, d2 = {"Lcom/qxyu/yucram/domain/model/PhotoFilter;", "", "dateRange", "Lcom/qxyu/yucram/domain/model/DateRange;", "location", "", "cameraMake", "cameraModel", "filterUsed", "isYucramPhoto", "", "isFavorite", "tags", "", "albumIds", "minFileSize", "", "maxFileSize", "orientation", "Lcom/qxyu/yucram/domain/model/PhotoOrientation;", "(Lcom/qxyu/yucram/domain/model/DateRange;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/util/List;Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;Lcom/qxyu/yucram/domain/model/PhotoOrientation;)V", "getAlbumIds", "()Ljava/util/List;", "getCameraMake", "()Ljava/lang/String;", "getCameraModel", "getDateRange", "()Lcom/qxyu/yucram/domain/model/DateRange;", "getFilterUsed", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getLocation", "getMaxFileSize", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMinFileSize", "getOrientation", "()Lcom/qxyu/yucram/domain/model/PhotoOrientation;", "getTags", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Lcom/qxyu/yucram/domain/model/DateRange;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/util/List;Ljava/util/List;Ljava/lang/Long;Ljava/lang/Long;Lcom/qxyu/yucram/domain/model/PhotoOrientation;)Lcom/qxyu/yucram/domain/model/PhotoFilter;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class PhotoFilter {
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.DateRange dateRange = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String location = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String cameraMake = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String cameraModel = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String filterUsed = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean isYucramPhoto = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean isFavorite = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> tags = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> albumIds = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long minFileSize = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long maxFileSize = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.PhotoOrientation orientation = null;
    
    public PhotoFilter(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.DateRange dateRange, @org.jetbrains.annotations.Nullable()
    java.lang.String location, @org.jetbrains.annotations.Nullable()
    java.lang.String cameraMake, @org.jetbrains.annotations.Nullable()
    java.lang.String cameraModel, @org.jetbrains.annotations.Nullable()
    java.lang.String filterUsed, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isYucramPhoto, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isFavorite, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> tags, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> albumIds, @org.jetbrains.annotations.Nullable()
    java.lang.Long minFileSize, @org.jetbrains.annotations.Nullable()
    java.lang.Long maxFileSize, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoOrientation orientation) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.DateRange getDateRange() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLocation() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCameraMake() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCameraModel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFilterUsed() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean isYucramPhoto() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean isFavorite() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getTags() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAlbumIds() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getMinFileSize() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getMaxFileSize() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoOrientation getOrientation() {
        return null;
    }
    
    public PhotoFilter() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.DateRange component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoOrientation component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoFilter copy(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.DateRange dateRange, @org.jetbrains.annotations.Nullable()
    java.lang.String location, @org.jetbrains.annotations.Nullable()
    java.lang.String cameraMake, @org.jetbrains.annotations.Nullable()
    java.lang.String cameraModel, @org.jetbrains.annotations.Nullable()
    java.lang.String filterUsed, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isYucramPhoto, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isFavorite, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> tags, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> albumIds, @org.jetbrains.annotations.Nullable()
    java.lang.Long minFileSize, @org.jetbrains.annotations.Nullable()
    java.lang.Long maxFileSize, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoOrientation orientation) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}