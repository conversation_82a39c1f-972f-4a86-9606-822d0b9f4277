package com.qxyu.yucram.domain.model;

/**
 * 处理后的图像
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B9\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001d\u001a\u00020\nH\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\fH\u00c6\u0003JG\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00c6\u0001J\u0013\u0010 \u001a\u00020!2\b\u0010\"\u001a\u0004\u0018\u00010\u0001H\u0096\u0002J\b\u0010#\u001a\u00020\u0005H\u0016J\t\u0010$\u001a\u00020%H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015\u00a8\u0006&"}, d2 = {"Lcom/qxyu/yucram/domain/model/ProcessedImage;", "", "data", "", "width", "", "height", "format", "Lcom/qxyu/yucram/domain/model/ProcessedImageFormat;", "colorSpace", "Lcom/qxyu/yucram/domain/model/ColorSpace;", "metadata", "Lcom/qxyu/yucram/domain/model/ImageMetadata;", "([BIILcom/qxyu/yucram/domain/model/ProcessedImageFormat;Lcom/qxyu/yucram/domain/model/ColorSpace;Lcom/qxyu/yucram/domain/model/ImageMetadata;)V", "getColorSpace", "()Lcom/qxyu/yucram/domain/model/ColorSpace;", "getData", "()[B", "getFormat", "()Lcom/qxyu/yucram/domain/model/ProcessedImageFormat;", "getHeight", "()I", "getMetadata", "()Lcom/qxyu/yucram/domain/model/ImageMetadata;", "getWidth", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
public final class ProcessedImage {
    @org.jetbrains.annotations.NotNull()
    private final byte[] data = null;
    private final int width = 0;
    private final int height = 0;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ProcessedImageFormat format = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ColorSpace colorSpace = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.ImageMetadata metadata = null;
    
    public ProcessedImage(@org.jetbrains.annotations.NotNull()
    byte[] data, int width, int height, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ProcessedImageFormat format, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorSpace colorSpace, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ImageMetadata metadata) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] getData() {
        return null;
    }
    
    public final int getWidth() {
        return 0;
    }
    
    public final int getHeight() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ProcessedImageFormat getFormat() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorSpace getColorSpace() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ImageMetadata getMetadata() {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final byte[] component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ProcessedImageFormat component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorSpace component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ImageMetadata component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ProcessedImage copy(@org.jetbrains.annotations.NotNull()
    byte[] data, int width, int height, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ProcessedImageFormat format, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorSpace colorSpace, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ImageMetadata metadata) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}