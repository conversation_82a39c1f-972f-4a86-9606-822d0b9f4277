package com.qxyu.yucram.presentation.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\u001a\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a \u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a*\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0003\u00a8\u0006\u0010"}, d2 = {"BottomNavigationBar", "", "navController", "Landroidx/navigation/NavController;", "modifier", "Landroidx/compose/ui/Modifier;", "FloatingCameraButton", "onClick", "Lkotlin/Function0;", "NavigationIcon", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "isSelected", "", "label", "", "app_debug"})
public final class BottomNavigationBarKt {
    
    /**
     * 底部导航栏组件
     */
    @androidx.compose.runtime.Composable()
    public static final void BottomNavigationBar(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 导航图标组件
     */
    @androidx.compose.runtime.Composable()
    private static final void NavigationIcon(androidx.compose.ui.graphics.vector.ImageVector icon, boolean isSelected, java.lang.String label, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 浮动相机按钮（可选的替代设计）
     */
    @androidx.compose.runtime.Composable()
    public static final void FloatingCameraButton(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}