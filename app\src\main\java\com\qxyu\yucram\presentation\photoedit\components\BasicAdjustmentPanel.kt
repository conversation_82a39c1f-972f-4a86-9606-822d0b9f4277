package com.qxyu.yucram.presentation.photoedit.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.PhotoEditParams

/**
 * 基础调整面板
 */
@Composable
fun BasicAdjustmentPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "基础调整",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 曝光
            item {
                AdjustmentSlider(
                    label = "曝光",
                    value = editParams.exposure,
                    valueRange = -2f..2f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(exposure = value))
                    },
                    formatValue = { value ->
                        when {
                            value > 0 -> "+${String.format("%.1f", value)}"
                            value < 0 -> String.format("%.1f", value)
                            else -> "0"
                        }
                    }
                )
            }
            
            // 高光
            item {
                AdjustmentSlider(
                    label = "高光",
                    value = editParams.highlights,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(highlights = value))
                    }
                )
            }
            
            // 阴影
            item {
                AdjustmentSlider(
                    label = "阴影",
                    value = editParams.shadows,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(shadows = value))
                    }
                )
            }
            
            // 白色
            item {
                AdjustmentSlider(
                    label = "白色",
                    value = editParams.whites,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(whites = value))
                    }
                )
            }
            
            // 黑色
            item {
                AdjustmentSlider(
                    label = "黑色",
                    value = editParams.blacks,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(blacks = value))
                    }
                )
            }
            
            // 对比度
            item {
                AdjustmentSlider(
                    label = "对比度",
                    value = editParams.contrast,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(contrast = value))
                    }
                )
            }
            
            // 亮度
            item {
                AdjustmentSlider(
                    label = "亮度",
                    value = editParams.brightness,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(brightness = value))
                    }
                )
            }
            
            // 自然饱和度
            item {
                AdjustmentSlider(
                    label = "自然饱和度",
                    value = editParams.vibrance,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(vibrance = value))
                    }
                )
            }
            
            // 饱和度
            item {
                AdjustmentSlider(
                    label = "饱和度",
                    value = editParams.saturation,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(saturation = value))
                    }
                )
            }
            
            // 色温
            item {
                AdjustmentSlider(
                    label = "色温",
                    value = editParams.temperature,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(temperature = value))
                    },
                    formatValue = { value ->
                        when {
                            value > 0 -> "暖色 +${value.toInt()}"
                            value < 0 -> "冷色 ${value.toInt()}"
                            else -> "0"
                        }
                    }
                )
            }
            
            // 色调
            item {
                AdjustmentSlider(
                    label = "色调",
                    value = editParams.tint,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(tint = value))
                    },
                    formatValue = { value ->
                        when {
                            value > 0 -> "洋红 +${value.toInt()}"
                            value < 0 -> "绿色 ${value.toInt()}"
                            else -> "0"
                        }
                    }
                )
            }
            
            // 锐度
            item {
                AdjustmentSlider(
                    label = "锐度",
                    value = editParams.sharpness,
                    valueRange = 0f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(sharpness = value))
                    }
                )
            }
            
            // 降噪
            item {
                AdjustmentSlider(
                    label = "降噪",
                    value = editParams.noiseReduction,
                    valueRange = 0f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(noiseReduction = value))
                    }
                )
            }
            
            // 清晰度
            item {
                AdjustmentSlider(
                    label = "清晰度",
                    value = editParams.clarity,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(clarity = value))
                    }
                )
            }
            
            // 去雾
            item {
                AdjustmentSlider(
                    label = "去雾",
                    value = editParams.dehaze,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(dehaze = value))
                    }
                )
            }
            
            // 暗角
            item {
                AdjustmentSlider(
                    label = "暗角",
                    value = editParams.vignette,
                    valueRange = -100f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(vignette = value))
                    }
                )
            }
            
            // 颗粒
            item {
                AdjustmentSlider(
                    label = "颗粒",
                    value = editParams.grain,
                    valueRange = 0f..100f,
                    onValueChanged = { value ->
                        onParamsChanged(editParams.copy(grain = value))
                    }
                )
            }
            
            // 重置按钮
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    TextButton(
                        onClick = {
                            onParamsChanged(PhotoEditParams())
                        }
                    ) {
                        Text(
                            text = "重置全部",
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}

/**
 * 调整滑块组件
 */
@Composable
fun AdjustmentSlider(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChanged: (Float) -> Unit,
    modifier: Modifier = Modifier,
    formatValue: (Float) -> String = { value ->
        when {
            value > 0 -> "+${value.toInt()}"
            value < 0 -> value.toInt().toString()
            else -> "0"
        }
    }
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 标签和数值
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            Text(
                text = formatValue(value),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 滑块
        Slider(
            value = value,
            onValueChange = onValueChanged,
            valueRange = valueRange,
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary,
                activeTrackColor = MaterialTheme.colorScheme.primary,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            )
        )
    }
}
