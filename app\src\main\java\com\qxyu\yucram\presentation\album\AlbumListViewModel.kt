package com.qxyu.yucram.presentation.album

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.AlbumRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 相册列表ViewModel
 */
@HiltViewModel
class AlbumListViewModel @Inject constructor(
    private val albumRepository: AlbumRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(AlbumListUiState())
    val uiState: StateFlow<AlbumListUiState> = _uiState.asStateFlow()
    
    // ========== 相册数据 ==========
    
    val albums: StateFlow<List<Album>> = albumRepository.getAllAlbums()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    val systemAlbums: StateFlow<List<Album>> = albumRepository.getSystemAlbums()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    val userAlbums: StateFlow<List<Album>> = albumRepository.getUserAlbums()
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    // ========== 相册操作 ==========
    
    fun loadAlbums() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                // 初始化系统相册
                albumRepository.initializeSystemAlbums()
                
                _uiState.update { it.copy(isLoading = false) }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载相册失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    fun createAlbum(name: String, description: String) {
        viewModelScope.launch {
            try {
                val request = CreateAlbumRequest(
                    name = name,
                    description = description.takeIf { it.isNotBlank() },
                    albumType = AlbumType.USER_CREATED
                )
                
                val result = albumRepository.createAlbum(request)
                
                result.fold(
                    onSuccess = { album ->
                        _uiState.update { 
                            it.copy(message = "相册创建成功")
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(error = "创建相册失败: ${error.message}")
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "创建相册失败: ${e.message}")
                }
            }
        }
    }
    
    fun searchAlbums(query: String) {
        viewModelScope.launch {
            try {
                // TODO: 实现搜索功能
                _uiState.update { it.copy(searchQuery = query) }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "搜索失败: ${e.message}")
                }
            }
        }
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
}

/**
 * 相册详情ViewModel
 */
@HiltViewModel
class AlbumDetailViewModel @Inject constructor(
    private val albumRepository: AlbumRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(AlbumDetailUiState())
    val uiState: StateFlow<AlbumDetailUiState> = _uiState.asStateFlow()
    
    private val _album = MutableStateFlow<Album?>(null)
    val album: StateFlow<Album?> = _album.asStateFlow()
    
    private val _photos = MutableStateFlow<List<Photo>>(emptyList())
    val photos: StateFlow<List<Photo>> = _photos.asStateFlow()
    
    private val _selectionState = MutableStateFlow(PhotoSelectionState())
    val selectionState: StateFlow<PhotoSelectionState> = _selectionState.asStateFlow()
    
    // ========== 相册加载 ==========
    
    fun loadAlbum(albumId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                // 加载相册信息
                val album = albumRepository.getAlbumById(albumId)
                if (album != null) {
                    _album.value = album
                    
                    // 加载相册中的照片
                    albumRepository.getPhotosInAlbum(albumId).collect { photoList ->
                        _photos.value = photoList
                    }
                    
                    _uiState.update { it.copy(isLoading = false) }
                } else {
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = "相册不存在"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载相册失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 相册操作 ==========
    
    fun updateAlbum(name: String, description: String, color: AlbumColor?) {
        val currentAlbum = _album.value ?: return
        
        viewModelScope.launch {
            try {
                val request = UpdateAlbumRequest(
                    name = name,
                    description = description.takeIf { it.isNotBlank() },
                    color = color
                )
                
                val result = albumRepository.updateAlbum(currentAlbum.id, request)
                
                result.fold(
                    onSuccess = { updatedAlbum ->
                        _album.value = updatedAlbum
                        _uiState.update { 
                            it.copy(message = "相册更新成功")
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(error = "更新相册失败: ${error.message}")
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "更新相册失败: ${e.message}")
                }
            }
        }
    }
    
    fun deleteAlbum() {
        val currentAlbum = _album.value ?: return
        
        viewModelScope.launch {
            try {
                val result = albumRepository.deleteAlbum(currentAlbum.id)
                
                result.fold(
                    onSuccess = {
                        _uiState.update { 
                            it.copy(
                                message = "相册删除成功",
                                isDeleted = true
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(error = "删除相册失败: ${error.message}")
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "删除相册失败: ${e.message}")
                }
            }
        }
    }
    
    // ========== 照片操作 ==========
    
    fun addPhotosToAlbum(photoIds: List<String>) {
        val currentAlbum = _album.value ?: return
        
        viewModelScope.launch {
            try {
                val result = albumRepository.addPhotosToAlbum(currentAlbum.id, photoIds)
                
                result.fold(
                    onSuccess = {
                        _uiState.update { 
                            it.copy(message = "照片添加成功")
                        }
                        // 重新加载照片列表
                        loadAlbum(currentAlbum.id)
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(error = "添加照片失败: ${error.message}")
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "添加照片失败: ${e.message}")
                }
            }
        }
    }
    
    fun removePhotosFromAlbum(photoIds: List<String>) {
        val currentAlbum = _album.value ?: return
        
        viewModelScope.launch {
            try {
                val result = albumRepository.removePhotosFromAlbum(currentAlbum.id, photoIds)
                
                result.fold(
                    onSuccess = {
                        _uiState.update { 
                            it.copy(message = "照片移除成功")
                        }
                        // 重新加载照片列表
                        loadAlbum(currentAlbum.id)
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(error = "移除照片失败: ${error.message}")
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "移除照片失败: ${e.message}")
                }
            }
        }
    }
    
    // ========== 选择操作 ==========
    
    fun enterSelectionMode(photoId: String? = null) {
        _selectionState.update { 
            it.enterSelectionMode(photoId)
        }
    }
    
    fun togglePhotoSelection(photoId: String) {
        _selectionState.update { 
            it.toggleSelection(photoId)
        }
    }
    
    fun selectAllPhotos() {
        val allPhotoIds = _photos.value.map { it.id }.toSet()
        _selectionState.update { 
            it.copy(selectedPhotos = allPhotoIds)
        }
    }
    
    fun clearSelection() {
        _selectionState.update { 
            PhotoSelectionState()
        }
    }
    
    fun deleteSelectedPhotos() {
        val selectedPhotoIds = _selectionState.value.selectedPhotos.toList()
        if (selectedPhotoIds.isNotEmpty()) {
            removePhotosFromAlbum(selectedPhotoIds)
            clearSelection()
        }
    }
    
    fun togglePhotoFavorite(photoId: String) {
        // TODO: 实现收藏功能
        viewModelScope.launch {
            try {
                // 调用PhotoRepository的setFavorite方法
                _uiState.update { 
                    it.copy(message = "收藏状态已更新")
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "更新收藏状态失败: ${e.message}")
                }
            }
        }
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
}

/**
 * 相册列表UI状态
 */
data class AlbumListUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val searchQuery: String = ""
)

/**
 * 相册详情UI状态
 */
data class AlbumDetailUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val isDeleted: Boolean = false
)
