package com.qxyu.yucram.domain.model;

/**
 * 照片选择状态
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0010\n\u0002\u0010 \n\u0002\b\u0003\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007J\u0006\u0010\u000f\u001a\u00020\u0000J\u000f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0006H\u00c6\u0003J#\u0010\u0012\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u00c6\u0001J\u0012\u0010\u0013\u001a\u00020\u00002\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0004J\u0013\u0010\u0015\u001a\u00020\u00062\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\nH\u00d6\u0001J\u000e\u0010\u0018\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u0004J\u0014\u0010\u0019\u001a\u00020\u00002\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00040\u001bJ\t\u0010\u001c\u001a\u00020\u0004H\u00d6\u0001J\u000e\u0010\u001d\u001a\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u0004R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\bR\u0011\u0010\t\u001a\u00020\n8F\u00a2\u0006\u0006\u001a\u0004\b\u000b\u0010\fR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001e"}, d2 = {"Lcom/qxyu/yucram/domain/model/PhotoSelectionState;", "", "selectedPhotos", "", "", "isSelectionMode", "", "(Ljava/util/Set;Z)V", "()Z", "selectedCount", "", "getSelectedCount", "()I", "getSelectedPhotos", "()Ljava/util/Set;", "clearSelection", "component1", "component2", "copy", "enterSelectionMode", "photoId", "equals", "other", "hashCode", "isSelected", "selectAll", "photoIds", "", "toString", "toggleSelection", "app_debug"})
public final class PhotoSelectionState {
    @org.jetbrains.annotations.NotNull()
    private final java.util.Set<java.lang.String> selectedPhotos = null;
    private final boolean isSelectionMode = false;
    
    public PhotoSelectionState(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedPhotos, boolean isSelectionMode) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> getSelectedPhotos() {
        return null;
    }
    
    public final boolean isSelectionMode() {
        return false;
    }
    
    public final int getSelectedCount() {
        return 0;
    }
    
    public final boolean isSelected(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoSelectionState toggleSelection(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoSelectionState selectAll(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> photoIds) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoSelectionState clearSelection() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoSelectionState enterSelectionMode(@org.jetbrains.annotations.Nullable()
    java.lang.String photoId) {
        return null;
    }
    
    public PhotoSelectionState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Set<java.lang.String> component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoSelectionState copy(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> selectedPhotos, boolean isSelectionMode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}