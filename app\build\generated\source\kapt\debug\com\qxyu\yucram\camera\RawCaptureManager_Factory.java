// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.camera;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class RawCaptureManager_Factory implements Factory<RawCaptureManager> {
  private final Provider<Context> contextProvider;

  public RawCaptureManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public RawCaptureManager get() {
    return newInstance(contextProvider.get());
  }

  public static RawCaptureManager_Factory create(Provider<Context> contextProvider) {
    return new RawCaptureManager_Factory(contextProvider);
  }

  public static RawCaptureManager newInstance(Context context) {
    return new RawCaptureManager(context);
  }
}
