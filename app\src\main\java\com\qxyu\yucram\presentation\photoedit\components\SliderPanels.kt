package com.qxyu.yucram.presentation.photoedit.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * 单滑块面板
 */
@Composable
fun SingleSliderPanel(
    title: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChanged: (Float) -> Unit,
    modifier: Modifier = Modifier,
    formatValue: (Float) -> String = { value ->
        when {
            value > 0 -> "+${value.toInt()}"
            value < 0 -> value.toInt().toString()
            else -> "0"
        }
    }
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // 滑块
        AdjustmentSlider(
            label = title,
            value = value,
            valueRange = valueRange,
            onValueChanged = onValueChanged,
            formatValue = formatValue
        )
        
        // 重置按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(
                onClick = {
                    onValueChanged(0f)
                }
            ) {
                Text(
                    text = "重置",
                    color = Color.White
                )
            }
        }
    }
}

/**
 * 双滑块面板
 */
@Composable
fun DualSliderPanel(
    title: String,
    value1: Float,
    label1: String,
    onValue1Changed: (Float) -> Unit,
    value2: Float,
    label2: String,
    onValue2Changed: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float>,
    modifier: Modifier = Modifier,
    formatValue1: (Float) -> String = { value ->
        when {
            value > 0 -> "+${value.toInt()}"
            value < 0 -> value.toInt().toString()
            else -> "0"
        }
    },
    formatValue2: (Float) -> String = { value ->
        when {
            value > 0 -> "+${value.toInt()}"
            value < 0 -> value.toInt().toString()
            else -> "0"
        }
    }
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // 第一个滑块
        AdjustmentSlider(
            label = label1,
            value = value1,
            valueRange = valueRange,
            onValueChanged = onValue1Changed,
            formatValue = formatValue1,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 第二个滑块
        AdjustmentSlider(
            label = label2,
            value = value2,
            valueRange = valueRange,
            onValueChanged = onValue2Changed,
            formatValue = formatValue2
        )
        
        // 重置按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            TextButton(
                onClick = {
                    onValue1Changed(0f)
                }
            ) {
                Text(
                    text = "重置$label1",
                    color = Color.White
                )
            }
            
            TextButton(
                onClick = {
                    onValue2Changed(0f)
                }
            ) {
                Text(
                    text = "重置$label2",
                    color = Color.White
                )
            }
        }
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(
                onClick = {
                    onValue1Changed(0f)
                    onValue2Changed(0f)
                }
            ) {
                Text(
                    text = "重置全部",
                    color = Color.White
                )
            }
        }
    }
}

/**
 * 三滑块面板
 */
@Composable
fun TripleSliderPanel(
    title: String,
    value1: Float,
    label1: String,
    onValue1Changed: (Float) -> Unit,
    value2: Float,
    label2: String,
    onValue2Changed: (Float) -> Unit,
    value3: Float,
    label3: String,
    onValue3Changed: (Float) -> Unit,
    valueRange: ClosedFloatingPointRange<Float>,
    modifier: Modifier = Modifier,
    formatValue: (Float) -> String = { value ->
        when {
            value > 0 -> "+${value.toInt()}"
            value < 0 -> value.toInt().toString()
            else -> "0"
        }
    }
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // 第一个滑块
        AdjustmentSlider(
            label = label1,
            value = value1,
            valueRange = valueRange,
            onValueChanged = onValue1Changed,
            formatValue = formatValue,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 第二个滑块
        AdjustmentSlider(
            label = label2,
            value = value2,
            valueRange = valueRange,
            onValueChanged = onValue2Changed,
            formatValue = formatValue,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 第三个滑块
        AdjustmentSlider(
            label = label3,
            value = value3,
            valueRange = valueRange,
            onValueChanged = onValue3Changed,
            formatValue = formatValue
        )
        
        // 重置按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(
                onClick = {
                    onValue1Changed(0f)
                    onValue2Changed(0f)
                    onValue3Changed(0f)
                }
            ) {
                Text(
                    text = "重置全部",
                    color = Color.White
                )
            }
        }
    }
}

/**
 * 带预设的滑块面板
 */
@Composable
fun SliderPanelWithPresets(
    title: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChanged: (Float) -> Unit,
    presets: List<Pair<String, Float>>,
    modifier: Modifier = Modifier,
    formatValue: (Float) -> String = { value ->
        when {
            value > 0 -> "+${value.toInt()}"
            value < 0 -> value.toInt().toString()
            else -> "0"
        }
    }
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 预设按钮
        if (presets.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                presets.forEach { (name, presetValue) ->
                    OutlinedButton(
                        onClick = { onValueChanged(presetValue) },
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.White
                        ),
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = name,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }
            }
        }
        
        // 滑块
        AdjustmentSlider(
            label = title,
            value = value,
            valueRange = valueRange,
            onValueChanged = onValueChanged,
            formatValue = formatValue
        )
        
        // 重置按钮
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextButton(
                onClick = {
                    onValueChanged(0f)
                }
            ) {
                Text(
                    text = "重置",
                    color = Color.White
                )
            }
        }
    }
}

/**
 * 精确数值输入滑块
 */
@Composable
fun PrecisionSlider(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChanged: (Float) -> Unit,
    modifier: Modifier = Modifier,
    step: Float = 1f,
    formatValue: (Float) -> String = { value ->
        when {
            value > 0 -> "+${String.format("%.1f", value)}"
            value < 0 -> String.format("%.1f", value)
            else -> "0"
        }
    }
) {
    var showTextField by remember { mutableStateOf(false) }
    var textFieldValue by remember(value) { mutableStateOf(value.toString()) }
    
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        // 标签和数值
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            if (showTextField) {
                OutlinedTextField(
                    value = textFieldValue,
                    onValueChange = { textFieldValue = it },
                    modifier = Modifier.width(80.dp),
                    textStyle = MaterialTheme.typography.bodySmall,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color.White,
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = Color.White.copy(alpha = 0.5f)
                    ),
                    singleLine = true
                )
            } else {
                TextButton(
                    onClick = { showTextField = true }
                ) {
                    Text(
                        text = formatValue(value),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        // 滑块
        Slider(
            value = value,
            onValueChange = onValueChanged,
            valueRange = valueRange,
            steps = if (step > 0) ((valueRange.endInclusive - valueRange.start) / step).toInt() - 1 else 0,
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary,
                activeTrackColor = MaterialTheme.colorScheme.primary,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            )
        )
    }
    
    // 处理文本输入
    LaunchedEffect(showTextField) {
        if (!showTextField && textFieldValue.isNotEmpty()) {
            try {
                val newValue = textFieldValue.toFloat().coerceIn(valueRange)
                onValueChanged(newValue)
            } catch (e: NumberFormatException) {
                // 忽略无效输入
            }
        }
    }
}
