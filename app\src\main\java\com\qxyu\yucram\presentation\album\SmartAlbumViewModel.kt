package com.qxyu.yucram.presentation.album

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.AlbumRepository
import com.qxyu.yucram.domain.repository.PhotoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import javax.inject.Inject

/**
 * 智能相册ViewModel
 */
@HiltViewModel
class SmartAlbumViewModel @Inject constructor(
    private val albumRepository: AlbumRepository,
    private val photoRepository: PhotoRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(SmartAlbumUiState())
    val uiState: StateFlow<SmartAlbumUiState> = _uiState.asStateFlow()
    
    private val _rules = MutableStateFlow<List<SmartAlbumRule>>(emptyList())
    val rules: StateFlow<List<SmartAlbumRule>> = _rules.asStateFlow()
    
    private val _previewPhotos = MutableStateFlow<List<Photo>>(emptyList())
    val previewPhotos: StateFlow<List<Photo>> = _previewPhotos.asStateFlow()
    
    // ========== 规则管理 ==========
    
    fun addRule() {
        val newRule = SmartAlbumRule(
            id = generateRuleId(),
            albumId = "", // 临时为空，创建相册时会设置
            ruleType = SmartAlbumRuleType.HAS_TAG,
            value = "",
            operator = SmartAlbumOperator.EQUALS
        )
        
        _rules.update { currentRules ->
            currentRules + newRule
        }
        
        refreshPreview()
    }
    
    fun updateRule(index: Int, rule: SmartAlbumRule) {
        _rules.update { currentRules ->
            currentRules.toMutableList().apply {
                if (index in indices) {
                    this[index] = rule
                }
            }
        }
        
        refreshPreview()
    }
    
    fun removeRule(index: Int) {
        _rules.update { currentRules ->
            currentRules.toMutableList().apply {
                if (index in indices) {
                    removeAt(index)
                }
            }
        }
        
        refreshPreview()
    }
    
    // ========== 预览功能 ==========
    
    fun refreshPreview() {
        val currentRules = _rules.value
        if (currentRules.isEmpty()) {
            _previewPhotos.value = emptyList()
            return
        }
        
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoadingPreview = true) }
                
                // 应用规则筛选照片
                val filteredPhotos = applySmartAlbumRules(currentRules)
                _previewPhotos.value = filteredPhotos
                
                _uiState.update { it.copy(isLoadingPreview = false) }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoadingPreview = false,
                        error = "预览失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    private suspend fun applySmartAlbumRules(rules: List<SmartAlbumRule>): List<Photo> {
        // 获取所有照片
        val allPhotos = photoRepository.getAllPhotos().first()
        
        // 应用每个规则
        return allPhotos.filter { photo ->
            rules.all { rule -> matchesRule(photo, rule) }
        }
    }
    
    private fun matchesRule(photo: Photo, rule: SmartAlbumRule): Boolean {
        return when (rule.ruleType) {
            SmartAlbumRuleType.HAS_TAG -> {
                if (rule.value.isBlank()) return false
                photo.tags.any { tag ->
                    when (rule.operator) {
                        SmartAlbumOperator.EQUALS -> tag.equals(rule.value, ignoreCase = true)
                        SmartAlbumOperator.CONTAINS -> tag.contains(rule.value, ignoreCase = true)
                        else -> false
                    }
                }
            }

            SmartAlbumRuleType.DATE_RANGE -> {
                // TODO: 实现日期范围匹配
                true
            }

            SmartAlbumRuleType.LOCATION -> {
                if (rule.value.isBlank()) return false
                photo.location?.let { location ->
                    location.address?.contains(rule.value, ignoreCase = true) == true ||
                    location.city?.contains(rule.value, ignoreCase = true) == true ||
                    location.country?.contains(rule.value, ignoreCase = true) == true
                } ?: false
            }

            SmartAlbumRuleType.CAMERA_MODEL -> {
                if (rule.value.isBlank()) return false
                photo.exifData?.cameraModel?.contains(rule.value, ignoreCase = true) == true
            }

            SmartAlbumRuleType.IS_FAVORITE -> {
                photo.isFavorite
            }

            SmartAlbumRuleType.IS_YUCRAM_PHOTO -> {
                photo.isYucramPhoto
            }

            SmartAlbumRuleType.HAS_FILTER -> {
                if (rule.value.isBlank()) {
                    // 任意滤镜
                    photo.filterUsed != null
                } else {
                    // 特定滤镜
                    photo.filterUsed?.contains(rule.value, ignoreCase = true) == true
                }
            }

            SmartAlbumRuleType.FILE_SIZE -> {
                if (rule.value.isBlank()) return false
                matchesFileSizeRule(photo.fileSize, rule.value)
            }

            // 其他规则类型的默认处理
            else -> false
        }
    }
    
    private fun matchesFileSizeRule(fileSize: Long, ruleValue: String): Boolean {
        try {
            val trimmedValue = ruleValue.trim()
            
            when {
                trimmedValue.startsWith(">") -> {
                    val sizeStr = trimmedValue.substring(1).trim()
                    val targetSize = parseSizeString(sizeStr)
                    return fileSize > targetSize
                }
                
                trimmedValue.startsWith("<") -> {
                    val sizeStr = trimmedValue.substring(1).trim()
                    val targetSize = parseSizeString(sizeStr)
                    return fileSize < targetSize
                }
                
                trimmedValue.startsWith("=") -> {
                    val sizeStr = trimmedValue.substring(1).trim()
                    val targetSize = parseSizeString(sizeStr)
                    return fileSize == targetSize
                }
                
                else -> {
                    val targetSize = parseSizeString(trimmedValue)
                    return fileSize == targetSize
                }
            }
        } catch (e: Exception) {
            return false
        }
    }
    
    private fun parseSizeString(sizeStr: String): Long {
        val upperCase = sizeStr.uppercase()
        
        return when {
            upperCase.endsWith("GB") -> {
                val number = upperCase.removeSuffix("GB").toFloat()
                (number * 1024 * 1024 * 1024).toLong()
            }
            
            upperCase.endsWith("MB") -> {
                val number = upperCase.removeSuffix("MB").toFloat()
                (number * 1024 * 1024).toLong()
            }
            
            upperCase.endsWith("KB") -> {
                val number = upperCase.removeSuffix("KB").toFloat()
                (number * 1024).toLong()
            }
            
            upperCase.endsWith("B") -> {
                upperCase.removeSuffix("B").toLong()
            }
            
            else -> {
                // 默认为字节
                sizeStr.toLong()
            }
        }
    }
    
    // ========== 相册创建 ==========
    
    fun createSmartAlbum(name: String, description: String, rules: List<SmartAlbumRule>) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isCreating = true) }
                
                val album = Album(
                    id = generateAlbumId(),
                    name = name,
                    description = description.takeIf { it.isNotBlank() },
                    dateCreated = LocalDateTime.now(),
                    dateModified = LocalDateTime.now(),
                    albumType = AlbumType.SMART_ALBUM,
                    photoCount = _previewPhotos.value.size
                )
                
                val result = albumRepository.createSmartAlbum(album, rules)
                
                result.fold(
                    onSuccess = { createdAlbum ->
                        _uiState.update { 
                            it.copy(
                                isCreating = false,
                                isCreated = true,
                                createdAlbumId = createdAlbum.id,
                                message = "智能相册创建成功"
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(
                                isCreating = false,
                                error = "创建智能相册失败: ${error.message}"
                            )
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isCreating = false,
                        error = "创建智能相册失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 辅助方法 ==========
    
    private fun generateRuleId(): String {
        return "rule_${System.currentTimeMillis()}_${(0..999).random()}"
    }
    
    private fun generateAlbumId(): String {
        return "smart_album_${System.currentTimeMillis()}_${(0..999).random()}"
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(message = null) }
    }
}

/**
 * 智能相册UI状态
 */
data class SmartAlbumUiState(
    val isLoadingPreview: Boolean = false,
    val isCreating: Boolean = false,
    val isCreated: Boolean = false,
    val createdAlbumId: String? = null,
    val error: String? = null,
    val message: String? = null
)
