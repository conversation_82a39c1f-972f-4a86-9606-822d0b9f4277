1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.qxyu.yucram"
4    android:versionCode="1"
5    android:versionName="2.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="34"
9        android:targetSdkVersion="34" />
10
11    <!-- Camera permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:6:5-65
12-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:7:5-71
13-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:7:22-68
14
15    <!-- Storage permissions for Android 14+ -->
16    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
16-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:10:5-76
16-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:10:22-73
17    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
17-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:11:5-75
17-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:11:22-72
18    <uses-permission
18-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:12:22-77
20        android:maxSdkVersion="32" />
20-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission
21-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:14:5-15:38
22        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
22-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:14:22-78
23        android:maxSdkVersion="32" />
23-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:15:9-35
24
25    <!-- Location permissions (optional for GPS watermark) -->
26    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
26-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:18:5-79
26-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:18:22-76
27    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
27-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:19:5-81
27-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:19:22-78
28
29    <!-- Vibration permission -->
30    <uses-permission android:name="android.permission.VIBRATE" />
30-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:22:5-66
30-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:22:22-63
31
32    <!-- Camera features -->
33    <uses-feature
33-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:25:5-27:35
34        android:name="android.hardware.camera"
34-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:26:9-47
35        android:required="true" />
35-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:27:9-32
36    <uses-feature
36-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:28:5-30:36
37        android:name="android.hardware.camera2.full"
37-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:29:9-53
38        android:required="false" />
38-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:30:9-33
39    <uses-feature
39-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:31:5-33:36
40        android:name="android.hardware.camera.autofocus"
40-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:32:9-57
41        android:required="false" />
41-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:33:9-33
42    <uses-feature
42-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:34:5-36:36
43        android:name="android.hardware.camera.flash"
43-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:35:9-53
44        android:required="false" />
44-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:36:9-33
45
46    <queries>
46-->[androidx.camera:camera-extensions:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\58db09b213b713378023972af49ed2b5\transformed\jetified-camera-extensions-1.3.4\AndroidManifest.xml:22:5-26:15
47        <intent>
47-->[androidx.camera:camera-extensions:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\58db09b213b713378023972af49ed2b5\transformed\jetified-camera-extensions-1.3.4\AndroidManifest.xml:23:9-25:18
48            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
48-->[androidx.camera:camera-extensions:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\58db09b213b713378023972af49ed2b5\transformed\jetified-camera-extensions-1.3.4\AndroidManifest.xml:24:13-86
48-->[androidx.camera:camera-extensions:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\58db09b213b713378023972af49ed2b5\transformed\jetified-camera-extensions-1.3.4\AndroidManifest.xml:24:21-83
49        </intent>
50    </queries>
51
52    <permission
52-->[androidx.core:core:1.12.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\90847ba8583f288576ac60503f85644e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
53        android:name="com.qxyu.yucram.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.12.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\90847ba8583f288576ac60503f85644e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.12.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\90847ba8583f288576ac60503f85644e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.qxyu.yucram.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.12.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\90847ba8583f288576ac60503f85644e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.12.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\90847ba8583f288576ac60503f85644e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
57
58    <application
58-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:38:5-73:19
59        android:name="com.qxyu.yucram.YucramApplication"
59-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:39:9-42
60        android:allowBackup="true"
60-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:40:9-35
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.12.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\90847ba8583f288576ac60503f85644e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
62        android:dataExtractionRules="@xml/data_extraction_rules"
62-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:41:9-65
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:fullBackupContent="@xml/backup_rules"
65-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:42:9-54
66        android:hardwareAccelerated="true"
66-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:48:9-43
67        android:icon="@drawable/ic_launcher"
67-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:43:9-45
68        android:label="@string/app_name"
68-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:44:9-41
69        android:roundIcon="@drawable/ic_launcher"
69-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:45:9-50
70        android:supportsRtl="true"
70-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:46:9-35
71        android:theme="@style/Theme.Yucram" >
71-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:47:9-44
72        <activity
72-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:51:9-61:20
73            android:name="com.qxyu.yucram.MainActivity"
73-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:52:13-41
74            android:configChanges="orientation|screenSize|keyboardHidden"
74-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:56:13-74
75            android:exported="true"
75-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:53:13-36
76            android:screenOrientation="portrait"
76-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:55:13-49
77            android:theme="@style/Theme.Yucram" >
77-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:54:13-48
78            <intent-filter>
78-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:57:13-60:29
79                <action android:name="android.intent.action.MAIN" />
79-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:58:17-69
79-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:58:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:59:17-77
81-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:59:27-74
82            </intent-filter>
83        </activity>
84
85        <!-- File provider for sharing images -->
86        <provider
87            android:name="androidx.core.content.FileProvider"
87-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:65:13-62
88            android:authorities="com.qxyu.yucram.fileprovider"
88-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:66:13-64
89            android:exported="false"
89-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:67:13-37
90            android:grantUriPermissions="true" >
90-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:68:13-47
91            <meta-data
91-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:69:13-71:54
92                android:name="android.support.FILE_PROVIDER_PATHS"
92-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:70:17-67
93                android:resource="@xml/file_paths" />
93-->D:\project\Androidstd\y3\yu augment1.0.0\app\src\main\AndroidManifest.xml:71:17-51
94        </provider>
95        <!--
96        Service for holding metadata. Cannot be instantiated.
97        Metadata will be merged from other manifests.
98        -->
99        <service
99-->[androidx.camera:camera-core:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\3a64348690f8c0f13ef58d47726b8512\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:29:9-33:78
100            android:name="androidx.camera.core.impl.MetadataHolderService"
100-->[androidx.camera:camera-core:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\3a64348690f8c0f13ef58d47726b8512\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:30:13-75
101            android:enabled="false"
101-->[androidx.camera:camera-core:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\3a64348690f8c0f13ef58d47726b8512\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:31:13-36
102            android:exported="false" >
102-->[androidx.camera:camera-core:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\3a64348690f8c0f13ef58d47726b8512\transformed\jetified-camera-core-1.3.4\AndroidManifest.xml:32:13-37
103            <meta-data
103-->[androidx.camera:camera-camera2:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\d5d5c241ee0ba03942cf311b9738aa3a\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
104                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
104-->[androidx.camera:camera-camera2:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\d5d5c241ee0ba03942cf311b9738aa3a\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
105                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
105-->[androidx.camera:camera-camera2:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\d5d5c241ee0ba03942cf311b9738aa3a\transformed\jetified-camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
106        </service>
107
108        <uses-library
108-->[androidx.camera:camera-extensions:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\58db09b213b713378023972af49ed2b5\transformed\jetified-camera-extensions-1.3.4\AndroidManifest.xml:29:9-31:40
109            android:name="androidx.camera.extensions.impl"
109-->[androidx.camera:camera-extensions:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\58db09b213b713378023972af49ed2b5\transformed\jetified-camera-extensions-1.3.4\AndroidManifest.xml:30:13-59
110            android:required="false" />
110-->[androidx.camera:camera-extensions:1.3.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\58db09b213b713378023972af49ed2b5\transformed\jetified-camera-extensions-1.3.4\AndroidManifest.xml:31:13-37
111
112        <activity
112-->[androidx.compose.ui:ui-test-manifest:1.5.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\0bf7732dd39c760b7c951efe6e712036\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
113            android:name="androidx.activity.ComponentActivity"
113-->[androidx.compose.ui:ui-test-manifest:1.5.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\0bf7732dd39c760b7c951efe6e712036\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
114            android:exported="true" />
114-->[androidx.compose.ui:ui-test-manifest:1.5.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\0bf7732dd39c760b7c951efe6e712036\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
115        <activity
115-->[androidx.compose.ui:ui-tooling-android:1.5.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\335d57b4e986264671c3493c8cc240d5\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
116            android:name="androidx.compose.ui.tooling.PreviewActivity"
116-->[androidx.compose.ui:ui-tooling-android:1.5.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\335d57b4e986264671c3493c8cc240d5\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
117            android:exported="true" />
117-->[androidx.compose.ui:ui-tooling-android:1.5.4] D:\XSBDownload\SDK\.gradle\caches\transforms-3\335d57b4e986264671c3493c8cc240d5\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
118
119        <provider
119-->[androidx.emoji2:emoji2:1.4.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\32711878ab6b6d5a565018326ccdde4f\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
120            android:name="androidx.startup.InitializationProvider"
120-->[androidx.emoji2:emoji2:1.4.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\32711878ab6b6d5a565018326ccdde4f\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:25:13-67
121            android:authorities="com.qxyu.yucram.androidx-startup"
121-->[androidx.emoji2:emoji2:1.4.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\32711878ab6b6d5a565018326ccdde4f\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:26:13-68
122            android:exported="false" >
122-->[androidx.emoji2:emoji2:1.4.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\32711878ab6b6d5a565018326ccdde4f\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:27:13-37
123            <meta-data
123-->[androidx.emoji2:emoji2:1.4.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\32711878ab6b6d5a565018326ccdde4f\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
124                android:name="androidx.emoji2.text.EmojiCompatInitializer"
124-->[androidx.emoji2:emoji2:1.4.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\32711878ab6b6d5a565018326ccdde4f\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
125                android:value="androidx.startup" />
125-->[androidx.emoji2:emoji2:1.4.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\32711878ab6b6d5a565018326ccdde4f\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
126            <meta-data
126-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\37741103786f5ffacf84e2ac82f55e1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\37741103786f5ffacf84e2ac82f55e1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
128                android:value="androidx.startup" />
128-->[androidx.lifecycle:lifecycle-process:2.7.0] D:\XSBDownload\SDK\.gradle\caches\transforms-3\37741103786f5ffacf84e2ac82f55e1a\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
131                android:value="androidx.startup" />
131-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
132        </provider>
133
134        <service
134-->[androidx.room:room-runtime:2.6.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\27a8bd4e9d1b190c801655633ebdda84\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
135            android:name="androidx.room.MultiInstanceInvalidationService"
135-->[androidx.room:room-runtime:2.6.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\27a8bd4e9d1b190c801655633ebdda84\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
136            android:directBootAware="true"
136-->[androidx.room:room-runtime:2.6.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\27a8bd4e9d1b190c801655633ebdda84\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
137            android:exported="false" />
137-->[androidx.room:room-runtime:2.6.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\27a8bd4e9d1b190c801655633ebdda84\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
138
139        <receiver
139-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
140            android:name="androidx.profileinstaller.ProfileInstallReceiver"
140-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
141            android:directBootAware="false"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
142            android:enabled="true"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
143            android:exported="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
144            android:permission="android.permission.DUMP" >
144-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
145            <intent-filter>
145-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
146                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
146-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
146-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
147            </intent-filter>
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
149                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
149-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
152                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
152-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
155                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
155-->[androidx.profileinstaller:profileinstaller:1.3.1] D:\XSBDownload\SDK\.gradle\caches\transforms-3\9901e2ac5c5ff6a283a5dc16fff6abbf\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
156            </intent-filter>
157        </receiver>
158    </application>
159
160</manifest>
