package com.qxyu.yucram.domain.model;

/**
 * 图像格式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/qxyu/yucram/domain/model/ProcessedImageFormat;", "", "mimeType", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getMimeType", "()Ljava/lang/String;", "JPEG", "PNG", "WEBP", "RAW_DNG", "TIFF", "app_debug"})
public enum ProcessedImageFormat {
    /*public static final*/ JPEG /* = new JPEG(null) */,
    /*public static final*/ PNG /* = new PNG(null) */,
    /*public static final*/ WEBP /* = new WEBP(null) */,
    /*public static final*/ RAW_DNG /* = new RAW_DNG(null) */,
    /*public static final*/ TIFF /* = new TIFF(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String mimeType = null;
    
    ProcessedImageFormat(java.lang.String mimeType) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMimeType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.ProcessedImageFormat> getEntries() {
        return null;
    }
}