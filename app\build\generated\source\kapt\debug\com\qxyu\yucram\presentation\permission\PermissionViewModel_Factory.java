// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.permission;

import android.app.Application;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PermissionViewModel_Factory implements Factory<PermissionViewModel> {
  private final Provider<Application> applicationProvider;

  public PermissionViewModel_Factory(Provider<Application> applicationProvider) {
    this.applicationProvider = applicationProvider;
  }

  @Override
  public PermissionViewModel get() {
    return newInstance(applicationProvider.get());
  }

  public static PermissionViewModel_Factory create(Provider<Application> applicationProvider) {
    return new PermissionViewModel_Factory(applicationProvider);
  }

  public static PermissionViewModel newInstance(Application application) {
    return new PermissionViewModel(application);
  }
}
