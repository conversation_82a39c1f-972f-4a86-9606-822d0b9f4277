package com.qxyu.yucram

import android.app.Application
import dagger.hilt.android.HiltAndroidApp

/**
 * Yucram应用程序主类
 * 使用Hilt进行依赖注入
 */
@HiltAndroidApp
class YucramApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化应用程序级别的组件
        initializeApp()
    }
    
    private fun initializeApp() {
        // 这里可以添加应用程序初始化逻辑
        // 例如：崩溃报告、分析工具等
    }
}
