package com.qxyu.yucram.presentation.borderwater;

/**
 * 边框和水印ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u0019\u001a\u00020\t2\u0006\u0010\u0013\u001a\u00020\t2\u0006\u0010\u0015\u001a\u00020\u000bH\u0082@\u00a2\u0006\u0002\u0010\u001aJ\u000e\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eJ\u0006\u0010\u001f\u001a\u00020\u001cJ\u0006\u0010 \u001a\u00020\u001cJ\u0016\u0010!\u001a\u00020\u001c2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020%J.\u0010&\u001a\u00020\'2\u0006\u0010\u0013\u001a\u00020\t2\u0006\u0010\u0015\u001a\u00020\u000b2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020%H\u0082@\u00a2\u0006\u0002\u0010(J\b\u0010)\u001a\u00020\'H\u0002J\f\u0010*\u001a\b\u0012\u0004\u0012\u00020,0+J\f\u0010-\u001a\b\u0012\u0004\u0012\u00020.0+J\u0006\u0010/\u001a\u000200J\u0010\u00101\u001a\u00020\u000b2\u0006\u0010\u0013\u001a\u00020\tH\u0002J\u000e\u00102\u001a\u00020\u001c2\u0006\u00103\u001a\u00020\'J\u0006\u00104\u001a\u00020\u001cJ\u0016\u00105\u001a\u0002062\u0006\u00107\u001a\u00020\'2\u0006\u00108\u001a\u00020\'J\u0006\u00109\u001a\u00020\u001cJ\u000e\u0010:\u001a\u00020\u001c2\u0006\u0010;\u001a\u00020\u0007J\u0006\u0010<\u001a\u00020\u001cJ\u000e\u0010=\u001a\u00020\u001c2\u0006\u0010>\u001a\u00020\u000bR\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00070\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u000e\u0010\u0012\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\r0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011\u00a8\u0006?"}, d2 = {"Lcom/qxyu/yucram/presentation/borderwater/BorderWatermarkViewModel;", "Landroidx/lifecycle/ViewModel;", "photoRepository", "Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "(Lcom/qxyu/yucram/domain/repository/PhotoRepository;)V", "_currentTab", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/presentation/borderwater/BorderWatermarkTab;", "_photo", "Lcom/qxyu/yucram/domain/model/Photo;", "_settings", "Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;", "_uiState", "Lcom/qxyu/yucram/presentation/borderwater/BorderWatermarkUiState;", "currentTab", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentTab", "()Lkotlinx/coroutines/flow/StateFlow;", "originalSettings", "photo", "getPhoto", "settings", "getSettings", "uiState", "getUiState", "applyBorderAndWatermark", "(Lcom/qxyu/yucram/domain/model/Photo;Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyPreset", "", "preset", "", "clearError", "clearMessage", "exportPhoto", "format", "Lcom/qxyu/yucram/domain/model/ExportFormat;", "quality", "", "exportPhotoWithBorderWatermark", "", "(Lcom/qxyu/yucram/domain/model/Photo;Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;Lcom/qxyu/yucram/domain/model/ExportFormat;FLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generatePresetId", "getBuiltInBorderPresets", "", "Lcom/qxyu/yucram/domain/model/BorderPreset;", "getBuiltInWatermarkPresets", "Lcom/qxyu/yucram/domain/model/WatermarkPreset;", "hasChanges", "", "loadExistingSettings", "loadPhoto", "photoId", "resetSettings", "saveAsPreset", "Lcom/qxyu/yucram/presentation/borderwater/BorderWatermarkPreset;", "name", "description", "saveSettings", "selectTab", "tab", "togglePreview", "updateSettings", "newSettings", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class BorderWatermarkViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.PhotoRepository photoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.borderwater.BorderWatermarkUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.borderwater.BorderWatermarkUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.Photo> _photo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> photo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.BorderWatermarkSettings> _settings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.BorderWatermarkSettings> settings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab> _currentTab = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab> currentTab = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.BorderWatermarkSettings originalSettings = null;
    
    @javax.inject.Inject()
    public BorderWatermarkViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.PhotoRepository photoRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.borderwater.BorderWatermarkUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> getPhoto() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.BorderWatermarkSettings> getSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab> getCurrentTab() {
        return null;
    }
    
    public final void loadPhoto(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void selectTab(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab tab) {
    }
    
    public final void updateSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings newSettings) {
    }
    
    public final void resetSettings() {
    }
    
    public final boolean hasChanges() {
        return false;
    }
    
    public final void applyPreset(@org.jetbrains.annotations.NotNull()
    java.lang.Object preset) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.borderwater.BorderWatermarkPreset saveAsPreset(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description) {
        return null;
    }
    
    public final void saveSettings() {
    }
    
    public final void exportPhoto(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ExportFormat format, float quality) {
    }
    
    public final void togglePreview() {
    }
    
    private final com.qxyu.yucram.domain.model.BorderWatermarkSettings loadExistingSettings(com.qxyu.yucram.domain.model.Photo photo) {
        return null;
    }
    
    private final java.lang.Object applyBorderAndWatermark(com.qxyu.yucram.domain.model.Photo photo, com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.Photo> $completion) {
        return null;
    }
    
    private final java.lang.Object exportPhotoWithBorderWatermark(com.qxyu.yucram.domain.model.Photo photo, com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, com.qxyu.yucram.domain.model.ExportFormat format, float quality, kotlin.coroutines.Continuation<? super java.lang.String> $completion) {
        return null;
    }
    
    private final java.lang.String generatePresetId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.BorderPreset> getBuiltInBorderPresets() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.WatermarkPreset> getBuiltInWatermarkPresets() {
        return null;
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}