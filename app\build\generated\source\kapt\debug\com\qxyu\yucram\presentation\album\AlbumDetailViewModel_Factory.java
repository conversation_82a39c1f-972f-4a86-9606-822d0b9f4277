// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.album;

import com.qxyu.yucram.domain.repository.AlbumRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AlbumDetailViewModel_Factory implements Factory<AlbumDetailViewModel> {
  private final Provider<AlbumRepository> albumRepositoryProvider;

  public AlbumDetailViewModel_Factory(Provider<AlbumRepository> albumRepositoryProvider) {
    this.albumRepositoryProvider = albumRepositoryProvider;
  }

  @Override
  public AlbumDetailViewModel get() {
    return newInstance(albumRepositoryProvider.get());
  }

  public static AlbumDetailViewModel_Factory create(
      Provider<AlbumRepository> albumRepositoryProvider) {
    return new AlbumDetailViewModel_Factory(albumRepositoryProvider);
  }

  public static AlbumDetailViewModel newInstance(AlbumRepository albumRepository) {
    return new AlbumDetailViewModel(albumRepository);
  }
}
