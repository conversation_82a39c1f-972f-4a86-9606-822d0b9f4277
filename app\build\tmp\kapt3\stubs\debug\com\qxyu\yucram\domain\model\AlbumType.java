package com.qxyu.yucram.domain.model;

/**
 * 相册类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000e\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumType;", "", "(Ljava/lang/String;I)V", "USER_CREATED", "RECENT", "FAVORITES", "YUCRAM_PHOTOS", "CAMERA_ROLL", "SCREENSHOTS", "VIDEOS", "RAW_PHOTOS", "FILTERED_PHOTOS", "LOCATION_BASED", "DATE_BASED", "SMART_ALBUM", "app_debug"})
public enum AlbumType {
    /*public static final*/ USER_CREATED /* = new USER_CREATED() */,
    /*public static final*/ RECENT /* = new RECENT() */,
    /*public static final*/ FAVORITES /* = new FAVORITES() */,
    /*public static final*/ YUCRAM_PHOTOS /* = new YUCRAM_PHOTOS() */,
    /*public static final*/ CAMERA_ROLL /* = new CAMERA_ROLL() */,
    /*public static final*/ SCREENSHOTS /* = new SCREENSHOTS() */,
    /*public static final*/ VIDEOS /* = new VIDEOS() */,
    /*public static final*/ RAW_PHOTOS /* = new RAW_PHOTOS() */,
    /*public static final*/ FILTERED_PHOTOS /* = new FILTERED_PHOTOS() */,
    /*public static final*/ LOCATION_BASED /* = new LOCATION_BASED() */,
    /*public static final*/ DATE_BASED /* = new DATE_BASED() */,
    /*public static final*/ SMART_ALBUM /* = new SMART_ALBUM() */;
    
    AlbumType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.AlbumType> getEntries() {
        return null;
    }
}