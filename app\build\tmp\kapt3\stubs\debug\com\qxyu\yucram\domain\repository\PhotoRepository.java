package com.qxyu.yucram.domain.repository;

/**
 * 照片仓库接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000b\n\u0002\b\u0012\bf\u0018\u00002\u00020\u0001J\u001c\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0005\u0010\u0006J$\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u000b\u0010\fJ*\u0010\r\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\n0\u000fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0010\u0010\u0011J,\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0015J8\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\u000f0\u00032\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\n0\u000f2\u0006\u0010\u0013\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0017\u0010\u0018J$\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\fJ\u0014\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001cH&J\u0014\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u001f0\u001cH&J\u0014\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001cH&J\u0018\u0010!\u001a\u0004\u0018\u00010\u001d2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\"\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010#\u001a\u00020$H\u00a6@\u00a2\u0006\u0002\u0010\u0006J$\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001c2\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\'H&J\u001c\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001c2\u0006\u0010*\u001a\u00020\nH&J\"\u0010+\u001a\b\u0012\u0004\u0012\u00020\u001d0\u000f2\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\n0\u000fH\u00a6@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010,\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001c2\u0006\u0010-\u001a\u00020\nH&J\u001c\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001c2\u0006\u0010/\u001a\u000200H&J\u001e\u00101\u001a\b\u0012\u0004\u0012\u00020\u001d0\u000f2\b\b\u0002\u00102\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u00103J\u0014\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001cH&J$\u00105\u001a\b\u0012\u0004\u0012\u00020\u001d0\u00032\u0006\u00106\u001a\u00020\nH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b7\u0010\fJ0\u00108\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u00032\f\u00109\u001a\b\u0012\u0004\u0012\u00020\n0\u000fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b:\u0010\u0011J\u0016\u0010;\u001a\u00020<2\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0002\u0010\fJ$\u0010=\u001a\b\u0012\u0004\u0012\u00020\u001d0\u00032\u0006\u0010>\u001a\u00020\u001dH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u0010@J0\u0010A\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u00032\f\u0010B\u001a\b\u0012\u0004\u0012\u00020\u001d0\u000fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bC\u0010\u0011J\"\u0010D\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bE\u0010\u0006J\u001c\u0010F\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001d0\u000f0\u001c2\u0006\u0010G\u001a\u00020\nH&J,\u0010H\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\u0006\u0010\t\u001a\u00020\n2\u0006\u0010I\u001a\u00020<H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bJ\u0010KJ$\u0010L\u001a\b\u0012\u0004\u0012\u00020\u001d0\u00032\u0006\u0010>\u001a\u00020\u001dH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bM\u0010@\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006N"}, d2 = {"Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "", "cleanupDeletedPhotos", "Lkotlin/Result;", "", "cleanupDeletedPhotos-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePhoto", "", "photoId", "", "deletePhoto-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePhotos", "photoIds", "", "deletePhotos-gIAlu-s", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "exportPhoto", "destinationPath", "exportPhoto-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "exportPhotos", "exportPhotos-0E7RQCE", "(Ljava/util/List;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateThumbnail", "generateThumbnail-gIAlu-s", "getAllPhotos", "Lkotlinx/coroutines/flow/Flow;", "Lcom/qxyu/yucram/domain/model/Photo;", "getAllPhotosPaged", "Landroidx/paging/PagingData;", "getFavoritePhotos", "getPhotoById", "getPhotoCount", "getPhotoStats", "Lcom/qxyu/yucram/domain/model/PhotoStats;", "getPhotosByDateRange", "startDate", "Ljava/time/LocalDateTime;", "endDate", "getPhotosByFilter", "filterName", "getPhotosByIds", "getPhotosByTag", "tag", "getPhotosWithFilter", "filter", "Lcom/qxyu/yucram/domain/model/PhotoFilter;", "getRecentPhotos", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getYucramPhotos", "importPhoto", "filePath", "importPhoto-gIAlu-s", "importPhotos", "filePaths", "importPhotos-gIAlu-s", "isPhotoFileExists", "", "savePhoto", "photo", "savePhoto-gIAlu-s", "(Lcom/qxyu/yucram/domain/model/Photo;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "savePhotos", "photos", "savePhotos-gIAlu-s", "scanDevicePhotos", "scanDevicePhotos-IoAF18A", "searchPhotos", "query", "setFavorite", "isFavorite", "setFavorite-0E7RQCE", "(Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePhoto", "updatePhoto-gIAlu-s", "app_debug"})
public abstract interface PhotoRepository {
    
    /**
     * 获取所有照片（分页）
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<androidx.paging.PagingData<com.qxyu.yucram.domain.model.Photo>> getAllPhotosPaged();
    
    /**
     * 获取所有照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getAllPhotos();
    
    /**
     * 根据ID获取照片
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoById(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.Photo> $completion);
    
    /**
     * 根据ID列表获取照片
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotosByIds(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> photoIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.qxyu.yucram.domain.model.Photo>> $completion);
    
    /**
     * 获取收藏照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getFavoritePhotos();
    
    /**
     * 根据筛选条件获取照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotosWithFilter(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoFilter filter);
    
    /**
     * 搜索照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> searchPhotos(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    /**
     * 根据日期范围获取照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotosByDateRange(@org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime startDate, @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime endDate);
    
    /**
     * 获取Yucram拍摄的照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getYucramPhotos();
    
    /**
     * 根据滤镜获取照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotosByFilter(@org.jetbrains.annotations.NotNull()
    java.lang.String filterName);
    
    /**
     * 根据标签获取照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotosByTag(@org.jetbrains.annotations.NotNull()
    java.lang.String tag);
    
    /**
     * 获取照片统计信息
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoStats(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.PhotoStats> $completion);
    
    /**
     * 获取照片数量
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 获取最近照片
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentPhotos(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.qxyu.yucram.domain.model.Photo>> $completion);
    
    /**
     * 检查照片文件是否存在
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isPhotoFileExists(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * 照片仓库接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}