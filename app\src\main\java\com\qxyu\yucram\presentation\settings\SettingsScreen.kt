package com.qxyu.yucram.presentation.settings

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * 设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateToThemeSettings: () -> Unit,
    onNavigateToAbout: () -> Unit,
    modifier: Modifier = Modifier
) {
    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = "设置",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            item {
                SettingsSection(title = "相机设置")
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.CameraAlt,
                    title = "相机参数",
                    subtitle = "分辨率、格式、质量设置",
                    onClick = { /* TODO: 导航到相机设置 */ }
                )
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.Palette,
                    title = "胶片模拟",
                    subtitle = "滤镜、色彩配置",
                    onClick = { /* TODO: 导航到胶片设置 */ }
                )
            }
            
            item {
                Spacer(modifier = Modifier.height(16.dp))
                SettingsSection(title = "外观设置")
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.DarkMode,
                    title = "主题设置",
                    subtitle = "深色模式、动态颜色",
                    onClick = onNavigateToThemeSettings
                )
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.Language,
                    title = "语言设置",
                    subtitle = "界面语言",
                    onClick = { /* TODO: 导航到语言设置 */ }
                )
            }
            
            item {
                Spacer(modifier = Modifier.height(16.dp))
                SettingsSection(title = "存储设置")
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.Storage,
                    title = "存储位置",
                    subtitle = "照片保存路径",
                    onClick = { /* TODO: 导航到存储设置 */ }
                )
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.CleaningServices,
                    title = "清理缓存",
                    subtitle = "清理临时文件",
                    onClick = { /* TODO: 清理缓存 */ }
                )
            }
            
            item {
                Spacer(modifier = Modifier.height(16.dp))
                SettingsSection(title = "关于")
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.Info,
                    title = "关于Yucram",
                    subtitle = "版本信息、开发者",
                    onClick = onNavigateToAbout
                )
            }
            
            item {
                SettingsItem(
                    icon = Icons.Filled.BugReport,
                    title = "反馈问题",
                    subtitle = "报告错误、建议功能",
                    onClick = { /* TODO: 反馈功能 */ }
                )
            }
        }
    }
}

/**
 * 设置分组标题
 */
@Composable
private fun SettingsSection(
    title: String,
    modifier: Modifier = Modifier
) {
    Text(
        text = title,
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.SemiBold,
        color = MaterialTheme.colorScheme.primary,
        modifier = modifier.padding(vertical = 8.dp)
    )
}

/**
 * 设置项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SettingsItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    trailing: @Composable (() -> Unit)? = null
) {
    Card(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            trailing?.invoke() ?: Icon(
                imageVector = Icons.Filled.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}
