package com.qxyu.yucram.filter

import android.content.Context
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.os.Build
import android.util.Log
import com.qxyu.yucram.domain.model.ImageSourceFormat
import com.qxyu.yucram.domain.model.LogCapability
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 设备能力检测器
 * 检测设备对RAW/LOG格式的支持能力
 */
@Singleton
class DeviceCapabilityDetector @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "DeviceCapabilityDetector"
        
        // OPPO Find X8 Ultra 设备标识
        private const val OPPO_BRAND = "OPPO"
        private const val FIND_X8_ULTRA_MODEL = "Find X8 Ultra"
        private const val FIND_X8_ULTRA_MODEL_ALT = "CPH2651" // 可能的型号代码
    }
    
    private var _logCapability: LogCapability? = null
    private var _supportedFormats: List<ImageSourceFormat>? = null
    
    /**
     * 检测LOG支持能力
     * 注意：大多数手机设备实际上不支持真正的LOG静帧录制
     * 这里主要是为了系统架构的完整性和未来扩展性
     */
    fun detectLogSupport(): LogCapability {
        if (_logCapability != null) {
            return _logCapability!!
        }

        _logCapability = when {
            isOppoFindX8Ultra() -> {
                // OPPO Find X8 Ultra支持10-bit视频，但LOG静帧需要验证
                Log.d(TAG, "检测到OPPO Find X8 Ultra，具备高质量视频能力")
                // 保守起见，暂时不声明支持LOG静帧
                LogCapability.NONE
            }
            isSonyDevice() -> detectSonyLogSupport()
            isCanonDevice() -> detectCanonLogSupport()
            isPanasonicDevice() -> detectPanasonicLogSupport()
            else -> {
                Log.d(TAG, "设备不支持LOG格式")
                LogCapability.NONE
            }
        }

        return _logCapability!!
    }
    
    /**
     * 获取支持的图像格式
     */
    fun getSupportedFormats(): List<ImageSourceFormat> {
        if (_supportedFormats != null) {
            return _supportedFormats!!
        }
        
        val formats = mutableListOf<ImageSourceFormat>()
        
        // 检查RAW支持
        if (isRawSupported()) {
            formats.add(ImageSourceFormat.RAW)
        }
        
        // 检查LOG支持
        if (detectLogSupport() != LogCapability.NONE) {
            formats.add(ImageSourceFormat.LOG)
        }
        
        // JPEG总是支持
        formats.add(ImageSourceFormat.JPEG)
        
        _supportedFormats = formats
        return formats
    }
    
    /**
     * 获取推荐的图像格式
     * 基于实际设备能力推荐最佳格式
     */
    fun getRecommendedFormat(): ImageSourceFormat {
        return when {
            // 优先使用RAW格式（大多数高端手机支持）
            isRawSupported() -> ImageSourceFormat.RAW
            // LOG格式作为未来扩展（目前大多数设备不支持）
            detectLogSupport() != LogCapability.NONE -> ImageSourceFormat.LOG
            // 默认使用JPEG
            else -> ImageSourceFormat.JPEG
        }
    }
    
    /**
     * 检查特定格式是否支持
     */
    fun isFormatSupported(format: ImageSourceFormat): Boolean {
        return when (format) {
            ImageSourceFormat.RAW -> isRawSupported()
            ImageSourceFormat.LOG -> detectLogSupport() != LogCapability.NONE
            ImageSourceFormat.JPEG -> true
        }
    }
    
    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            brand = Build.BRAND,
            model = Build.MODEL,
            androidVersion = Build.VERSION.SDK_INT,
            logCapability = detectLogSupport(),
            supportedFormats = getSupportedFormats()
        )
    }
    
    // 私有方法
    
    /**
     * 检测是否为OPPO Find X8 Ultra
     */
    private fun isOppoFindX8Ultra(): Boolean {
        val brand = Build.BRAND
        val model = Build.MODEL
        
        return brand.equals(OPPO_BRAND, ignoreCase = true) && (
            model.contains(FIND_X8_ULTRA_MODEL, ignoreCase = true) ||
            model.contains(FIND_X8_ULTRA_MODEL_ALT, ignoreCase = true)
        )
    }
    
    /**
     * 检查RAW支持
     */
    private fun isRawSupported(): Boolean {
        return try {
            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            val cameraIds = cameraManager.cameraIdList
            
            for (cameraId in cameraIds) {
                val characteristics = cameraManager.getCameraCharacteristics(cameraId)
                val capabilities = characteristics.get(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES)
                
                capabilities?.let { caps ->
                    if (caps.contains(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_RAW)) {
                        return true
                    }
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "检查RAW支持时出错", e)
            false
        }
    }
    
    /**
     * 检测Sony设备
     */
    private fun isSonyDevice(): Boolean {
        return Build.BRAND.equals("Sony", ignoreCase = true)
    }
    
    /**
     * 检测Canon设备
     */
    private fun isCanonDevice(): Boolean {
        return Build.BRAND.equals("Canon", ignoreCase = true)
    }
    
    /**
     * 检测Panasonic设备
     */
    private fun isPanasonicDevice(): Boolean {
        return Build.BRAND.equals("Panasonic", ignoreCase = true)
    }
    
    /**
     * 检测Sony LOG支持
     */
    private fun detectSonyLogSupport(): LogCapability {
        // 这里可以根据具体的Sony设备型号来判断
        return LogCapability.SONY_SLOG
    }
    
    /**
     * 检测Canon LOG支持
     */
    private fun detectCanonLogSupport(): LogCapability {
        return LogCapability.CANON_LOG
    }
    
    /**
     * 检测Panasonic LOG支持
     */
    private fun detectPanasonicLogSupport(): LogCapability {
        return LogCapability.PANASONIC_VLOG
    }
}

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val brand: String,
    val model: String,
    val androidVersion: Int,
    val logCapability: LogCapability,
    val supportedFormats: List<ImageSourceFormat>
)
