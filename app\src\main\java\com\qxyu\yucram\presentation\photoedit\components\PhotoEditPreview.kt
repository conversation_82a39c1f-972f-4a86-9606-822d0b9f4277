package com.qxyu.yucram.presentation.photoedit.components

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*

/**
 * 照片编辑预览组件
 */
@Composable
fun PhotoEditPreview(
    photo: Photo,
    editParams: PhotoEditParams,
    currentTool: EditTool?,
    onToolInteraction: (ToolInteraction) -> Unit,
    modifier: Modifier = Modifier
) {
    var scale by remember { mutableStateOf(1f) }
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }
    var showBeforeAfter by remember { mutableStateOf(false) }
    var showHistogram by remember { mutableStateOf(false) }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // 主要预览图像
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(photo.filePath)
                .crossfade(true)
                .build(),
            contentDescription = photo.fileName,
            contentScale = ContentScale.Fit,
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer(
                    scaleX = scale,
                    scaleY = scale,
                    translationX = offsetX,
                    translationY = offsetY
                )
                .pointerInput(Unit) {
                    detectTransformGestures(
                        onGesture = { _, pan, zoom, _ ->
                            scale = (scale * zoom).coerceIn(0.5f, 5f)
                            if (scale > 1f) {
                                offsetX += pan.x
                                offsetY += pan.y
                            } else {
                                offsetX = 0f
                                offsetY = 0f
                            }
                        }
                    )
                }
        )
        
        // 工具覆盖层
        currentTool?.let { tool ->
            ToolOverlay(
                tool = tool,
                editParams = editParams,
                onInteraction = onToolInteraction,
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 预览控制栏
        PreviewControls(
            showBeforeAfter = showBeforeAfter,
            onToggleBeforeAfter = { showBeforeAfter = !showBeforeAfter },
            showHistogram = showHistogram,
            onToggleHistogram = { showHistogram = !showHistogram },
            onResetZoom = {
                scale = 1f
                offsetX = 0f
                offsetY = 0f
            },
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(16.dp)
        )
        
        // 编辑信息显示
        EditInfoDisplay(
            editParams = editParams,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
        )
        
        // 直方图显示
        if (showHistogram) {
            HistogramDisplay(
                photo = photo,
                editParams = editParams,
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(16.dp)
                    .size(200.dp, 120.dp)
            )
        }
        
        // 对比显示
        if (showBeforeAfter) {
            BeforeAfterComparison(
                photo = photo,
                editParams = editParams,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

/**
 * 预览控制栏
 */
@Composable
fun PreviewControls(
    showBeforeAfter: Boolean,
    onToggleBeforeAfter: () -> Unit,
    showHistogram: Boolean,
    onToggleHistogram: () -> Unit,
    onResetZoom: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.7f),
                RoundedCornerShape(8.dp)
            )
            .padding(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        // 对比按钮
        IconButton(
            onClick = onToggleBeforeAfter,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = if (showBeforeAfter) Icons.Filled.VisibilityOff else Icons.Filled.Visibility,
                contentDescription = "对比",
                tint = if (showBeforeAfter) MaterialTheme.colorScheme.primary else Color.White
            )
        }
        
        // 直方图按钮
        IconButton(
            onClick = onToggleHistogram,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.BarChart,
                contentDescription = "直方图",
                tint = if (showHistogram) MaterialTheme.colorScheme.primary else Color.White
            )
        }
        
        // 重置缩放按钮
        IconButton(
            onClick = onResetZoom,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.CenterFocusWeak,
                contentDescription = "重置缩放",
                tint = Color.White
            )
        }
    }
}

/**
 * 编辑信息显示
 */
@Composable
fun EditInfoDisplay(
    editParams: PhotoEditParams,
    modifier: Modifier = Modifier
) {
    val hasAdjustments = editParams != PhotoEditParams()
    
    if (hasAdjustments) {
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.7f)
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(12.dp)
            ) {
                Text(
                    text = "已调整",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                // 显示主要调整项
                val adjustments = mutableListOf<String>()
                
                if (editParams.exposure != 0f) {
                    adjustments.add("曝光${formatValue(editParams.exposure)}")
                }
                if (editParams.highlights != 0f) {
                    adjustments.add("高光${formatValue(editParams.highlights)}")
                }
                if (editParams.shadows != 0f) {
                    adjustments.add("阴影${formatValue(editParams.shadows)}")
                }
                if (editParams.vibrance != 0f) {
                    adjustments.add("自然饱和度${formatValue(editParams.vibrance)}")
                }
                if (editParams.saturation != 0f) {
                    adjustments.add("饱和度${formatValue(editParams.saturation)}")
                }
                
                adjustments.take(3).forEach { adjustment ->
                    Text(
                        text = adjustment,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White
                    )
                }
                
                if (adjustments.size > 3) {
                    Text(
                        text = "等${adjustments.size}项调整",
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

/**
 * 工具覆盖层
 */
@Composable
fun ToolOverlay(
    tool: EditTool,
    editParams: PhotoEditParams,
    onInteraction: (ToolInteraction) -> Unit,
    modifier: Modifier = Modifier
) {
    when (tool) {
        EditTool.CROP_ROTATE -> {
            CropOverlay(
                cropRatio = editParams.cropRatio,
                rotation = editParams.rotation,
                onInteraction = onInteraction,
                modifier = modifier
            )
        }
        EditTool.RADIAL_FILTER -> {
            RadialFilterOverlay(
                localAdjustments = editParams.localAdjustments,
                onInteraction = onInteraction,
                modifier = modifier
            )
        }
        EditTool.GRADUATED_FILTER -> {
            GraduatedFilterOverlay(
                localAdjustments = editParams.localAdjustments,
                onInteraction = onInteraction,
                modifier = modifier
            )
        }
        else -> {
            // 其他工具不需要覆盖层
        }
    }
}

/**
 * 直方图显示
 */
@Composable
fun HistogramDisplay(
    photo: Photo,
    editParams: PhotoEditParams,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "直方图",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            
            // TODO: 实现真实的直方图绘制
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp)
                    .background(
                        Color.Gray.copy(alpha = 0.3f),
                        RoundedCornerShape(4.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "直方图显示",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 对比显示
 */
@Composable
fun BeforeAfterComparison(
    photo: Photo,
    editParams: PhotoEditParams,
    modifier: Modifier = Modifier
) {
    // TODO: 实现前后对比功能
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.5f)),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "对比模式",
            style = MaterialTheme.typography.titleMedium,
            color = Color.White
        )
    }
}

/**
 * 裁剪覆盖层
 */
@Composable
fun CropOverlay(
    cropRatio: CropRatio?,
    rotation: Float,
    onInteraction: (ToolInteraction) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现裁剪覆盖层
    Box(modifier = modifier)
}

/**
 * 径向滤镜覆盖层
 */
@Composable
fun RadialFilterOverlay(
    localAdjustments: List<LocalAdjustment>,
    onInteraction: (ToolInteraction) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现径向滤镜覆盖层
    Box(modifier = modifier)
}

/**
 * 渐变滤镜覆盖层
 */
@Composable
fun GraduatedFilterOverlay(
    localAdjustments: List<LocalAdjustment>,
    onInteraction: (ToolInteraction) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现渐变滤镜覆盖层
    Box(modifier = modifier)
}

/**
 * 工具交互事件
 */
sealed class ToolInteraction {
    data class CropChanged(val cropRatio: CropRatio?, val rotation: Float) : ToolInteraction()
    data class LocalAdjustmentAdded(val adjustment: LocalAdjustment) : ToolInteraction()
    data class LocalAdjustmentModified(val id: String, val adjustment: LocalAdjustment) : ToolInteraction()
    data class LocalAdjustmentRemoved(val id: String) : ToolInteraction()
}

/**
 * 格式化数值
 */
private fun formatValue(value: Float): String {
    return when {
        value > 0 -> "+${value.toInt()}"
        value < 0 -> value.toInt().toString()
        else -> "0"
    }
}
