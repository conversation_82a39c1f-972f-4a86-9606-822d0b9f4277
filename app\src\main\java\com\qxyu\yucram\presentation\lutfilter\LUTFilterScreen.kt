package com.qxyu.yucram.presentation.lutfilter

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.lutfilter.components.*

/**
 * LUT滤镜主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LUTFilterScreen(
    photoId: String,
    onNavigateBack: () -> Unit = {},
    onSaveComplete: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: LUTFilterViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val photo by viewModel.photo.collectAsStateWithLifecycle()
    val selectedFilter by viewModel.selectedFilter.collectAsStateWithLifecycle()
    val filterSettings by viewModel.filterSettings.collectAsStateWithLifecycle()
    val currentCategory by viewModel.currentCategory.collectAsStateWithLifecycle()
    val filters by viewModel.filters.collectAsStateWithLifecycle()
    
    var showSaveDialog by remember { mutableStateOf(false) }
    var showFilterInfo by remember { mutableStateOf(false) }
    var showBeforeAfter by remember { mutableStateOf(false) }
    
    LaunchedEffect(photoId) {
        viewModel.loadPhoto(photoId)
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // 顶部工具栏
        LUTFilterTopBar(
            photo = photo,
            selectedFilter = selectedFilter,
            hasChanges = viewModel.hasChanges(),
            onNavigateBack = {
                if (viewModel.hasChanges()) {
                    showSaveDialog = true
                } else {
                    onNavigateBack()
                }
            },
            onReset = { viewModel.resetFilter() },
            onSave = { viewModel.applyFilter() },
            onInfo = { showFilterInfo = true },
            onBeforeAfter = { showBeforeAfter = !showBeforeAfter }
        )
        
        // 主要预览区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            when {
                uiState.isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                photo != null -> {
                    // 照片预览区域
                    LUTFilterPreview(
                        photo = photo!!,
                        selectedFilter = selectedFilter,
                        filterSettings = filterSettings,
                        showBeforeAfter = showBeforeAfter,
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                uiState.error != null -> {
                    LUTFilterError(
                        error = uiState.error!!,
                        onRetry = { viewModel.loadPhoto(photoId) },
                        onNavigateBack = onNavigateBack,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
            
            // 处理进度指示器
            if (uiState.isProcessing) {
                LUTProcessingIndicator(
                    progress = uiState.processingProgress,
                    message = uiState.processingMessage,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
        
        // 滤镜分类选择
        LUTCategorySelector(
            currentCategory = currentCategory,
            onCategorySelected = { category ->
                viewModel.selectCategory(category)
            },
            modifier = Modifier.fillMaxWidth()
        )
        
        // 滤镜列表
        LUTFilterList(
            filters = filters,
            selectedFilter = selectedFilter,
            onFilterSelected = { filter ->
                viewModel.selectFilter(filter)
            },
            onFilterLongPress = { filter ->
                viewModel.showFilterDetails(filter)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
        )
        
        // 滤镜控制面板
        if (selectedFilter != null) {
            LUTFilterControlPanel(
                filter = selectedFilter!!,
                settings = filterSettings,
                onSettingsChanged = { newSettings ->
                    viewModel.updateFilterSettings(newSettings)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
            )
        }
    }
    
    // 保存确认对话框
    if (showSaveDialog) {
        SaveConfirmationDialog(
            onSave = {
                viewModel.applyFilter()
                showSaveDialog = false
                onSaveComplete()
            },
            onDiscard = {
                showSaveDialog = false
                onNavigateBack()
            },
            onCancel = {
                showSaveDialog = false
            }
        )
    }
    
    // 滤镜信息对话框
    if (showFilterInfo && selectedFilter != null) {
        LUTFilterInfoDialog(
            filter = selectedFilter!!,
            onDismiss = { showFilterInfo = false }
        )
    }
    
    // 处理应用完成
    LaunchedEffect(uiState.isApplied) {
        if (uiState.isApplied) {
            onSaveComplete()
        }
    }
}

/**
 * LUT滤镜顶部工具栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LUTFilterTopBar(
    photo: Photo?,
    selectedFilter: LutFilter?,
    hasChanges: Boolean,
    onNavigateBack: () -> Unit,
    onReset: () -> Unit,
    onSave: () -> Unit,
    onInfo: () -> Unit,
    onBeforeAfter: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Column {
                Text(
                    text = selectedFilter?.displayName ?: "LUT滤镜",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White
                )
                if (hasChanges) {
                    Text(
                        text = "已修改",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
        },
        actions = {
            // 前后对比
            IconButton(onClick = onBeforeAfter) {
                Icon(
                    imageVector = Icons.Filled.Compare,
                    contentDescription = "前后对比",
                    tint = Color.White
                )
            }
            
            // 滤镜信息
            if (selectedFilter != null) {
                IconButton(onClick = onInfo) {
                    Icon(
                        imageVector = Icons.Filled.Info,
                        contentDescription = "滤镜信息",
                        tint = Color.White
                    )
                }
            }
            
            // 重置
            IconButton(
                onClick = onReset,
                enabled = hasChanges
            ) {
                Icon(
                    imageVector = Icons.Filled.Refresh,
                    contentDescription = "重置",
                    tint = if (hasChanges) Color.White else Color.White.copy(alpha = 0.3f)
                )
            }
            
            // 应用
            IconButton(
                onClick = onSave,
                enabled = hasChanges
            ) {
                Icon(
                    imageVector = Icons.Filled.Check,
                    contentDescription = "应用",
                    tint = if (hasChanges) MaterialTheme.colorScheme.primary else Color.White.copy(alpha = 0.3f)
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        modifier = modifier
    )
}

/**
 * LUT处理进度指示器
 */
@Composable
fun LUTProcessingIndicator(
    progress: Float,
    message: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        )
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                progress = progress,
                modifier = Modifier.size(48.dp),
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "${(progress * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.7f)
            )
        }
    }
}

/**
 * 保存确认对话框
 */
@Composable
fun SaveConfirmationDialog(
    onSave: () -> Unit,
    onDiscard: () -> Unit,
    onCancel: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onCancel,
        title = {
            Text("应用滤镜？")
        },
        text = {
            Text("您有未保存的滤镜更改，是否要应用到照片？")
        },
        confirmButton = {
            TextButton(onClick = onSave) {
                Text("应用")
            }
        },
        dismissButton = {
            Row {
                TextButton(onClick = onDiscard) {
                    Text("放弃")
                }
                TextButton(onClick = onCancel) {
                    Text("取消")
                }
            }
        }
    )
}

/**
 * LUT滤镜错误状态
 */
@Composable
fun LUTFilterError(
    error: String,
    onRetry: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Filled.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "加载失败",
            style = MaterialTheme.typography.headlineSmall,
            color = Color.White
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                onClick = onNavigateBack,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.White
                )
            ) {
                Text("返回")
            }
            
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}
