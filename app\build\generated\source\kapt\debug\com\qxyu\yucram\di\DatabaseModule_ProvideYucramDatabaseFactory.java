// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import android.content.Context;
import com.qxyu.yucram.data.local.YucramDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideYucramDatabaseFactory implements Factory<YucramDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideYucramDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public YucramDatabase get() {
    return provideYucramDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideYucramDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideYucramDatabaseFactory(contextProvider);
  }

  public static YucramDatabase provideYucramDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideYucramDatabase(context));
  }
}
