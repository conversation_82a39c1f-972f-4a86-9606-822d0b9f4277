// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import android.content.Context;
import com.qxyu.yucram.camera.CameraManager;
import com.qxyu.yucram.filter.ImageProcessingPipeline;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideCameraManagerFactory implements Factory<CameraManager> {
  private final Provider<Context> contextProvider;

  private final Provider<ImageProcessingPipeline> imageProcessingPipelineProvider;

  public AppModule_ProvideCameraManagerFactory(Provider<Context> contextProvider,
      Provider<ImageProcessingPipeline> imageProcessingPipelineProvider) {
    this.contextProvider = contextProvider;
    this.imageProcessingPipelineProvider = imageProcessingPipelineProvider;
  }

  @Override
  public CameraManager get() {
    return provideCameraManager(contextProvider.get(), imageProcessingPipelineProvider.get());
  }

  public static AppModule_ProvideCameraManagerFactory create(Provider<Context> contextProvider,
      Provider<ImageProcessingPipeline> imageProcessingPipelineProvider) {
    return new AppModule_ProvideCameraManagerFactory(contextProvider, imageProcessingPipelineProvider);
  }

  public static CameraManager provideCameraManager(Context context,
      ImageProcessingPipeline imageProcessingPipeline) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideCameraManager(context, imageProcessingPipeline));
  }
}
