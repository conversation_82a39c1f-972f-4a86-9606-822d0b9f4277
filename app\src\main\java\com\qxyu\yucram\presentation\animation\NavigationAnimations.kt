package com.qxyu.yucram.presentation.animation

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.runtime.Composable
import androidx.navigation.NavBackStackEntry

/**
 * 导航动画配置
 */
object NavigationAnimations {
    
    // 动画持续时间
    const val ANIMATION_DURATION = 300
    const val FAST_ANIMATION_DURATION = 200
    const val SLOW_ANIMATION_DURATION = 500
    
    /**
     * 水平滑动进入动画
     */
    fun slideInHorizontally(
        duration: Int = ANIMATION_DURATION,
        initialOffsetX: (Int) -> Int = { it }
    ): EnterTransition {
        return slideInHorizontally(
            initialOffsetX = initialOffsetX,
            animationSpec = tween(
                durationMillis = duration,
                easing = FastOutSlowInEasing
            )
        )
    }
    
    /**
     * 水平滑动退出动画
     */
    fun slideOutHorizontally(
        duration: Int = ANIMATION_DURATION,
        targetOffsetX: (Int) -> Int = { -it }
    ): ExitTransition {
        return slideOutHorizontally(
            targetOffsetX = targetOffsetX,
            animationSpec = tween(
                durationMillis = duration,
                easing = FastOutSlowInEasing
            )
        )
    }
    
    /**
     * 垂直滑动进入动画
     */
    fun slideInVertically(
        duration: Int = ANIMATION_DURATION,
        initialOffsetY: (Int) -> Int = { it }
    ): EnterTransition {
        return slideInVertically(
            initialOffsetY = initialOffsetY,
            animationSpec = tween(
                durationMillis = duration,
                easing = FastOutSlowInEasing
            )
        )
    }
    
    /**
     * 垂直滑动退出动画
     */
    fun slideOutVertically(
        duration: Int = ANIMATION_DURATION,
        targetOffsetY: (Int) -> Int = { -it }
    ): ExitTransition {
        return slideOutVertically(
            targetOffsetY = targetOffsetY,
            animationSpec = tween(
                durationMillis = duration,
                easing = FastOutSlowInEasing
            )
        )
    }
    
    /**
     * 缩放进入动画
     */
    fun scaleIn(
        duration: Int = ANIMATION_DURATION,
        initialScale: Float = 0.8f
    ): EnterTransition {
        return scaleIn(
            initialScale = initialScale,
            animationSpec = tween(
                durationMillis = duration,
                easing = FastOutSlowInEasing
            )
        )
    }
    
    /**
     * 缩放退出动画
     */
    fun scaleOut(
        duration: Int = ANIMATION_DURATION,
        targetScale: Float = 0.8f
    ): ExitTransition {
        return scaleOut(
            targetScale = targetScale,
            animationSpec = tween(
                durationMillis = duration,
                easing = FastOutSlowInEasing
            )
        )
    }
    
    /**
     * 淡入动画
     */
    fun fadeIn(
        duration: Int = ANIMATION_DURATION
    ): EnterTransition {
        return fadeIn(
            animationSpec = tween(
                durationMillis = duration,
                easing = LinearEasing
            )
        )
    }
    
    /**
     * 淡出动画
     */
    fun fadeOut(
        duration: Int = ANIMATION_DURATION
    ): ExitTransition {
        return fadeOut(
            animationSpec = tween(
                durationMillis = duration,
                easing = LinearEasing
            )
        )
    }
    
    /**
     * 组合动画：滑动 + 淡入淡出
     */
    fun slideAndFade(
        direction: SlideDirection = SlideDirection.LEFT_TO_RIGHT,
        duration: Int = ANIMATION_DURATION
    ): Pair<EnterTransition, ExitTransition> {
        return when (direction) {
            SlideDirection.LEFT_TO_RIGHT -> {
                slideInHorizontally(duration) { it } + fadeIn(duration) to
                slideOutHorizontally(duration) { -it } + fadeOut(duration)
            }
            SlideDirection.RIGHT_TO_LEFT -> {
                slideInHorizontally(duration) { -it } + fadeIn(duration) to
                slideOutHorizontally(duration) { it } + fadeOut(duration)
            }
            SlideDirection.TOP_TO_BOTTOM -> {
                slideInVertically(duration) { -it } + fadeIn(duration) to
                slideOutVertically(duration) { it } + fadeOut(duration)
            }
            SlideDirection.BOTTOM_TO_TOP -> {
                slideInVertically(duration) { it } + fadeIn(duration) to
                slideOutVertically(duration) { -it } + fadeOut(duration)
            }
        }
    }
    
    /**
     * 组合动画：缩放 + 淡入淡出
     */
    fun scaleAndFade(
        duration: Int = ANIMATION_DURATION,
        initialScale: Float = 0.8f,
        targetScale: Float = 0.8f
    ): Pair<EnterTransition, ExitTransition> {
        return scaleIn(duration, initialScale) + fadeIn(duration) to
               scaleOut(duration, targetScale) + fadeOut(duration)
    }
    
    /**
     * 弹性动画
     */
    fun springAnimation(
        dampingRatio: Float = Spring.DampingRatioMediumBouncy,
        stiffness: Float = Spring.StiffnessLow
    ): AnimationSpec<Float> {
        return spring(
            dampingRatio = dampingRatio,
            stiffness = stiffness
        )
    }
    
    /**
     * 相机页面专用动画
     */
    fun cameraPageAnimation(): Pair<EnterTransition, ExitTransition> {
        return slideInVertically(FAST_ANIMATION_DURATION) { it } + fadeIn(FAST_ANIMATION_DURATION) to
               slideOutVertically(FAST_ANIMATION_DURATION) { it } + fadeOut(FAST_ANIMATION_DURATION)
    }
    
    /**
     * 图库页面专用动画
     */
    fun galleryPageAnimation(): Pair<EnterTransition, ExitTransition> {
        return slideInHorizontally(ANIMATION_DURATION) { it } + fadeIn(ANIMATION_DURATION) to
               slideOutHorizontally(ANIMATION_DURATION) { -it } + fadeOut(ANIMATION_DURATION)
    }
    
    /**
     * 设置页面专用动画
     */
    fun settingsPageAnimation(): Pair<EnterTransition, ExitTransition> {
        return slideInHorizontally(ANIMATION_DURATION) { it } + scaleIn(ANIMATION_DURATION, 0.95f) to
               slideOutHorizontally(ANIMATION_DURATION) { it } + scaleOut(ANIMATION_DURATION, 0.95f)
    }
    
    /**
     * 照片详情页面动画
     */
    fun photoDetailAnimation(): Pair<EnterTransition, ExitTransition> {
        return scaleIn(SLOW_ANIMATION_DURATION, 0.7f) + fadeIn(SLOW_ANIMATION_DURATION) to
               scaleOut(SLOW_ANIMATION_DURATION, 0.7f) + fadeOut(SLOW_ANIMATION_DURATION)
    }
}

/**
 * 滑动方向枚举
 */
enum class SlideDirection {
    LEFT_TO_RIGHT,
    RIGHT_TO_LEFT,
    TOP_TO_BOTTOM,
    BOTTOM_TO_TOP
}

/**
 * 动画类型枚举
 */
enum class AnimationType {
    SLIDE,
    FADE,
    SCALE,
    SLIDE_AND_FADE,
    SCALE_AND_FADE,
    SPRING
}
