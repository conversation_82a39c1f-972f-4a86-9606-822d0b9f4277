package com.qxyu.yucram.camera;

/**
 * RAW图像捕获管理器
 * 负责RAW格式图像的捕获和基础处理
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u0012\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\n\b\u0007\u0018\u0000 22\u00020\u0001:\u00012B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0019H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001b\u0010\u001cJ\b\u0010\u001d\u001a\u00020\u001eH\u0002J\u0006\u0010\u001f\u001a\u00020\u001eJ\u0016\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020#2\u0006\u0010$\u001a\u00020%J\b\u0010&\u001a\u0004\u0018\u00010\u0017J$\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u001e0\u00192\u0006\u0010(\u001a\u00020)H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b*\u0010+J\u0006\u0010\u0011\u001a\u00020\u0012J\u0010\u0010,\u001a\u00020\u001e2\u0006\u0010-\u001a\u00020#H\u0002J\u0010\u0010.\u001a\u00020\u001e2\u0006\u0010-\u001a\u00020#H\u0002J\b\u0010/\u001a\u00020\u001eH\u0002J\b\u00100\u001a\u00020\u001eH\u0002J\b\u00101\u001a\u00020\u001eH\u0002R\u0010\u0010\u0005\u001a\u0004\u0018\u00010\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000b\u001a\u0004\u0018\u00010\fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\r\u001a\u0004\u0018\u00010\u000eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0014X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00063"}, d2 = {"Lcom/qxyu/yucram/camera/RawCaptureManager;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "backgroundHandler", "Landroid/os/Handler;", "backgroundThread", "Landroid/os/HandlerThread;", "cameraDevice", "Landroid/hardware/camera2/CameraDevice;", "cameraManager", "Lcom/qxyu/yucram/camera/CameraManager;", "captureSession", "Landroid/hardware/camera2/CameraCaptureSession;", "characteristics", "Landroid/hardware/camera2/CameraCharacteristics;", "isRawSupported", "", "jpegImageReader", "Landroid/media/ImageReader;", "rawImageReader", "rawSize", "Landroid/util/Size;", "captureRawImage", "Lkotlin/Result;", "Lcom/qxyu/yucram/camera/RawCaptureResult;", "captureRawImage-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkRawSupport", "", "cleanup", "createDngFile", "", "rawImage", "Landroid/media/Image;", "captureResult", "Landroid/hardware/camera2/CaptureResult;", "getRawSize", "initialize", "cameraId", "", "initialize-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "processJpegImage", "image", "processRawImage", "setupImageReaders", "startBackgroundThread", "stopBackgroundThread", "Companion", "app_debug"})
public final class RawCaptureManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "RawCaptureManager";
    private static final int MAX_IMAGES = 2;
    @org.jetbrains.annotations.Nullable()
    private com.qxyu.yucram.camera.CameraManager cameraManager;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.camera2.CameraDevice cameraDevice;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.camera2.CameraCaptureSession captureSession;
    @org.jetbrains.annotations.Nullable()
    private android.hardware.camera2.CameraCharacteristics characteristics;
    @org.jetbrains.annotations.Nullable()
    private android.media.ImageReader rawImageReader;
    @org.jetbrains.annotations.Nullable()
    private android.media.ImageReader jpegImageReader;
    @org.jetbrains.annotations.Nullable()
    private android.os.HandlerThread backgroundThread;
    @org.jetbrains.annotations.Nullable()
    private android.os.Handler backgroundHandler;
    private boolean isRawSupported = false;
    @org.jetbrains.annotations.Nullable()
    private android.util.Size rawSize;
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.camera.RawCaptureManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public RawCaptureManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 检查设备RAW支持能力
     */
    private final void checkRawSupport() {
    }
    
    /**
     * 设置图像读取器
     */
    private final void setupImageReaders() {
    }
    
    /**
     * 处理RAW图像
     */
    private final void processRawImage(android.media.Image image) {
    }
    
    /**
     * 处理JPEG图像
     */
    private final void processJpegImage(android.media.Image image) {
    }
    
    /**
     * 创建DNG文件
     */
    @org.jetbrains.annotations.NotNull()
    public final byte[] createDngFile(@org.jetbrains.annotations.NotNull()
    android.media.Image rawImage, @org.jetbrains.annotations.NotNull()
    android.hardware.camera2.CaptureResult captureResult) {
        return null;
    }
    
    /**
     * 获取RAW支持状态
     */
    public final boolean isRawSupported() {
        return false;
    }
    
    /**
     * 获取RAW图像尺寸
     */
    @org.jetbrains.annotations.Nullable()
    public final android.util.Size getRawSize() {
        return null;
    }
    
    /**
     * 启动后台线程
     */
    private final void startBackgroundThread() {
    }
    
    /**
     * 停止后台线程
     */
    private final void stopBackgroundThread() {
    }
    
    /**
     * 清理资源
     */
    public final void cleanup() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/qxyu/yucram/camera/RawCaptureManager$Companion;", "", "()V", "MAX_IMAGES", "", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}