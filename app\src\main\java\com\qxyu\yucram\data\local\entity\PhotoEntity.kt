package com.qxyu.yucram.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * 照片数据库实体
 */
@Entity(tableName = "photos")
data class PhotoEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val filePath: String,
    val fileName: String,
    val dateCreated: Long,
    val dateModified: Long,
    val size: Long,
    val width: Int,
    val height: Int,
    val mimeType: String,
    val isRaw: Boolean = false,
    val hasFilter: Boolean = false,
    val filterName: String? = null,
    val latitude: Double? = null,
    val longitude: Double? = null,
    val isFavorite: Boolean = false,
    val tags: String = "" // JSON string of tags
)
