{"logs": [{"outputFile": "com.qxyu.yucram.app-mergeDebugResources-74:/values-ka/values-ka.xml", "map": [{"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\90847ba8583f288576ac60503f85644e\\transformed\\core-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3262,3358,3460,3559,3658,3764,3868,9227", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3353,3455,3554,3653,3759,3863,3981,9323"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\e6ffc869bf3a530da82c75f86303dcbf\\transformed\\jetified-material3-1.1.2\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,399,509,586,681,794,926,1040,1184,1266,1364,1454,1548,1666,1782,1885,2016,2149,2280,2448,2573,2686,2801,2919,3010,3103,3217,3352,3451,3549,3656,3789,3925,4032,4130,4203,4284,4366,4449,4558,4634,4715,4812,4911,5001,5099,5181,5283,5375,5478,5591,5667,5769", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "164,277,394,504,581,676,789,921,1035,1179,1261,1359,1449,1543,1661,1777,1880,2011,2144,2275,2443,2568,2681,2796,2914,3005,3098,3212,3347,3446,3544,3651,3784,3920,4027,4125,4198,4279,4361,4444,4553,4629,4710,4807,4906,4996,5094,5176,5278,5370,5473,5586,5662,5764,5857"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2808,2922,3035,3152,4168,4245,4340,4453,4585,4699,4843,4925,5023,5113,5207,5325,5441,5544,5675,5808,5939,6107,6232,6345,6460,6578,6669,6762,6876,7011,7110,7208,7315,7448,7584,7691,7991,8154,8908,9144,9328,9707,9783,9864,9961,10060,10150,10248,10330,10432,10524,10627,10740,10816,10918", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "2917,3030,3147,3257,4240,4335,4448,4580,4694,4838,4920,5018,5108,5202,5320,5436,5539,5670,5803,5934,6102,6227,6340,6455,6573,6664,6757,6871,7006,7105,7203,7310,7443,7579,7686,7784,8059,8230,8985,9222,9432,9778,9859,9956,10055,10145,10243,10325,10427,10519,10622,10735,10811,10913,11006"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\b687dbcc02c458c0942b5ddaef0cb144\\transformed\\appcompat-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,8990", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,9067"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\df3071bcb830d4a3b4d94201c338cea1\\transformed\\jetified-ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1003,1072,1161,1252,1324,1403,1473", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,998,1067,1156,1247,1319,1398,1468,1589"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3986,4082,7789,7888,8064,8235,8315,8411,8501,8588,8659,8728,8817,9072,9437,9516,9586", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "4077,4163,7883,7986,8149,8310,8406,8496,8583,8654,8723,8812,8903,9139,9511,9581,9702"}}]}]}