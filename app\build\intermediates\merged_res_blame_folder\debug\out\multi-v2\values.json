{"logs": [{"outputFile": "com.qxyu.yucram.app-mergeDebugResources-74:/values/values.xml", "map": [{"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\02bc2a2f1c68cc85101c5666ca565cb2\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "316", "startColumns": "4", "startOffsets": "19695", "endColumns": "42", "endOffsets": "19733"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\f3c8b23b740de844b26b7d88c39c5413\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "319", "startColumns": "4", "startOffsets": "19852", "endColumns": "49", "endOffsets": "19897"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\da65f349106ae3cf13c62b0a1ebe2f6f\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "288,293", "startColumns": "4,4", "startOffsets": "18245,18488", "endColumns": "53,66", "endOffsets": "18294,18550"}}, {"source": "D:\\project\\Androidstd\\y3\\yu augment1.0.0\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2,26", "startColumns": "4,4", "startOffsets": "55,1269", "endLines": "24,30", "endColumns": "12,12", "endOffsets": "1263,1527"}, "to": {"startLines": "1887,1910", "startColumns": "4,4", "startOffsets": "123500,124592", "endLines": "1909,1914", "endColumns": "12,12", "endOffsets": "124587,124850"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\df3071bcb830d4a3b4d94201c338cea1\\transformed\\jetified-ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,280,282,283,285,287,321,374,375,412,413,417,428,429,433,434,435,437,438,439,444,451,452,453,1559,1562,1565", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15696,15755,15814,15874,15934,15994,16054,16114,16174,16234,16294,16354,16414,16473,16533,16593,16653,16713,16773,16833,16893,16953,17013,17073,17132,17192,17252,17311,17370,17429,17488,17547,17811,17935,17993,18105,18190,19966,23502,23567,26205,26271,26502,26985,27037,27277,27339,27393,27463,27497,27547,27788,28172,28219,28255,98802,98914,99025", "endLines": "244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,280,282,283,285,287,321,374,375,412,413,417,428,429,433,434,435,437,438,439,444,451,452,453,1561,1564,1568", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "15750,15809,15869,15929,15989,16049,16109,16169,16229,16289,16349,16409,16468,16528,16588,16648,16708,16768,16828,16888,16948,17008,17068,17127,17187,17247,17306,17365,17424,17483,17542,17601,17880,17988,18043,18151,18240,20014,23562,23616,26266,26367,26555,27032,27092,27334,27388,27424,27492,27542,27596,27829,28214,28250,28340,98909,99020,99215"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\4932d34c6b15c9cd20e35237208ab938\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "22248", "endColumns": "82", "endOffsets": "22326"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\eed9ed84c5790d1d8e05a790e87d6f62\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,170,218,274,349,425,497,563", "endLines": "2,3,4,5,6,7,8,9,38", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "106,165,213,269,344,420,492,558,2084"}, "to": {"startLines": "10,205,206,207,215,216,217,289,3128", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "511,13055,13114,13162,13829,13904,13980,18299,171358", "endLines": "10,205,206,207,215,216,217,289,3148", "endColumns": "55,58,47,55,74,75,71,65,24", "endOffsets": "562,13109,13157,13213,13899,13975,14047,18360,172193"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\b2b3f0bb1d97c1855bae9d8207cf6665\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "284,297,320,2820,2825", "startColumns": "4,4,4,4,4", "startOffsets": "18048,18693,19902,161301,161471", "endLines": "284,297,320,2824,2828", "endColumns": "56,64,63,24,24", "endOffsets": "18100,18753,19961,161466,161615"}}, {"source": "D:\\project\\Androidstd\\y3\\yu augment1.0.0\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "30,35,2,33,27,10,5,39,41,34,43,13,16,18,17,11,21,26,25,22,7,38,19,40,12,42,6,28,20,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1113,1284,55,1187,967,364,124,1396,1470,1236,1545,481,554,640,598,402,766,921,874,809,262,1362,683,1434,441,1508,193,1013,728,1061", "endColumns": "49,52,43,48,45,37,68,37,37,47,35,38,43,42,41,38,42,45,46,37,72,33,44,35,39,36,68,47,37,51", "endOffsets": "1158,1332,94,1231,1008,397,188,1429,1503,1279,1576,515,593,678,635,436,804,962,916,842,330,1391,723,1465,476,1540,257,1056,761,1108"}, "to": {"startLines": "354,356,357,358,359,371,372,373,414,415,418,420,421,422,423,424,425,426,427,430,431,436,440,441,445,446,449,454,467,470", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22198,22331,22384,22428,22477,23357,23395,23464,26372,26410,26560,26642,26681,26725,26768,26810,26849,26892,26938,27097,27135,27429,27601,27646,27834,27874,28035,28345,29145,29310", "endColumns": "49,52,43,48,45,37,68,37,37,47,35,38,43,42,41,38,42,45,46,37,72,33,44,35,39,36,68,47,37,51", "endOffsets": "22243,22379,22423,22472,22518,23390,23459,23497,26405,26453,26591,26676,26720,26763,26805,26844,26887,26933,26980,27130,27203,27458,27641,27677,27869,27906,28099,28388,29178,29357"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\b687dbcc02c458c0942b5ddaef0cb144\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,8,19,20,21,22,23,24,25,26,29,30,36,37,39,40,44,45,46,47,48,49,50,51,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,87,88,89,90,91,92,96,97,98,99,104,105,106,107,111,112,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,203,204,208,209,210,211,212,213,214,233,234,235,236,237,238,239,240,276,277,278,279,286,294,295,298,315,322,323,324,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,443,471,472,473,474,475,476,484,485,489,493,497,502,508,515,519,523,528,532,536,540,544,548,552,558,562,568,572,578,582,587,591,594,598,604,608,614,618,624,627,631,635,639,643,647,648,649,650,653,656,659,662,666,667,668,669,670,673,675,677,679,684,685,689,695,699,700,702,714,715,719,725,729,730,731,735,762,766,767,771,799,971,997,1168,1194,1225,1233,1239,1255,1277,1282,1287,1297,1306,1315,1319,1326,1345,1352,1353,1362,1365,1368,1372,1376,1380,1383,1384,1389,1394,1404,1409,1416,1422,1423,1426,1430,1435,1437,1439,1442,1445,1447,1451,1454,1461,1464,1467,1471,1473,1477,1479,1481,1483,1487,1495,1503,1515,1521,1530,1533,1544,1547,1548,1553,1554,1569,1638,1708,1709,1719,1728,1729,1731,1735,1738,1741,1744,1747,1750,1753,1756,1760,1763,1766,1769,1773,1776,1780,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1806,1808,1809,1810,1811,1812,1813,1814,1815,1817,1818,1820,1821,1823,1825,1826,1828,1829,1830,1831,1832,1833,1835,1836,1837,1838,1839,1851,1853,1855,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1871,1872,1873,1874,1875,1876,1877,1879,1883,1915,1916,1917,1918,1919,1920,1924,1925,1926,1927,1929,1931,1933,1935,1937,1938,1939,1940,1942,1944,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1960,1961,1962,1963,1965,1967,1968,1970,1971,1973,1975,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1990,1991,1992,1993,1995,1996,1997,1998,1999,2001,2003,2005,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2024,2099,2102,2105,2108,2122,2135,2177,2180,2209,2236,2245,2309,2672,2682,2720,2748,2868,2892,2898,2904,2925,3049,3108,3114,3122,3149,3184,3216,3282,3302,3357,3369,3395", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,874,915,970,1032,1096,1166,1227,1302,1471,1548,1929,2014,2147,2223,2441,2518,2596,2702,2808,2887,2967,3024,3213,3287,3362,3427,3493,3553,3614,3686,3759,3826,3951,4010,4069,4128,4187,4246,4300,4354,4407,4461,4515,4569,4804,4878,4957,5030,5262,5333,5405,5477,5550,5607,5819,5892,5966,6040,6304,6376,6449,6519,6734,6794,6945,7014,7083,7153,7227,7303,7367,7444,7520,7597,7662,7731,7808,7883,7952,8020,8097,8163,8224,8321,8386,8455,8554,8625,8684,8742,8799,8858,8922,8993,9065,9137,9209,9281,9348,9416,9484,9543,9606,9670,9760,9851,9911,9977,10044,10110,10180,10244,10297,10364,10425,10492,10605,10663,10726,10791,10856,10931,11004,11076,11120,11167,11213,11262,11323,11384,11445,11507,11571,11635,11699,11764,11827,11887,11948,12014,12073,12133,12195,12266,12326,12882,12968,13218,13308,13395,13483,13565,13648,13738,15030,15082,15140,15185,15251,15315,15372,15429,17606,17663,17711,17760,18156,18555,18602,18758,19663,20019,20083,20145,20205,20332,20406,20476,20554,20608,20678,20763,20811,20857,20918,20981,21047,21111,21182,21245,21310,21374,21435,21496,21548,21621,21695,21764,21839,21913,21987,22128,27735,29362,29440,29530,29618,29714,29804,30386,30475,30722,31003,31255,31540,31933,32410,32632,32854,33130,33357,33587,33817,34047,34277,34504,34923,35149,35574,35804,36232,36451,36734,36942,37073,37300,37726,37951,38378,38599,39024,39144,39420,39721,40045,40336,40650,40787,40918,41023,41265,41432,41636,41844,42115,42227,42339,42444,42561,42775,42921,43061,43147,43495,43583,43829,44247,44496,44578,44676,45333,45433,45685,46109,46364,46458,46547,46784,48808,49050,49152,49405,51561,62242,63758,74453,75981,77738,78364,78784,80045,81310,81566,81802,82349,82843,83448,83646,84226,85594,85969,86087,86625,86782,86978,87251,87507,87677,87818,87882,88247,88614,89290,89554,89892,90245,90339,90525,90831,91093,91218,91345,91584,91795,91914,92107,92284,92739,92920,93042,93301,93414,93601,93703,93810,93939,94214,94722,95218,96095,96389,96959,97108,97840,98012,98096,98432,98524,99220,104451,109822,109884,110462,111046,111137,111250,111479,111639,111791,111962,112128,112297,112464,112627,112870,113040,113213,113384,113658,113857,114062,114392,114476,114572,114668,114766,114866,114968,115070,115172,115274,115376,115476,115572,115684,115813,115936,116067,116198,116296,116410,116504,116644,116778,116874,116986,117086,117202,117298,117410,117510,117650,117786,117950,118080,118238,118388,118529,118673,118808,118920,119070,119198,119326,119462,119594,119724,119854,119966,120864,121010,121154,121292,121358,121448,121524,121628,121718,121820,121928,122036,122136,122216,122308,122406,122516,122568,122646,122752,122844,122948,123058,123180,123343,124855,124935,125035,125125,125235,125325,125566,125660,125766,125858,125958,126070,126184,126300,126416,126510,126624,126736,126838,126958,127080,127162,127266,127386,127512,127610,127704,127792,127904,128020,128142,128254,128429,128545,128631,128723,128835,128959,129026,129152,129220,129348,129492,129620,129689,129784,129899,130012,130111,130220,130331,130442,130543,130648,130748,130878,130969,131092,131186,131298,131384,131488,131584,131672,131790,131894,131998,132124,132212,132320,132420,132510,132620,132704,132806,132890,132944,133008,133114,133200,133310,133394,133653,136269,136387,136502,136582,136943,137480,138884,138962,140306,141667,142055,144898,154951,155289,156960,158317,162469,163220,163482,163682,164061,168339,170620,170849,171143,172198,173281,174131,177157,177901,180032,180372,181683", "endLines": "2,3,8,19,20,21,22,23,24,25,26,29,30,36,37,39,40,44,45,46,47,48,49,50,51,54,55,56,57,58,59,60,61,62,63,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,87,88,89,90,91,92,96,97,98,99,104,105,106,107,111,112,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,203,204,208,209,210,211,212,213,214,233,234,235,236,237,238,239,240,276,277,278,279,286,294,295,298,315,322,323,324,325,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,443,471,472,473,474,475,483,484,488,492,496,501,507,514,518,522,527,531,535,539,543,547,551,557,561,567,571,577,581,586,590,593,597,603,607,613,617,623,626,630,634,638,642,646,647,648,649,652,655,658,661,665,666,667,668,669,672,674,676,678,683,684,688,694,698,699,701,713,714,718,724,728,729,730,734,761,765,766,770,798,970,996,1167,1193,1224,1232,1238,1254,1276,1281,1286,1296,1305,1314,1318,1325,1344,1351,1352,1361,1364,1367,1371,1375,1379,1382,1383,1388,1393,1403,1408,1415,1421,1422,1425,1429,1434,1436,1438,1441,1444,1446,1450,1453,1460,1463,1466,1470,1472,1476,1478,1480,1482,1486,1494,1502,1514,1520,1529,1532,1543,1546,1547,1552,1553,1558,1637,1707,1708,1718,1727,1728,1730,1734,1737,1740,1743,1746,1749,1752,1755,1759,1762,1765,1768,1772,1775,1779,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1805,1807,1808,1809,1810,1811,1812,1813,1814,1816,1817,1819,1820,1822,1824,1825,1827,1828,1829,1830,1831,1832,1834,1835,1836,1837,1838,1839,1852,1854,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1870,1871,1872,1873,1874,1875,1876,1878,1882,1886,1915,1916,1917,1918,1919,1923,1924,1925,1926,1928,1930,1932,1934,1936,1937,1938,1939,1941,1943,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1959,1960,1961,1962,1964,1966,1967,1969,1970,1972,1974,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1989,1990,1991,1992,1994,1995,1996,1997,1998,2000,2002,2004,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2098,2101,2104,2107,2121,2127,2144,2179,2208,2235,2244,2308,2671,2675,2709,2747,2765,2891,2897,2903,2924,3048,3068,3113,3117,3127,3183,3195,3281,3301,3356,3368,3394,3401", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,910,965,1027,1091,1161,1222,1297,1373,1543,1621,2009,2091,2218,2294,2513,2591,2697,2803,2882,2962,3019,3077,3282,3357,3422,3488,3548,3609,3681,3754,3821,3889,4005,4064,4123,4182,4241,4295,4349,4402,4456,4510,4564,4618,4873,4952,5025,5099,5328,5400,5472,5545,5602,5660,5887,5961,6035,6110,6371,6444,6514,6585,6789,6850,7009,7078,7148,7222,7298,7362,7439,7515,7592,7657,7726,7803,7878,7947,8015,8092,8158,8219,8316,8381,8450,8549,8620,8679,8737,8794,8853,8917,8988,9060,9132,9204,9276,9343,9411,9479,9538,9601,9665,9755,9846,9906,9972,10039,10105,10175,10239,10292,10359,10420,10487,10600,10658,10721,10786,10851,10926,10999,11071,11115,11162,11208,11257,11318,11379,11440,11502,11566,11630,11694,11759,11822,11882,11943,12009,12068,12128,12190,12261,12321,12389,12963,13050,13303,13390,13478,13560,13643,13733,13824,15077,15135,15180,15246,15310,15367,15424,15478,17658,17706,17755,17806,18185,18597,18646,18799,19690,20078,20140,20200,20257,20401,20471,20549,20603,20673,20758,20806,20852,20913,20976,21042,21106,21177,21240,21305,21369,21430,21491,21543,21616,21690,21759,21834,21908,21982,22123,22193,27783,29435,29525,29613,29709,29799,30381,30470,30717,30998,31250,31535,31928,32405,32627,32849,33125,33352,33582,33812,34042,34272,34499,34918,35144,35569,35799,36227,36446,36729,36937,37068,37295,37721,37946,38373,38594,39019,39139,39415,39716,40040,40331,40645,40782,40913,41018,41260,41427,41631,41839,42110,42222,42334,42439,42556,42770,42916,43056,43142,43490,43578,43824,44242,44491,44573,44671,45328,45428,45680,46104,46359,46453,46542,46779,48803,49045,49147,49400,51556,62237,63753,74448,75976,77733,78359,78779,80040,81305,81561,81797,82344,82838,83443,83641,84221,85589,85964,86082,86620,86777,86973,87246,87502,87672,87813,87877,88242,88609,89285,89549,89887,90240,90334,90520,90826,91088,91213,91340,91579,91790,91909,92102,92279,92734,92915,93037,93296,93409,93596,93698,93805,93934,94209,94717,95213,96090,96384,96954,97103,97835,98007,98091,98427,98519,98797,104446,109817,109879,110457,111041,111132,111245,111474,111634,111786,111957,112123,112292,112459,112622,112865,113035,113208,113379,113653,113852,114057,114387,114471,114567,114663,114761,114861,114963,115065,115167,115269,115371,115471,115567,115679,115808,115931,116062,116193,116291,116405,116499,116639,116773,116869,116981,117081,117197,117293,117405,117505,117645,117781,117945,118075,118233,118383,118524,118668,118803,118915,119065,119193,119321,119457,119589,119719,119849,119961,120101,121005,121149,121287,121353,121443,121519,121623,121713,121815,121923,122031,122131,122211,122303,122401,122511,122563,122641,122747,122839,122943,123053,123175,123338,123495,124930,125030,125120,125230,125320,125561,125655,125761,125853,125953,126065,126179,126295,126411,126505,126619,126731,126833,126953,127075,127157,127261,127381,127507,127605,127699,127787,127899,128015,128137,128249,128424,128540,128626,128718,128830,128954,129021,129147,129215,129343,129487,129615,129684,129779,129894,130007,130106,130215,130326,130437,130538,130643,130743,130873,130964,131087,131181,131293,131379,131483,131579,131667,131785,131889,131993,132119,132207,132315,132415,132505,132615,132699,132801,132885,132939,133003,133109,133195,133305,133389,133509,136264,136382,136497,136577,136938,137171,137992,138957,140301,141662,142050,144893,154946,155081,156654,158312,158884,163215,163477,163677,164056,168334,168940,170844,170995,171353,173276,173588,177152,177896,180027,180367,181678,181881"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\387087f4006821e551dafecba6232cd5\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2145,2161,2167,3196,3212", "startColumns": "4,4,4,4,4", "startOffsets": "137997,138422,138600,173593,174004", "endLines": "2160,2166,2176,3211,3215", "endColumns": "24,24,24,24,24", "endOffsets": "138417,138595,138879,173999,174126"}}, {"source": "D:\\project\\Androidstd\\y3\\yu augment1.0.0\\app\\src\\main\\res\\values\\ic_launcher_background.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "64", "startColumns": "4", "startOffsets": "3894", "endColumns": "56", "endOffsets": "3946"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\9b90a5a854d365de55e1f42cebf3048b\\transformed\\navigation-common-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3069,3082,3088,3094,3103", "startColumns": "4,4,4,4,4", "startOffsets": "168945,169584,169828,170075,170438", "endLines": "3081,3087,3093,3096,3107", "endColumns": "24,24,24,24,24", "endOffsets": "169579,169823,170070,170203,170615"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\0927b5fb657df5ed10e89c0837507fc2\\transformed\\navigation-runtime-2.7.6\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "292,2128,3097,3100", "startColumns": "4,4,4,4", "startOffsets": "18435,137176,170208,170323", "endLines": "292,2134,3099,3102", "endColumns": "52,24,24,24", "endOffsets": "18483,137475,170318,170433"}}, {"source": "D:\\project\\Androidstd\\y3\\yu augment1.0.0\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "30,28,29,31,13,15,14,35,34,41,3,4,5,8,9,10,40,18,20,19,23,24,25,37,36", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1109,1017,1064,1155,481,583,531,1285,1240,1497,98,147,201,291,341,390,1452,667,763,714,841,888,937,1375,1330", "endColumns": "45,46,44,47,49,50,51,44,44,48,48,53,54,49,48,54,44,46,47,48,46,48,47,44,44", "endOffsets": "1150,1059,1104,1198,526,629,578,1325,1280,1541,142,196,251,336,385,440,1492,709,806,758,883,932,980,1415,1370"}, "to": {"startLines": "27,28,31,32,35,38,41,42,43,77,84,85,86,93,94,95,100,101,102,103,108,109,110,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1378,1424,1626,1671,1879,2096,2299,2351,2396,4623,5104,5153,5207,5665,5715,5764,6115,6160,6207,6255,6590,6637,6686,6855,6900", "endColumns": "45,46,44,47,49,50,51,44,44,48,48,53,54,49,48,54,44,46,47,48,46,48,47,44,44", "endOffsets": "1419,1466,1666,1714,1924,2142,2346,2391,2436,4667,5148,5202,5257,5710,5759,5814,6155,6202,6250,6299,6632,6681,6729,6895,6940"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\90847ba8583f288576ac60503f85644e\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,33,34,52,53,78,79,196,197,198,199,200,201,202,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,241,242,243,290,291,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,326,364,365,366,367,368,369,370,448,1840,1841,1845,1846,1850,2022,2023,2676,2710,2766,2799,2829,2862", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1719,1791,3082,3147,4672,4741,12394,12464,12532,12604,12674,12735,12809,14052,14113,14174,14236,14300,14362,14423,14491,14591,14651,14717,14790,14859,14916,14968,15483,15555,15631,18365,18400,18804,18859,18922,18977,19035,19093,19154,19217,19274,19325,19375,19436,19493,19559,19593,19628,20262,22846,22913,22985,23054,23123,23197,23269,27964,120106,120223,120424,120534,120735,133514,133586,155086,156659,158889,160620,161620,162302", "endLines": "9,33,34,52,53,78,79,196,197,198,199,200,201,202,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,241,242,243,290,291,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,326,364,365,366,367,368,369,370,448,1840,1844,1845,1849,1850,2022,2023,2681,2719,2798,2819,2861,2867", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1786,1874,3142,3208,4736,4799,12459,12527,12599,12669,12730,12804,12877,14108,14169,14231,14295,14357,14418,14486,14586,14646,14712,14785,14854,14911,14963,15025,15550,15626,15691,18395,18430,18854,18917,18972,19030,19088,19149,19212,19269,19320,19370,19431,19488,19554,19588,19623,19658,20327,22908,22980,23049,23118,23192,23264,23352,28030,120218,120419,120529,120730,120859,133581,133648,155284,156955,160615,161296,162297,162464"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\6f3cb205eccbb0d031dbb3b1dd978e9b\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "318", "startColumns": "4", "startOffsets": "19798", "endColumns": "53", "endOffsets": "19847"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\e6ffc869bf3a530da82c75f86303dcbf\\transformed\\jetified-material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,139,221,298,378,426,487,566,668,750,866,916,981,1038,1103,1188,1279,1349,1442,1531,1625,1770,1857,1941,2033,2127,2187,2251,2334,2424,2487,2555,2623,2720,2825,2897,2962,3006,3052,3121,3174,3227,3295,3341,3391,3458,3525,3591,3656,3710,3782,3849,3919,4001,4047,4113", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "134,216,293,373,421,482,561,663,745,861,911,976,1033,1098,1183,1274,1344,1437,1526,1620,1765,1852,1936,2028,2122,2182,2246,2329,2419,2482,2550,2618,2715,2820,2892,2957,3001,3047,3116,3169,3222,3290,3336,3386,3453,3520,3586,3651,3705,3777,3844,3914,3996,4042,4108,4169"}, "to": {"startLines": "360,361,362,363,376,377,378,379,380,381,384,385,386,387,388,389,390,391,392,393,394,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,416,419,432,442,447,450,455,456,457,458,459,460,461,462,463,464,465,466,468,469", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "22523,22607,22689,22766,23621,23669,23730,23809,23911,23993,24109,24159,24224,24281,24346,24431,24522,24592,24685,24774,24868,25013,25100,25184,25276,25370,25430,25494,25577,25667,25730,25798,25866,25963,26068,26140,26458,26596,27208,27682,27911,28104,28393,28439,28489,28556,28623,28689,28754,28808,28880,28947,29017,29099,29183,29249", "endLines": "360,361,362,363,376,377,378,379,380,383,384,385,386,387,388,389,390,391,392,393,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,416,419,432,442,447,450,455,456,457,458,459,460,461,462,463,464,465,466,468,469", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "22602,22684,22761,22841,23664,23725,23804,23906,23988,24104,24154,24219,24276,24341,24426,24517,24587,24680,24769,24863,25008,25095,25179,25271,25365,25425,25489,25572,25662,25725,25793,25861,25958,26063,26135,26200,26497,26637,27272,27730,27959,28167,28434,28484,28551,28618,28684,28749,28803,28875,28942,29012,29094,29140,29244,29305"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\b20592846c0f2792d5204722bd71df0f\\transformed\\jetified-camera-view-1.3.4\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,11,3118", "startColumns": "4,4,4", "startOffsets": "250,567,171000", "endLines": "7,18,3121", "endColumns": "11,11,24", "endOffsets": "397,869,171138"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\e432500d24233a66680c501b24e12143\\transformed\\jetified-coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "281", "startColumns": "4", "startOffsets": "17885", "endColumns": "49", "endOffsets": "17930"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\e9634510b03b6631a3bc49b0aa976cc2\\transformed\\jetified-activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "296,317", "startColumns": "4,4", "startOffsets": "18651,19738", "endColumns": "41,59", "endOffsets": "18688,19793"}}]}]}