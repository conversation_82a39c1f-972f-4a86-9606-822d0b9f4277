package com.qxyu.yucram.presentation.album;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000T\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a(\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a(\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a(\u0010\f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001aR\u0010\r\u001a\u00020\u00012\u0014\b\u0002\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\u000f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\u0012\u001a\u00020\u0013H\u0007\u001aJ\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\u000f2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a^\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u001a\u001a\u00020\n2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00030\u001c2\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\u001e\u001a\u00020\u001f2\u000e\b\u0002\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a0\u0010!\u001a\u00020\u00012\u0018\u0010\"\u001a\u0014\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010#2\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u00a8\u0006%"}, d2 = {"AlbumGridItem", "", "album", "Lcom/qxyu/yucram/domain/model/Album;", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "AlbumListError", "error", "", "onRetry", "AlbumListItem", "AlbumListScreen", "onNavigateToAlbum", "Lkotlin/Function1;", "onNavigateToCreateAlbum", "onNavigateBack", "viewModel", "Lcom/qxyu/yucram/presentation/album/AlbumListViewModel;", "AlbumListTopBar", "viewMode", "Lcom/qxyu/yucram/presentation/album/AlbumViewMode;", "onViewModeChanged", "onCreateAlbum", "AlbumSection", "title", "albums", "", "onAlbumClick", "showCreateButton", "", "onCreateClick", "CreateAlbumDialog", "onConfirm", "Lkotlin/Function2;", "onDismiss", "app_debug"})
public final class AlbumListScreenKt {
    
    /**
     * 相册列表界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AlbumListScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onNavigateToAlbum, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToCreateAlbum, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.album.AlbumListViewModel viewModel) {
    }
    
    /**
     * 相册列表顶部栏
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AlbumListTopBar(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.album.AlbumViewMode viewMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.album.AlbumViewMode, kotlin.Unit> onViewModeChanged, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCreateAlbum, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 相册分组
     */
    @androidx.compose.runtime.Composable()
    public static final void AlbumSection(@org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.Album> albums, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.album.AlbumViewMode viewMode, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onAlbumClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, boolean showCreateButton, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCreateClick) {
    }
    
    /**
     * 相册网格项
     */
    @androidx.compose.runtime.Composable()
    public static final void AlbumGridItem(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Album album, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 相册列表项
     */
    @androidx.compose.runtime.Composable()
    public static final void AlbumListItem(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Album album, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 创建相册对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void CreateAlbumDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.String, kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    /**
     * 相册列表错误状态
     */
    @androidx.compose.runtime.Composable()
    public static final void AlbumListError(@org.jetbrains.annotations.NotNull()
    java.lang.String error, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}