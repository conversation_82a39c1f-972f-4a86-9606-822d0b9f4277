{"logs": [{"outputFile": "com.qxyu.yucram.app-mergeDebugResources-74:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\90847ba8583f288576ac60503f85644e\\transformed\\core-1.12.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3304,3401,3503,3602,3702,3809,3915,9382", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "3396,3498,3597,3697,3804,3910,4031,9478"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\df3071bcb830d4a3b4d94201c338cea1\\transformed\\jetified-ui-release\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,380,479,565,644,741,832,919,991,1060,1145,1235,1311,1387,1459", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "195,278,375,474,560,639,736,827,914,986,1055,1140,1230,1306,1382,1454,1576"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4036,4131,7940,8037,8218,8381,8460,8557,8648,8735,8807,8876,8961,9223,9580,9656,9728", "endColumns": "94,82,96,98,85,78,96,90,86,71,68,84,89,75,75,71,121", "endOffsets": "4126,4209,8032,8131,8299,8455,8552,8643,8730,8802,8871,8956,9046,9294,9651,9723,9845"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\e6ffc869bf3a530da82c75f86303dcbf\\transformed\\jetified-material3-1.1.2\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,288,407,523,600,695,808,945,1059,1200,1280,1378,1469,1565,1676,1799,1902,2043,2183,2325,2513,2647,2764,2885,3007,3099,3192,3313,3447,3547,3654,3755,3896,4043,4149,4249,4331,4408,4494,4577,4674,4750,4830,4927,5029,5118,5214,5298,5406,5503,5603,5718,5794,5894", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "167,283,402,518,595,690,803,940,1054,1195,1275,1373,1464,1560,1671,1794,1897,2038,2178,2320,2508,2642,2759,2880,3002,3094,3187,3308,3442,3542,3649,3750,3891,4038,4144,4244,4326,4403,4489,4572,4669,4745,4825,4922,5024,5113,5209,5293,5401,5498,5598,5713,5789,5889,5981"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2836,2953,3069,3188,4214,4291,4386,4499,4636,4750,4891,4971,5069,5160,5256,5367,5490,5593,5734,5874,6016,6204,6338,6455,6576,6698,6790,6883,7004,7138,7238,7345,7446,7587,7734,7840,8136,8304,9051,9299,9483,9850,9926,10006,10103,10205,10294,10390,10474,10582,10679,10779,10894,10970,11070", "endColumns": "116,115,118,115,76,94,112,136,113,140,79,97,90,95,110,122,102,140,139,141,187,133,116,120,121,91,92,120,133,99,106,100,140,146,105,99,81,76,85,82,96,75,79,96,101,88,95,83,107,96,99,114,75,99,91", "endOffsets": "2948,3064,3183,3299,4286,4381,4494,4631,4745,4886,4966,5064,5155,5251,5362,5485,5588,5729,5869,6011,6199,6333,6450,6571,6693,6785,6878,6999,7133,7233,7340,7441,7582,7729,7835,7935,8213,8376,9132,9377,9575,9921,10001,10098,10200,10289,10385,10469,10577,10674,10774,10889,10965,11065,11157"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\b687dbcc02c458c0942b5ddaef0cb144\\transformed\\appcompat-1.6.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,2836", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,2917"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,734,819,899,991,1085,1182,1276,1375,1469,1565,1660,1752,1844,1929,2036,2147,2249,2357,2465,2572,2737,9137", "endColumns": "107,105,106,88,100,117,84,79,91,93,96,93,98,93,95,94,91,91,84,106,110,101,107,107,106,164,98,85", "endOffsets": "208,314,421,510,611,729,814,894,986,1080,1177,1271,1370,1464,1560,1655,1747,1839,1924,2031,2142,2244,2352,2460,2567,2732,2831,9218"}}]}]}