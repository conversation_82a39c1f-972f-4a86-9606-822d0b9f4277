package com.qxyu.yucram.data.local.dao;

/**
 * 相册照片关联DAO
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0007\u001a\u00020\u00032\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00050\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\u000b\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001c\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\t2\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170\t0\u00162\u0006\u0010\u0014\u001a\u00020\u0010H\'J\u001e\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u001aJ\u0016\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u001e\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u001aJ$\u0010\u001d\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u00102\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00100\tH\u00a7@\u00a2\u0006\u0002\u0010\u001fJ&\u0010 \u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u00102\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010!\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010\"\u00a8\u0006#"}, d2 = {"Lcom/qxyu/yucram/data/local/dao/AlbumPhotoDao;", "", "addPhotoToAlbum", "", "albumPhoto", "Lcom/qxyu/yucram/domain/model/AlbumPhoto;", "(Lcom/qxyu/yucram/domain/model/AlbumPhoto;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addPhotosToAlbum", "albumPhotos", "", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cleanupDeletedPhotoAssociations", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAlbumsForPhoto", "Lcom/qxyu/yucram/domain/model/Album;", "photoId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPhotoCountInAlbum", "", "albumId", "getPhotosInAlbum", "Lkotlinx/coroutines/flow/Flow;", "Lcom/qxyu/yucram/domain/model/Photo;", "isPhotoInAlbum", "", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeAllPhotosFromAlbum", "removePhotoFromAlbum", "removePhotosFromAlbum", "photoIds", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePhotoOrder", "order", "(Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface AlbumPhotoDao {
    
    /**
     * 添加照片到相册
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addPhotoToAlbum(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.AlbumPhoto albumPhoto, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 批量添加照片到相册
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addPhotosToAlbum(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.AlbumPhoto> albumPhotos, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 从相册移除照片
     */
    @androidx.room.Query(value = "DELETE FROM album_photos WHERE albumId = :albumId AND photoId = :photoId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removePhotoFromAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 批量从相册移除照片
     */
    @androidx.room.Query(value = "DELETE FROM album_photos WHERE albumId = :albumId AND photoId IN (:photoIds)")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removePhotosFromAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> photoIds, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取相册中的照片
     */
    @androidx.room.Query(value = "\n        SELECT p.* FROM photos p\n        INNER JOIN album_photos ap ON p.id = ap.photoId\n        WHERE ap.albumId = :albumId AND p.isDeleted = 0\n        ORDER BY ap.order ASC, ap.dateAdded DESC\n    ")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotosInAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId);
    
    /**
     * 获取照片所属的相册
     */
    @androidx.room.Query(value = "\n        SELECT a.* FROM albums a\n        INNER JOIN album_photos ap ON a.id = ap.albumId\n        WHERE ap.photoId = :photoId\n        ORDER BY a.dateModified DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlbumsForPhoto(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.qxyu.yucram.domain.model.Album>> $completion);
    
    /**
     * 获取相册照片数量
     */
    @androidx.room.Query(value = "\n        SELECT COUNT(*) FROM album_photos ap\n        INNER JOIN photos p ON ap.photoId = p.id\n        WHERE ap.albumId = :albumId AND p.isDeleted = 0\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoCountInAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 检查照片是否在相册中
     */
    @androidx.room.Query(value = "SELECT COUNT(*) > 0 FROM album_photos WHERE albumId = :albumId AND photoId = :photoId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isPhotoInAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * 更新照片在相册中的顺序
     */
    @androidx.room.Query(value = "UPDATE album_photos SET `order` = :order WHERE albumId = :albumId AND photoId = :photoId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePhotoOrder(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    java.lang.String photoId, int order, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 清理已删除照片的关联
     */
    @androidx.room.Query(value = "\n        DELETE FROM album_photos \n        WHERE photoId IN (\n            SELECT id FROM photos WHERE isDeleted = 1\n        )\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object cleanupDeletedPhotoAssociations(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 删除相册的所有照片关联
     */
    @androidx.room.Query(value = "DELETE FROM album_photos WHERE albumId = :albumId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removeAllPhotosFromAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}