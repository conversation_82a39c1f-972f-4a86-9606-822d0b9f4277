package com.qxyu.yucram.domain.model

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp

/**
 * 边框设置
 */
data class BorderSettings(
    val isEnabled: Boolean = false,
    val borderType: BorderType = BorderType.SOLID,
    val borderStyle: BorderStyle = BorderStyle.CLASSIC,
    val width: Float = 20f,                    // 边框宽度 (dp)
    val color: Color = Color.White,            // 边框颜色
    val cornerRadius: Float = 0f,              // 圆角半径 (dp)
    val shadowEnabled: Boolean = false,        // 是否启用阴影
    val shadowColor: Color = Color.Black.copy(alpha = 0.3f),
    val shadowBlur: Float = 8f,                // 阴影模糊半径
    val shadowOffset: Pair<Float, Float> = 0f to 4f, // 阴影偏移 (x, y)
    
    // 渐变边框设置
    val gradientColors: List<Color> = listOf(Color.White, Color.Gray),
    val gradientAngle: Float = 0f,             // 渐变角度
    
    // 图案边框设置
    val patternType: BorderPattern = BorderPattern.NONE,
    val patternColor: Color = Color.Black,
    val patternSize: Float = 4f,               // 图案大小
    
    // 装饰边框设置
    val decorativeStyle: DecorativeStyle = DecorativeStyle.NONE,
    val decorativeColor: Color = Color(0xFFFFD700)
)

/**
 * 边框类型
 */
enum class BorderType {
    SOLID,          // 纯色边框
    GRADIENT,       // 渐变边框
    PATTERN,        // 图案边框
    DECORATIVE      // 装饰边框
}

/**
 * 边框样式
 */
enum class BorderStyle {
    CLASSIC,        // 经典样式
    MODERN,         // 现代样式
    VINTAGE,        // 复古样式
    ARTISTIC,       // 艺术样式
    POLAROID,       // 宝丽来样式
    FILM,           // 胶片样式
    DIGITAL,        // 数字样式
    ELEGANT         // 优雅样式
}

/**
 * 边框图案
 */
enum class BorderPattern {
    NONE,           // 无图案
    DOTS,           // 圆点
    LINES,          // 线条
    DASHES,         // 虚线
    WAVES,          // 波浪
    ZIGZAG,         // 锯齿
    HEARTS,         // 爱心
    STARS,          // 星星
    FLOWERS,        // 花朵
    GEOMETRIC       // 几何图案
}

/**
 * 装饰样式
 */
enum class DecorativeStyle {
    NONE,           // 无装饰
    CORNER_ORNAMENTS, // 角落装饰
    EDGE_ORNAMENTS,   // 边缘装饰
    FULL_FRAME,       // 全框装饰
    VINTAGE_CORNERS,  // 复古角落
    FLORAL_BORDER,    // 花卉边框
    GEOMETRIC_FRAME,  // 几何框架
    ARTISTIC_FRAME    // 艺术框架
}

/**
 * 水印设置
 */
data class WatermarkSettings(
    val isEnabled: Boolean = false,
    val watermarkType: WatermarkType = WatermarkType.TEXT,
    
    // 文字水印设置
    val text: String = "",
    val fontSize: TextUnit = 16.sp,
    val fontWeight: FontWeight = FontWeight.Normal,
    val textColor: Color = Color.White,
    val textShadowEnabled: Boolean = true,
    val textShadowColor: Color = Color.Black.copy(alpha = 0.5f),
    val textShadowOffset: Pair<Float, Float> = 1f to 1f,
    val textShadowBlur: Float = 2f,
    
    // 图片水印设置
    val imagePath: String = "",                // 水印图片路径
    val imageSize: Float = 100f,               // 图片大小 (dp)
    val imageOpacity: Float = 0.8f,            // 图片不透明度
    
    // Logo水印设置
    val logoType: LogoType = LogoType.YUCRAM,
    val logoSize: Float = 80f,                 // Logo大小 (dp)
    val logoOpacity: Float = 0.7f,             // Logo不透明度
    val logoColor: Color = Color.White,        // Logo颜色
    
    // 位置设置
    val position: WatermarkPosition = WatermarkPosition.BOTTOM_RIGHT,
    val customX: Float = 0f,                   // 自定义X位置 (0-1)
    val customY: Float = 0f,                   // 自定义Y位置 (0-1)
    val margin: Float = 16f,                   // 边距 (dp)
    
    // 通用设置
    val opacity: Float = 0.8f,                 // 整体不透明度
    val rotation: Float = 0f,                  // 旋转角度
    val scale: Float = 1f,                     // 缩放比例
    val blendMode: WatermarkBlendMode = WatermarkBlendMode.NORMAL
)

/**
 * 水印类型
 */
enum class WatermarkType {
    TEXT,           // 文字水印
    IMAGE,          // 图片水印
    LOGO,           // Logo水印
    SIGNATURE,      // 签名水印
    TIMESTAMP,      // 时间戳水印
    LOCATION,       // 位置水印
    CAMERA_INFO,    // 相机信息水印
    CUSTOM          // 自定义水印
}

/**
 * Logo类型
 */
enum class LogoType {
    YUCRAM,         // Yucram Logo
    CAMERA,         // 相机图标
    LENS,           // 镜头图标
    APERTURE,       // 光圈图标
    CUSTOM          // 自定义Logo
}

/**
 * 水印位置
 */
enum class WatermarkPosition {
    TOP_LEFT,       // 左上角
    TOP_CENTER,     // 上方中央
    TOP_RIGHT,      // 右上角
    CENTER_LEFT,    // 左侧中央
    CENTER,         // 中央
    CENTER_RIGHT,   // 右侧中央
    BOTTOM_LEFT,    // 左下角
    BOTTOM_CENTER,  // 下方中央
    BOTTOM_RIGHT,   // 右下角
    CUSTOM          // 自定义位置
}

/**
 * 水印混合模式
 */
enum class WatermarkBlendMode {
    NORMAL,         // 正常
    MULTIPLY,       // 正片叠底
    SCREEN,         // 滤色
    OVERLAY,        // 叠加
    SOFT_LIGHT,     // 柔光
    HARD_LIGHT,     // 强光
    COLOR_DODGE,    // 颜色减淡
    COLOR_BURN,     // 颜色加深
    DIFFERENCE,     // 差值
    EXCLUSION       // 排除
}

/**
 * 边框和水印组合设置
 */
data class BorderWatermarkSettings(
    val borderSettings: BorderSettings = BorderSettings(),
    val watermarkSettings: WatermarkSettings = WatermarkSettings(),
    val previewEnabled: Boolean = true,        // 是否启用预览
    val exportQuality: Float = 1f,             // 导出质量 (0-1)
    val exportFormat: ExportFormat = ExportFormat.JPEG
)

/**
 * 导出格式
 */
enum class ExportFormat {
    JPEG,           // JPEG格式
    PNG,            // PNG格式
    WEBP            // WebP格式
}

/**
 * 预设边框样式
 */
data class BorderPreset(
    val id: String,
    val name: String,
    val description: String,
    val thumbnail: String? = null,
    val settings: BorderSettings,
    val category: BorderCategory = BorderCategory.CLASSIC,
    val isBuiltIn: Boolean = true
)

/**
 * 预设水印样式
 */
data class WatermarkPreset(
    val id: String,
    val name: String,
    val description: String,
    val thumbnail: String? = null,
    val settings: WatermarkSettings,
    val category: WatermarkCategory = WatermarkCategory.TEXT,
    val isBuiltIn: Boolean = true
)

/**
 * 边框分类
 */
enum class BorderCategory {
    CLASSIC,        // 经典
    MODERN,         // 现代
    VINTAGE,        // 复古
    ARTISTIC,       // 艺术
    DECORATIVE,     // 装饰
    MINIMAL,        // 极简
    COLORFUL        // 彩色
}

/**
 * 水印分类
 */
enum class WatermarkCategory {
    TEXT,           // 文字
    LOGO,           // Logo
    SIGNATURE,      // 签名
    INFO,           // 信息
    ARTISTIC,       // 艺术
    BRAND           // 品牌
}
