# 🎞️ Yucram 胶片模拟系统 API 接口文档

## 📋 模块概述

胶片模拟系统提供专业级的色彩处理和胶片效果模拟，支持实时预览、LUT处理、RAW/LOG色彩管道等功能。

## 🎯 核心接口

### 1. 胶片模拟管理器 (FilmSimulationManager)

```kotlin
interface FilmSimulationManager {
    
    // 胶片预设管理
    suspend fun getAvailableFilms(): List<FilmPreset>
    suspend fun applyFilmPreset(presetId: String): Result<Unit>
    suspend fun getCurrentFilm(): FilmPreset?
    
    // 实时预览
    fun enableRealtimePreview(enabled: Boolean)
    fun isRealtimePreviewEnabled(): Boolean
    
    // 自定义调整
    suspend fun adjustExposure(value: Float): Result<Unit> // -2.0 ~ +2.0
    suspend fun adjustContrast(value: Float): Result<Unit> // -1.0 ~ +1.0
    suspend fun adjustSaturation(value: Float): Result<Unit> // -1.0 ~ +1.0
    suspend fun adjustHighlights(value: Float): Result<Unit> // -1.0 ~ +1.0
    suspend fun adjustShadows(value: Float): Result<Unit> // -1.0 ~ +1.0
    suspend fun adjustTemperature(value: Float): Result<Unit> // -1.0 ~ +1.0
    suspend fun adjustTint(value: Float): Result<Unit> // -1.0 ~ +1.0
    
    // 重置和保存
    suspend fun resetToDefault(): Result<Unit>
    suspend fun saveAsCustomPreset(name: String): Result<FilmPreset>
}
```

### 2. LUT处理器 (LutProcessor)

```kotlin
interface LutProcessor {
    
    // LUT文件管理
    suspend fun loadLutFile(filePath: String): Result<LutData>
    suspend fun getInstalledLuts(): List<LutInfo>
    suspend fun installLutFromAssets(assetPath: String): Result<Unit>
    
    // LUT应用
    suspend fun applyLut(lutId: String, intensity: Float = 1.0f): Result<Unit>
    suspend fun removeLut(): Result<Unit>
    
    // LUT信息
    fun getLutInfo(lutId: String): LutInfo?
    fun isLutApplied(): Boolean
}
```

### 3. 色彩处理器 (ColorProcessor)

```kotlin
interface ColorProcessor {
    
    // RAW处理
    suspend fun processRawImage(rawData: ByteArray): Result<ProcessedImage>
    suspend fun setRawProcessingParams(params: RawProcessingParams): Result<Unit>
    
    // LOG处理
    suspend fun processLogImage(logData: ByteArray): Result<ProcessedImage>
    suspend fun setLogProcessingParams(params: LogProcessingParams): Result<Unit>
    
    // 色彩空间转换
    suspend fun convertColorSpace(
        image: ProcessedImage, 
        targetSpace: ColorSpace
    ): Result<ProcessedImage>
    
    // 色调映射
    suspend fun applyToneMapping(
        image: ProcessedImage, 
        params: ToneMappingParams
    ): Result<ProcessedImage>
}
```

### 4. 胶片预设服务 (FilmPresetService)

```kotlin
interface FilmPresetService {
    
    // 预设管理
    suspend fun createPreset(preset: FilmPreset): Result<String>
    suspend fun updatePreset(presetId: String, preset: FilmPreset): Result<Unit>
    suspend fun deletePreset(presetId: String): Result<Unit>
    suspend fun getPreset(presetId: String): Result<FilmPreset>
    suspend fun getAllPresets(): Result<List<FilmPreset>>
    
    // 预设分类
    suspend fun getPresetsByCategory(category: FilmCategory): Result<List<FilmPreset>>
    suspend fun searchPresets(query: String): Result<List<FilmPreset>>
    
    // 导入导出
    suspend fun exportPreset(presetId: String): Result<String> // JSON
    suspend fun importPreset(jsonData: String): Result<FilmPreset>
}
```

## 📊 数据模型

### FilmPreset (胶片预设)

```kotlin
data class FilmPreset(
    val id: String,
    val name: String,
    val description: String,
    val category: FilmCategory,
    val thumbnailUrl: String?,
    val isBuiltIn: Boolean,
    val parameters: FilmParameters,
    val lutPath: String?,
    val createdAt: Long,
    val updatedAt: Long
)

data class FilmParameters(
    val exposure: Float = 0.0f,        // -2.0 ~ +2.0
    val contrast: Float = 0.0f,        // -1.0 ~ +1.0
    val saturation: Float = 0.0f,      // -1.0 ~ +1.0
    val highlights: Float = 0.0f,      // -1.0 ~ +1.0
    val shadows: Float = 0.0f,         // -1.0 ~ +1.0
    val temperature: Float = 0.0f,     // -1.0 ~ +1.0 (冷暖)
    val tint: Float = 0.0f,           // -1.0 ~ +1.0 (品红绿)
    val vibrance: Float = 0.0f,       // -1.0 ~ +1.0
    val clarity: Float = 0.0f,        // -1.0 ~ +1.0
    val dehaze: Float = 0.0f,         // 0.0 ~ 1.0
    val vignette: Float = 0.0f        // 0.0 ~ 1.0
)

enum class FilmCategory {
    CLASSIC,      // 经典胶片
    VINTAGE,      // 复古胶片
    MODERN,       // 现代胶片
    BLACK_WHITE,  // 黑白胶片
    CINEMATIC,    // 电影胶片
    PORTRAIT,     // 人像胶片
    LANDSCAPE,    // 风景胶片
    CUSTOM        // 自定义
}
```

### LutData (LUT数据)

```kotlin
data class LutData(
    val id: String,
    val name: String,
    val size: Int,              // 17, 33, 65 等
    val format: LutFormat,
    val data: FloatArray,
    val filePath: String
)

data class LutInfo(
    val id: String,
    val name: String,
    val description: String,
    val size: Int,
    val format: LutFormat,
    val fileSize: Long,
    val checksum: String
)

enum class LutFormat {
    CUBE,           // .cube 文件
    THREE_DL,       // .3dl 文件
    LUT,            // .lut 文件
    PNG_LUT         // PNG格式的LUT
}
```

### 处理参数

```kotlin
data class RawProcessingParams(
    val whiteBalance: WhiteBalanceParams,
    val exposure: ExposureParams,
    val toneMapping: ToneMappingParams,
    val colorGrading: ColorGradingParams,
    val noiseReduction: NoiseReductionParams,
    val sharpening: SharpeningParams
)

data class LogProcessingParams(
    val logCurve: LogCurve,
    val colorSpace: ColorSpace,
    val gammaCorrection: Float,
    val colorGrading: ColorGradingParams
)

enum class ColorSpace {
    SRGB,
    ADOBE_RGB,
    PROPHOTO_RGB,
    REC2020,
    DCI_P3
}

enum class LogCurve {
    LOG_C,
    S_LOG2,
    S_LOG3,
    V_LOG,
    CUSTOM
}
```

## 🎮 使用示例

### 基础使用

```kotlin
// 获取胶片模拟管理器
val filmManager = FilmSimulationManager.getInstance()

// 获取可用胶片
val films = filmManager.getAvailableFilms()

// 应用胶片预设
filmManager.applyFilmPreset("kodak_portra_400")

// 启用实时预览
filmManager.enableRealtimePreview(true)

// 调整参数
filmManager.adjustExposure(0.5f)
filmManager.adjustContrast(0.2f)
```

### LUT处理

```kotlin
// 获取LUT处理器
val lutProcessor = LutProcessor.getInstance()

// 加载LUT文件
val lutResult = lutProcessor.loadLutFile("/path/to/lut.cube")

// 应用LUT
lutProcessor.applyLut("cinematic_lut_01", intensity = 0.8f)
```

### 自定义预设

```kotlin
// 创建自定义预设
val customPreset = FilmPreset(
    id = "custom_001",
    name = "我的风格",
    description = "个人定制胶片风格",
    category = FilmCategory.CUSTOM,
    parameters = FilmParameters(
        exposure = 0.3f,
        contrast = 0.1f,
        saturation = -0.2f
    )
)

// 保存预设
val presetService = FilmPresetService.getInstance()
presetService.createPreset(customPreset)
```

## 🔧 配置选项

### 性能配置

```kotlin
data class FilmSimulationConfig(
    val enableRealtimePreview: Boolean = true,
    val previewQuality: PreviewQuality = PreviewQuality.HIGH,
    val maxCacheSize: Long = 100 * 1024 * 1024, // 100MB
    val enableGpuAcceleration: Boolean = true,
    val threadPoolSize: Int = 4
)

enum class PreviewQuality {
    LOW,     // 快速预览
    MEDIUM,  // 平衡模式
    HIGH,    // 高质量预览
    ULTRA    // 最高质量
}
```

### 初始化配置

```kotlin
// 初始化胶片模拟系统
FilmSimulationSystem.initialize(
    context = applicationContext,
    config = FilmSimulationConfig(
        enableRealtimePreview = true,
        previewQuality = PreviewQuality.HIGH,
        enableGpuAcceleration = true
    )
)
```

## 📱 UI组件接口

### 胶片选择器

```kotlin
@Composable
fun FilmSelector(
    selectedFilm: FilmPreset?,
    onFilmSelected: (FilmPreset) -> Unit,
    modifier: Modifier = Modifier
)

@Composable
fun FilmParameterSlider(
    parameter: FilmParameter,
    value: Float,
    onValueChange: (Float) -> Unit,
    modifier: Modifier = Modifier
)

@Composable
fun FilmPreviewGrid(
    films: List<FilmPreset>,
    selectedFilm: FilmPreset?,
    onFilmClick: (FilmPreset) -> Unit,
    modifier: Modifier = Modifier
)
```

## 🚀 高级功能

### 批量处理

```kotlin
interface BatchProcessor {
    suspend fun processImages(
        images: List<String>,
        preset: FilmPreset,
        outputDir: String
    ): Result<List<String>>
    
    fun getProcessingProgress(): Flow<ProcessingProgress>
    suspend fun cancelProcessing(): Result<Unit>
}
```

### 实时预览优化

```kotlin
interface PreviewOptimizer {
    fun setPreviewSize(width: Int, height: Int)
    fun setPreviewQuality(quality: PreviewQuality)
    fun enableSmartCaching(enabled: Boolean)
    fun clearPreviewCache()
}
```

---

## 📋 接口调用清单

### 🎯 核心功能
- ✅ `FilmSimulationManager` - 胶片模拟核心管理
- ✅ `LutProcessor` - LUT文件处理
- ✅ `ColorProcessor` - 色彩处理管道
- ✅ `FilmPresetService` - 预设管理服务

### 🎨 UI组件
- ✅ `FilmSelector` - 胶片选择器
- ✅ `FilmParameterSlider` - 参数调节滑块
- ✅ `FilmPreviewGrid` - 预设预览网格

### ⚡ 高级功能
- ✅ `BatchProcessor` - 批量处理
- ✅ `PreviewOptimizer` - 预览优化

这个接口文档为胶片模拟系统提供了完整的API规范，您可以高效地调用这些接口来实现各种胶片效果和色彩处理功能。
