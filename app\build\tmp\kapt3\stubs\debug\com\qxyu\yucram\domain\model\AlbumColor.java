package com.qxyu.yucram.domain.model;

/**
 * 相册主题色
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0018\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0019\u0010\u0002\u001a\u00020\u0003\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016j\u0002\b\u0017j\u0002\b\u0018j\u0002\b\u0019j\u0002\b\u001a\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006\u001b"}, d2 = {"Lcom/qxyu/yucram/domain/model/AlbumColor;", "", "color", "Landroidx/compose/ui/graphics/Color;", "(Ljava/lang/String;IJ)V", "getColor-0d7_KjU", "()J", "J", "RED", "PINK", "PURPLE", "DEEP_PURPLE", "INDIGO", "BLUE", "LIGHT_BLUE", "CYAN", "TEAL", "GREEN", "LIGHT_GREEN", "LIME", "YELLOW", "AMBER", "ORANGE", "DEEP_ORANGE", "BROWN", "GREY", "BLUE_GREY", "app_debug"})
public enum AlbumColor {
    /*public static final*/ RED /* = new RED(0L) */,
    /*public static final*/ PINK /* = new PINK(0L) */,
    /*public static final*/ PURPLE /* = new PURPLE(0L) */,
    /*public static final*/ DEEP_PURPLE /* = new DEEP_PURPLE(0L) */,
    /*public static final*/ INDIGO /* = new INDIGO(0L) */,
    /*public static final*/ BLUE /* = new BLUE(0L) */,
    /*public static final*/ LIGHT_BLUE /* = new LIGHT_BLUE(0L) */,
    /*public static final*/ CYAN /* = new CYAN(0L) */,
    /*public static final*/ TEAL /* = new TEAL(0L) */,
    /*public static final*/ GREEN /* = new GREEN(0L) */,
    /*public static final*/ LIGHT_GREEN /* = new LIGHT_GREEN(0L) */,
    /*public static final*/ LIME /* = new LIME(0L) */,
    /*public static final*/ YELLOW /* = new YELLOW(0L) */,
    /*public static final*/ AMBER /* = new AMBER(0L) */,
    /*public static final*/ ORANGE /* = new ORANGE(0L) */,
    /*public static final*/ DEEP_ORANGE /* = new DEEP_ORANGE(0L) */,
    /*public static final*/ BROWN /* = new BROWN(0L) */,
    /*public static final*/ GREY /* = new GREY(0L) */,
    /*public static final*/ BLUE_GREY /* = new BLUE_GREY(0L) */;
    private final long color = 0L;
    
    AlbumColor(long color) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.AlbumColor> getEntries() {
        return null;
    }
}