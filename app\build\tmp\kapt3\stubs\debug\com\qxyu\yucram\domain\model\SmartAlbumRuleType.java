package com.qxyu.yucram.domain.model;

/**
 * 智能相册规则类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0013\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015\u00a8\u0006\u0016"}, d2 = {"Lcom/qxyu/yucram/domain/model/SmartAlbumRuleType;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "HAS_TAG", "DATE_RANGE", "LOCATION", "CAMERA_MODEL", "IS_FAVORITE", "IS_YUCRAM_PHOTO", "HAS_FILTER", "FILE_SIZE", "FILTER_USED", "ORIENTATION", "TAGS", "FAVORITES", "YUCRAM_PHOTOS", "FACE_COUNT", "SCENE_TYPE", "app_debug"})
public enum SmartAlbumRuleType {
    /*public static final*/ HAS_TAG /* = new HAS_TAG(null) */,
    /*public static final*/ DATE_RANGE /* = new DATE_RANGE(null) */,
    /*public static final*/ LOCATION /* = new LOCATION(null) */,
    /*public static final*/ CAMERA_MODEL /* = new CAMERA_MODEL(null) */,
    /*public static final*/ IS_FAVORITE /* = new IS_FAVORITE(null) */,
    /*public static final*/ IS_YUCRAM_PHOTO /* = new IS_YUCRAM_PHOTO(null) */,
    /*public static final*/ HAS_FILTER /* = new HAS_FILTER(null) */,
    /*public static final*/ FILE_SIZE /* = new FILE_SIZE(null) */,
    /*public static final*/ FILTER_USED /* = new FILTER_USED(null) */,
    /*public static final*/ ORIENTATION /* = new ORIENTATION(null) */,
    /*public static final*/ TAGS /* = new TAGS(null) */,
    /*public static final*/ FAVORITES /* = new FAVORITES(null) */,
    /*public static final*/ YUCRAM_PHOTOS /* = new YUCRAM_PHOTOS(null) */,
    /*public static final*/ FACE_COUNT /* = new FACE_COUNT(null) */,
    /*public static final*/ SCENE_TYPE /* = new SCENE_TYPE(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    
    SmartAlbumRuleType(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.SmartAlbumRuleType> getEntries() {
        return null;
    }
}