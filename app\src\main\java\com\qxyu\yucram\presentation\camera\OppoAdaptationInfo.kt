package com.qxyu.yucram.presentation.camera

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.outlined.Cancel
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.utils.DeviceInfo
import com.qxyu.yucram.utils.ProprietaryFormat

/**
 * OPPO设备适配信息显示组件
 */
@Composable
fun OppoAdaptationInfo(
    deviceInfo: DeviceInfo,
    proprietaryFormats: List<ProprietaryFormat>,
    modifier: Modifier = Modifier
) {
    if (!deviceInfo.isOppoDevice) return
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (deviceInfo.isOppoFindX8Ultra) 
                MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.1f)
            else MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 设备信息标题
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Filled.Info,
                    contentDescription = "设备信息",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                Text(
                    text = "OPPO设备适配",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 设备型号
            DeviceInfoRow(
                label = "设备型号",
                value = "${deviceInfo.brand} ${deviceInfo.model}"
            )
            
            // Find X8 Ultra特殊标识
            if (deviceInfo.isOppoFindX8Ultra) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Filled.CheckCircle,
                        contentDescription = "支持",
                        tint = Color(0xFF4CAF50),
                        modifier = Modifier.size(16.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(8.dp))
                    
                    Text(
                        text = "Find X8 Ultra 已识别",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color(0xFF4CAF50),
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // 专有格式支持
            if (proprietaryFormats.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "专有格式支持",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                proprietaryFormats.forEach { format ->
                    ProprietaryFormatRow(format = format)
                    Spacer(modifier = Modifier.height(4.dp))
                }
            }
            
            // 适配说明
            if (deviceInfo.isOppoFindX8Ultra) {
                Spacer(modifier = Modifier.height(12.dp))
                
                Text(
                    text = "注意：JPG MAX和RAW MAX格式需要OPPO官方SDK支持，当前使用标准Camera2 API实现高质量拍摄。",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                    lineHeight = MaterialTheme.typography.bodySmall.lineHeight
                )
            }
        }
    }
}

/**
 * 设备信息行
 */
@Composable
private fun DeviceInfoRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
        )
        
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 专有格式行
 */
@Composable
private fun ProprietaryFormatRow(
    format: ProprietaryFormat,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = if (format.isSupported) Icons.Filled.CheckCircle else Icons.Outlined.Cancel,
            contentDescription = if (format.isSupported) "支持" else "不支持",
            tint = if (format.isSupported) Color(0xFF4CAF50) else Color(0xFFFF5722),
            modifier = Modifier.size(16.dp)
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = format.name,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = format.description,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f)
            )
        }
        
        Text(
            text = if (format.isSupported) "可能支持" else "需要官方SDK",
            style = MaterialTheme.typography.labelSmall,
            color = if (format.isSupported) Color(0xFF4CAF50) else Color(0xFFFF5722)
        )
    }
}
