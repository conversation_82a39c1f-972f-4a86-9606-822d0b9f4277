{"logs": [{"outputFile": "com.qxyu.yucram.app-mergeDebugResources-74:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\b687dbcc02c458c0942b5ddaef0cb144\\transformed\\appcompat-1.6.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,17529", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,17710"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\e6ffc869bf3a530da82c75f86303dcbf\\transformed\\jetified-material3-1.1.2\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,272,485,695,907,1088,1281,1493,1728,1943,2178,2361,2559,2748,2943,3161,3385,3588,3814,4035,4262,4518,4736,4951,5174,5398,5589,5786,6002,6223,6418,6619,6820,7041,7278,7483,7677,7851,8030,8216,8401,8602,8781,8964,9163,9363,9562,9760,9947,10152,10351,10550,10765,10942,11140", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "267,480,690,902,1083,1276,1488,1723,1938,2173,2356,2554,2743,2938,3156,3380,3583,3809,4030,4257,4513,4731,4946,5169,5393,5584,5781,5997,6218,6413,6614,6815,7036,7273,7478,7672,7846,8025,8211,8396,8597,8776,8959,9158,9358,9557,9755,9942,10147,10346,10545,10760,10937,11135,11329"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5528,5745,5958,6168,8183,8364,8557,8769,9004,9219,9454,9637,9835,10024,10219,10437,10661,10864,11090,11311,11538,11794,12012,12227,12450,12674,12865,13062,13278,13499,13694,13895,14096,14317,14554,14759,15352,15713,17343,17881,18270,19055,19234,19417,19616,19816,20015,20213,20400,20605,20804,21003,21218,21395,21593", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "5740,5953,6163,6375,8359,8552,8764,8999,9214,9449,9632,9830,10019,10214,10432,10656,10859,11085,11306,11533,11789,12007,12222,12445,12669,12860,13057,13273,13494,13689,13890,14091,14312,14549,14754,14948,15521,15887,17524,18061,18466,19229,19412,19611,19811,20010,20208,20395,20600,20799,20998,21213,21390,21588,21782"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\90847ba8583f288576ac60503f85644e\\transformed\\core-1.12.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6380,6576,6781,6982,7183,7390,7595,18066", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "6571,6776,6977,7178,7385,7590,7802,18265"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\df3071bcb830d4a3b4d94201c338cea1\\transformed\\jetified-ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,1985,2152,2333,2518,2684,2863,3030", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1980,2147,2328,2513,2679,2858,3025,3263"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7807,7998,14953,15150,15526,15892,16077,16270,16458,16645,16810,16977,17158,17715,18471,18650,18817", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "7993,8178,15145,15347,15708,16072,16265,16453,16640,16805,16972,17153,17338,17876,18645,18812,19050"}}]}]}