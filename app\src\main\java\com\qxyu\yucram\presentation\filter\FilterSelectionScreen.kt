package com.qxyu.yucram.presentation.filter

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.qxyu.yucram.domain.model.*

/**
 * 滤镜选择界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterSelectionScreen(
    onFilterSelected: (FilterPreset) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: FilterSelectionViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val filters by viewModel.filters.collectAsState()
    
    LaunchedEffect(Unit) {
        viewModel.loadFilters()
    }
    
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.surface,
        contentColor = MaterialTheme.colorScheme.onSurface
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题栏
            FilterSelectionHeader(
                onDismiss = onDismiss,
                onImportClick = { /* TODO: 导入LUT */ }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 滤镜网格
            FilterPresetGrid(
                filters = filters,
                selectedFilter = uiState.selectedFilter,
                onFilterClick = { filter ->
                    viewModel.selectFilter(filter)
                    onFilterSelected(filter)
                },
                isLoading = uiState.isLoading,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(400.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 后处理参数调节区域
            uiState.selectedFilter?.let { filter ->
                PostProcessingControls(
                    params = filter.postProcessingParams,
                    onParameterChanged = viewModel::updatePostProcessingParam,
                    modifier = Modifier.fillMaxWidth()
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 滤镜选择标题栏
 */
@Composable
private fun FilterSelectionHeader(
    onDismiss: () -> Unit,
    onImportClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "滤镜选择",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold
        )
        
        Row {
            IconButton(onClick = onImportClick) {
                Icon(
                    imageVector = Icons.Filled.Add,
                    contentDescription = "导入LUT"
                )
            }
            
            IconButton(onClick = onDismiss) {
                Icon(
                    imageVector = Icons.Filled.Close,
                    contentDescription = "关闭"
                )
            }
        }
    }
}

/**
 * 滤镜预设网格
 */
@Composable
private fun FilterPresetGrid(
    filters: List<FilterPreset>,
    selectedFilter: FilterPreset?,
    onFilterClick: (FilterPreset) -> Unit,
    isLoading: Boolean,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(12.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                contentPadding = PaddingValues(4.dp)
            ) {
                items(filters) { filter ->
                    FilterPresetCard(
                        filter = filter,
                        isSelected = filter.id == selectedFilter?.id,
                        onClick = { onFilterClick(filter) }
                    )
                }
            }
        }
    }
}

/**
 * 滤镜预设卡片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun FilterPresetCard(
    filter: FilterPreset,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 0.95f else 1.0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow
        ),
        label = "filter_card_scale"
    )
    
    val borderColor by animateColorAsState(
        targetValue = if (isSelected) {
            MaterialTheme.colorScheme.primary
        } else {
            Color.Transparent
        },
        animationSpec = tween(200),
        label = "filter_card_border"
    )
    
    Card(
        onClick = onClick,
        modifier = modifier
            .aspectRatio(1.0f)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            },
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        border = androidx.compose.foundation.BorderStroke(
            width = if (isSelected) 2.dp else 0.dp,
            color = borderColor
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(8.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // 预览区域
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .clip(RoundedCornerShape(6.dp))
                    .background(MaterialTheme.colorScheme.surface),
                contentAlignment = Alignment.Center
            ) {
                // TODO: 显示滤镜预览图或图标
                Icon(
                    imageVector = Icons.Filled.PhotoFilter,
                    contentDescription = null,
                    modifier = Modifier.size(24.dp),
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // 滤镜名称
            Text(
                text = filter.name,
                style = MaterialTheme.typography.labelSmall,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 后处理参数控制
 */
@Composable
private fun PostProcessingControls(
    params: PostProcessingParams,
    onParameterChanged: (String, Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            text = "后处理调节",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        // 主要参数滑块
        PostProcessingSlider(
            label = "高光",
            value = params.highlights,
            valueRange = -1.0f..1.0f,
            onValueChange = { onParameterChanged("highlights", it) }
        )
        
        PostProcessingSlider(
            label = "阴影",
            value = params.shadows,
            valueRange = -1.0f..1.0f,
            onValueChange = { onParameterChanged("shadows", it) }
        )
        
        PostProcessingSlider(
            label = "暗角",
            value = params.vignette,
            valueRange = 0.0f..1.0f,
            onValueChange = { onParameterChanged("vignette", it) }
        )
        
        PostProcessingSlider(
            label = "色散",
            value = params.chromaAberration,
            valueRange = 0.0f..1.0f,
            onValueChange = { onParameterChanged("chromaAberration", it) }
        )
        
        PostProcessingSlider(
            label = "颗粒",
            value = params.grain,
            valueRange = 0.0f..1.0f,
            onValueChange = { onParameterChanged("grain", it) }
        )
        
        PostProcessingSlider(
            label = "锐度",
            value = params.sharpness,
            valueRange = -1.0f..1.0f,
            onValueChange = { onParameterChanged("sharpness", it) }
        )
    }
}

/**
 * 后处理参数滑块
 */
@Composable
private fun PostProcessingSlider(
    label: String,
    value: Float,
    valueRange: ClosedFloatingPointRange<Float>,
    onValueChange: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier.padding(vertical = 4.dp)) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = label,
                style = MaterialTheme.typography.bodyMedium
            )
            
            Text(
                text = String.format("%.1f", value),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Slider(
            value = value,
            onValueChange = onValueChange,
            valueRange = valueRange,
            modifier = Modifier.fillMaxWidth()
        )
    }
}
