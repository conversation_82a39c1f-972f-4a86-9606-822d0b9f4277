package com.qxyu.yucram.domain.model;

/**
 * LUT混合模式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0014\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013j\u0002\b\u0014j\u0002\b\u0015j\u0002\b\u0016\u00a8\u0006\u0017"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTBlendMode;", "", "displayName", "", "(Ljava/lang/String;ILjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "NORMAL", "MULTIPLY", "SCREEN", "OVERLAY", "SOFT_LIGHT", "HARD_LIGHT", "COLOR_DODGE", "COLOR_BURN", "DARKEN", "LIGHTEN", "DIFFERENCE", "EXCLUSION", "HUE", "SATURATION", "COLOR", "LUMINOSITY", "app_debug"})
public enum LUTBlendMode {
    /*public static final*/ NORMAL /* = new NORMAL(null) */,
    /*public static final*/ MULTIPLY /* = new MULTIPLY(null) */,
    /*public static final*/ SCREEN /* = new SCREEN(null) */,
    /*public static final*/ OVERLAY /* = new OVERLAY(null) */,
    /*public static final*/ SOFT_LIGHT /* = new SOFT_LIGHT(null) */,
    /*public static final*/ HARD_LIGHT /* = new HARD_LIGHT(null) */,
    /*public static final*/ COLOR_DODGE /* = new COLOR_DODGE(null) */,
    /*public static final*/ COLOR_BURN /* = new COLOR_BURN(null) */,
    /*public static final*/ DARKEN /* = new DARKEN(null) */,
    /*public static final*/ LIGHTEN /* = new LIGHTEN(null) */,
    /*public static final*/ DIFFERENCE /* = new DIFFERENCE(null) */,
    /*public static final*/ EXCLUSION /* = new EXCLUSION(null) */,
    /*public static final*/ HUE /* = new HUE(null) */,
    /*public static final*/ SATURATION /* = new SATURATION(null) */,
    /*public static final*/ COLOR /* = new COLOR(null) */,
    /*public static final*/ LUMINOSITY /* = new LUMINOSITY(null) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    
    LUTBlendMode(java.lang.String displayName) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.LUTBlendMode> getEntries() {
        return null;
    }
}