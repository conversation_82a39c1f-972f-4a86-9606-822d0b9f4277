package com.qxyu.yucram.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.qxyu.yucram.domain.model.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Room数据库类型转换器
 */
class Converters {
    
    private val gson = Gson()
    
    // ========== LocalDateTime转换 ==========
    
    @TypeConverter
    fun fromLocalDateTime(dateTime: LocalDateTime?): String? {
        return dateTime?.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
    }
    
    @TypeConverter
    fun toLocalDateTime(dateTimeString: String?): LocalDateTime? {
        return dateTimeString?.let {
            LocalDateTime.parse(it, DateTimeFormatter.ISO_LOCAL_DATE_TIME)
        }
    }
    
    // ========== List<String>转换 ==========
    
    @TypeConverter
    fun fromStringList(value: List<String>?): String? {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringList(value: String?): List<String> {
        return if (value.isNullOrEmpty()) {
            emptyList()
        } else {
            try {
                val listType = object : TypeToken<List<String>>() {}.type
                gson.fromJson(value, listType) ?: emptyList()
            } catch (e: Exception) {
                emptyList()
            }
        }
    }
    
    // ========== PhotoLocation转换 ==========
    
    @TypeConverter
    fun fromPhotoLocation(location: PhotoLocation?): String? {
        return location?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toPhotoLocation(locationString: String?): PhotoLocation? {
        return locationString?.let {
            try {
                gson.fromJson(it, PhotoLocation::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // ========== ExifData转换 ==========
    
    @TypeConverter
    fun fromExifData(exifData: ExifData?): String? {
        return exifData?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toExifData(exifDataString: String?): ExifData? {
        return exifDataString?.let {
            try {
                gson.fromJson(it, ExifData::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // ========== ProcessingInfo转换 ==========
    
    @TypeConverter
    fun fromProcessingInfo(processingInfo: ProcessingInfo?): String? {
        return processingInfo?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toProcessingInfo(processingInfoString: String?): ProcessingInfo? {
        return processingInfoString?.let {
            try {
                gson.fromJson(it, ProcessingInfo::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // ========== ImageSourceFormat转换 ==========
    
    @TypeConverter
    fun fromImageSourceFormat(format: ImageSourceFormat?): String? {
        return format?.name
    }
    
    @TypeConverter
    fun toImageSourceFormat(formatString: String?): ImageSourceFormat? {
        return formatString?.let {
            try {
                ImageSourceFormat.valueOf(it)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // ========== PhotoAdjustments转换 ==========
    
    @TypeConverter
    fun fromPhotoAdjustments(adjustments: PhotoAdjustments?): String? {
        return adjustments?.let { gson.toJson(it) }
    }
    
    @TypeConverter
    fun toPhotoAdjustments(adjustmentsString: String?): PhotoAdjustments? {
        return adjustmentsString?.let {
            try {
                gson.fromJson(it, PhotoAdjustments::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // ========== AlbumType转换 ==========
    
    @TypeConverter
    fun fromAlbumType(albumType: AlbumType?): String? {
        return albumType?.name
    }
    
    @TypeConverter
    fun toAlbumType(albumTypeString: String?): AlbumType? {
        return albumTypeString?.let {
            try {
                AlbumType.valueOf(it)
            } catch (e: Exception) {
                AlbumType.USER_CREATED
            }
        }
    }
    
    // ========== AlbumColor转换 ==========
    
    @TypeConverter
    fun fromAlbumColor(color: AlbumColor?): String? {
        return color?.name
    }
    
    @TypeConverter
    fun toAlbumColor(colorString: String?): AlbumColor? {
        return colorString?.let {
            try {
                AlbumColor.valueOf(it)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    // ========== PhotoSortOrder转换 ==========
    
    @TypeConverter
    fun fromPhotoSortOrder(sortOrder: PhotoSortOrder?): String? {
        return sortOrder?.name
    }
    
    @TypeConverter
    fun toPhotoSortOrder(sortOrderString: String?): PhotoSortOrder? {
        return sortOrderString?.let {
            try {
                PhotoSortOrder.valueOf(it)
            } catch (e: Exception) {
                PhotoSortOrder.DATE_TAKEN_DESC
            }
        }
    }
    
    // ========== SmartAlbumRuleType转换 ==========
    
    @TypeConverter
    fun fromSmartAlbumRuleType(ruleType: SmartAlbumRuleType?): String? {
        return ruleType?.name
    }
    
    @TypeConverter
    fun toSmartAlbumRuleType(ruleTypeString: String?): SmartAlbumRuleType? {
        return ruleTypeString?.let {
            try {
                SmartAlbumRuleType.valueOf(it)
            } catch (e: Exception) {
                null
            }
        }
    }
}
