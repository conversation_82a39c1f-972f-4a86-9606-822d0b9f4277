package com.qxyu.yucram.domain.model;

/**
 * 裁剪比例
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\r\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011\u00a8\u0006\u0012"}, d2 = {"Lcom/qxyu/yucram/domain/model/CropRatio;", "", "ratio", "", "displayName", "", "(Ljava/lang/String;IFLjava/lang/String;)V", "getDisplayName", "()Ljava/lang/String;", "getRatio", "()F", "ORIGINAL", "SQUARE", "RATIO_4_3", "RATIO_3_2", "RATIO_16_9", "RATIO_21_9", "CUSTOM", "app_debug"})
public enum CropRatio {
    /*public static final*/ ORIGINAL /* = new ORIGINAL(0.0F, null) */,
    /*public static final*/ SQUARE /* = new SQUARE(0.0F, null) */,
    /*public static final*/ RATIO_4_3 /* = new RATIO_4_3(0.0F, null) */,
    /*public static final*/ RATIO_3_2 /* = new RATIO_3_2(0.0F, null) */,
    /*public static final*/ RATIO_16_9 /* = new RATIO_16_9(0.0F, null) */,
    /*public static final*/ RATIO_21_9 /* = new RATIO_21_9(0.0F, null) */,
    /*public static final*/ CUSTOM /* = new CUSTOM(0.0F, null) */;
    private final float ratio = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    
    CropRatio(float ratio, java.lang.String displayName) {
    }
    
    public final float getRatio() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.CropRatio> getEntries() {
        return null;
    }
}