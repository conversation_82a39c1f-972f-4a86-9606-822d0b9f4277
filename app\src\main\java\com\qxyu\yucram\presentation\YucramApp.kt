package com.qxyu.yucram.presentation

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.compose.rememberNavController
import com.qxyu.yucram.camera.CameraManager
import com.qxyu.yucram.navigation.YucramNavigation
import com.qxyu.yucram.presentation.components.BottomNavigationBar
import com.qxyu.yucram.presentation.components.NavigationRail
import com.qxyu.yucram.ui.theme.YucramTheme
import com.qxyu.yucram.presentation.utils.*

/**
 * Yucram应用主容器
 */
@Composable
fun YucramApp(
    cameraManager: CameraManager,
    modifier: Modifier = Modifier
) {
    val navController = rememberNavController()

    YucramTheme {
        ResponsiveLayout(modifier = modifier) { layoutInfo ->
            when {
                // 大屏幕横屏：使用导航抽屉
                layoutInfo.isExpanded && layoutInfo.isLandscape -> {
                    // TODO: 实现导航抽屉布局
                    CompactLayout(navController, cameraManager)
                }

                // 中等屏幕横屏：使用导航栏
                layoutInfo.isMedium && layoutInfo.isLandscape -> {
                    MediumLayout(navController, cameraManager)
                }

                // 其他情况：使用底部导航
                else -> {
                    CompactLayout(navController, cameraManager)
                }
            }
        }
    }
}

/**
 * 紧凑布局（手机）- 全屏沉浸式
 */
@Composable
private fun CompactLayout(
    navController: androidx.navigation.NavHostController,
    cameraManager: CameraManager
) {
    // 全屏沉浸式布局，无底部导航栏
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        YucramNavigation(
            navController = navController,
            cameraManager = cameraManager
        )
    }
}

/**
 * 中等布局（平板横屏）
 */
@Composable
private fun MediumLayout(
    navController: androidx.navigation.NavHostController,
    cameraManager: CameraManager
) {
    Row(modifier = Modifier.fillMaxSize()) {
        // 导航栏
        NavigationRail(navController = navController)

        // 主内容
        YucramNavigation(
            navController = navController,
            cameraManager = cameraManager
        )
    }
}

/**
 * 应用状态管理
 */
@Composable
fun rememberYucramAppState(
    navController: androidx.navigation.NavHostController = rememberNavController()
) = remember(navController) {
    YucramAppState(navController)
}

/**
 * 应用状态类
 */
class YucramAppState(
    val navController: androidx.navigation.NavHostController
) {
    /**
     * 导航到指定路由
     */
    fun navigateTo(route: String) {
        navController.navigate(route) {
            launchSingleTop = true
        }
    }
    
    /**
     * 返回上一页
     */
    fun navigateBack() {
        navController.popBackStack()
    }
    
    /**
     * 导航到相机页面
     */
    fun navigateToCamera() {
        navigateTo(com.qxyu.yucram.navigation.YucramDestinations.CAMERA_ROUTE)
    }
    
    /**
     * 导航到图库页面
     */
    fun navigateToGallery() {
        navigateTo(com.qxyu.yucram.navigation.YucramDestinations.GALLERY_ROUTE)
    }
    
    /**
     * 导航到设置页面
     */
    fun navigateToSettings() {
        navigateTo(com.qxyu.yucram.navigation.YucramDestinations.SETTINGS_ROUTE)
    }
    
    /**
     * 导航到照片详情
     */
    fun navigateToPhotoDetail(photoId: String) {
        navigateTo("photo_detail/$photoId")
    }
    
    /**
     * 导航到照片编辑
     */
    fun navigateToPhotoEdit(photoId: String) {
        navigateTo("photo_edit/$photoId")
    }
}
