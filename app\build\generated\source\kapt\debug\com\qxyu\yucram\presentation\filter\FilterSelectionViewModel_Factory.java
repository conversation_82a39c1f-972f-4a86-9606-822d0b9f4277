// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.filter;

import com.qxyu.yucram.filter.DeviceCapabilityDetector;
import com.qxyu.yucram.filter.LutFileManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSelectionViewModel_Factory implements Factory<FilterSelectionViewModel> {
  private final Provider<LutFileManager> lutFileManagerProvider;

  private final Provider<DeviceCapabilityDetector> deviceCapabilityDetectorProvider;

  public FilterSelectionViewModel_Factory(Provider<LutFileManager> lutFileManagerProvider,
      Provider<DeviceCapabilityDetector> deviceCapabilityDetectorProvider) {
    this.lutFileManagerProvider = lutFileManagerProvider;
    this.deviceCapabilityDetectorProvider = deviceCapabilityDetectorProvider;
  }

  @Override
  public FilterSelectionViewModel get() {
    return newInstance(lutFileManagerProvider.get(), deviceCapabilityDetectorProvider.get());
  }

  public static FilterSelectionViewModel_Factory create(
      Provider<LutFileManager> lutFileManagerProvider,
      Provider<DeviceCapabilityDetector> deviceCapabilityDetectorProvider) {
    return new FilterSelectionViewModel_Factory(lutFileManagerProvider, deviceCapabilityDetectorProvider);
  }

  public static FilterSelectionViewModel newInstance(LutFileManager lutFileManager,
      DeviceCapabilityDetector deviceCapabilityDetector) {
    return new FilterSelectionViewModel(lutFileManager, deviceCapabilityDetector);
  }
}
