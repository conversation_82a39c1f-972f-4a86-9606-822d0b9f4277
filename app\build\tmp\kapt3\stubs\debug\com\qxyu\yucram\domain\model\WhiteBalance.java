package com.qxyu.yucram.domain.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/qxyu/yucram/domain/model/WhiteBalance;", "", "(Ljava/lang/String;I)V", "AUTO", "DAYLIGHT", "CLOUDY", "TUNGSTEN", "FLUORESCENT", "MANUAL", "app_debug"})
public enum WhiteBalance {
    /*public static final*/ AUTO /* = new AUTO() */,
    /*public static final*/ DAYLIGHT /* = new DAYLIGHT() */,
    /*public static final*/ CLOUDY /* = new CLOUDY() */,
    /*public static final*/ TUNGSTEN /* = new TUNGSTEN() */,
    /*public static final*/ FLUORESCENT /* = new FLUORESCENT() */,
    /*public static final*/ MANUAL /* = new MANUAL() */;
    
    WhiteBalance() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.WhiteBalance> getEntries() {
        return null;
    }
}