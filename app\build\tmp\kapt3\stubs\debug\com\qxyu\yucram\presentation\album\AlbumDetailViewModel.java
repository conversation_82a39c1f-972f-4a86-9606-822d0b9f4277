package com.qxyu.yucram.presentation.album;

/**
 * 相册详情ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0019\u001a\u00020\u001a2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\tJ\u0006\u0010\u001d\u001a\u00020\u001aJ\u0006\u0010\u001e\u001a\u00020\u001aJ\u0006\u0010\u001f\u001a\u00020\u001aJ\u0006\u0010 \u001a\u00020\u001aJ\u0006\u0010!\u001a\u00020\u001aJ\u0012\u0010\"\u001a\u00020\u001a2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u001cJ\u000e\u0010$\u001a\u00020\u001a2\u0006\u0010%\u001a\u00020\u001cJ\u0014\u0010&\u001a\u00020\u001a2\f\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\tJ\u0006\u0010\'\u001a\u00020\u001aJ\u000e\u0010(\u001a\u00020\u001a2\u0006\u0010#\u001a\u00020\u001cJ\u000e\u0010)\u001a\u00020\u001a2\u0006\u0010#\u001a\u00020\u001cJ \u0010*\u001a\u00020\u001a2\u0006\u0010+\u001a\u00020\u001c2\u0006\u0010,\u001a\u00020\u001c2\b\u0010-\u001a\u0004\u0018\u00010.R\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\f0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012\u00a8\u0006/"}, d2 = {"Lcom/qxyu/yucram/presentation/album/AlbumDetailViewModel;", "Landroidx/lifecycle/ViewModel;", "albumRepository", "Lcom/qxyu/yucram/domain/repository/AlbumRepository;", "(Lcom/qxyu/yucram/domain/repository/AlbumRepository;)V", "_album", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/domain/model/Album;", "_photos", "", "Lcom/qxyu/yucram/domain/model/Photo;", "_selectionState", "Lcom/qxyu/yucram/domain/model/PhotoSelectionState;", "_uiState", "Lcom/qxyu/yucram/presentation/album/AlbumDetailUiState;", "album", "Lkotlinx/coroutines/flow/StateFlow;", "getAlbum", "()Lkotlinx/coroutines/flow/StateFlow;", "photos", "getPhotos", "selectionState", "getSelectionState", "uiState", "getUiState", "addPhotosToAlbum", "", "photoIds", "", "clearError", "clearMessage", "clearSelection", "deleteAlbum", "deleteSelectedPhotos", "enterSelectionMode", "photoId", "loadAlbum", "albumId", "removePhotosFromAlbum", "selectAllPhotos", "togglePhotoFavorite", "togglePhotoSelection", "updateAlbum", "name", "description", "color", "Lcom/qxyu/yucram/domain/model/AlbumColor;", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AlbumDetailViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.AlbumRepository albumRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.album.AlbumDetailUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.album.AlbumDetailUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.Album> _album = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Album> album = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> _photos = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> photos = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.PhotoSelectionState> _selectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.PhotoSelectionState> selectionState = null;
    
    @javax.inject.Inject()
    public AlbumDetailViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.AlbumRepository albumRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.album.AlbumDetailUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Album> getAlbum() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotos() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.PhotoSelectionState> getSelectionState() {
        return null;
    }
    
    public final void loadAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId) {
    }
    
    public final void updateAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.AlbumColor color) {
    }
    
    public final void deleteAlbum() {
    }
    
    public final void addPhotosToAlbum(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> photoIds) {
    }
    
    public final void removePhotosFromAlbum(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> photoIds) {
    }
    
    public final void enterSelectionMode(@org.jetbrains.annotations.Nullable()
    java.lang.String photoId) {
    }
    
    public final void togglePhotoSelection(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void selectAllPhotos() {
    }
    
    public final void clearSelection() {
    }
    
    public final void deleteSelectedPhotos() {
    }
    
    public final void togglePhotoFavorite(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}