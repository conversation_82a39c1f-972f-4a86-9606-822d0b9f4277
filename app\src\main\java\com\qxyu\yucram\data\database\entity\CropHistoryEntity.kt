package com.qxyu.yucram.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.qxyu.yucram.domain.model.*

/**
 * 裁剪历史记录数据库实体
 */
@Entity(tableName = "crop_history")
data class CropHistoryEntity(
    @PrimaryKey
    val id: String,
    val photoId: String,
    val timestamp: Long,
    val description: String,
    
    // 裁剪设置
    val cropLeft: Float,
    val cropTop: Float,
    val cropRight: Float,
    val cropBottom: Float,
    val aspectRatio: String,
    val customAspectRatio: Float?,
    val showGrid: Boolean,
    val gridType: String,
    val cropMode: String,
    val maintainAspectRatio: Boolean,
    val snapToGrid: Boolean,
    val gridOpacity: Float,
    val backgroundColor: Int,
    val enableSmartGuides: Boolean,
    val autoCenter: Boolean,
    
    val canUndo: Boolean
) {
    companion object {
        fun fromDomainModel(history: CropHistory): CropHistoryEntity {
            return CropHistoryEntity(
                id = history.id,
                photoId = history.photoId,
                timestamp = history.timestamp,
                description = history.description,
                cropLeft = history.cropSettings.cropRect.left,
                cropTop = history.cropSettings.cropRect.top,
                cropRight = history.cropSettings.cropRect.right,
                cropBottom = history.cropSettings.cropRect.bottom,
                aspectRatio = history.cropSettings.aspectRatio.name,
                customAspectRatio = history.cropSettings.customAspectRatio,
                showGrid = history.cropSettings.showGrid,
                gridType = history.cropSettings.gridType.name,
                cropMode = history.cropSettings.cropMode.name,
                maintainAspectRatio = history.cropSettings.maintainAspectRatio,
                snapToGrid = history.cropSettings.snapToGrid,
                gridOpacity = history.cropSettings.gridOpacity,
                backgroundColor = history.cropSettings.backgroundColor,
                enableSmartGuides = history.cropSettings.enableSmartGuides,
                autoCenter = history.cropSettings.autoCenter,
                canUndo = history.canUndo
            )
        }
    }
    
    fun toDomainModel(): CropHistory {
        return CropHistory(
            id = id,
            photoId = photoId,
            timestamp = timestamp,
            cropSettings = CropSettings(
                cropRect = CropRect(cropLeft, cropTop, cropRight, cropBottom),
                aspectRatio = AspectRatio.valueOf(aspectRatio),
                customAspectRatio = customAspectRatio,
                showGrid = showGrid,
                gridType = GridType.valueOf(gridType),
                cropMode = CropMode.valueOf(cropMode),
                maintainAspectRatio = maintainAspectRatio,
                snapToGrid = snapToGrid,
                gridOpacity = gridOpacity,
                backgroundColor = backgroundColor,
                enableSmartGuides = enableSmartGuides,
                autoCenter = autoCenter
            ),
            description = description,
            canUndo = canUndo
        )
    }
}
