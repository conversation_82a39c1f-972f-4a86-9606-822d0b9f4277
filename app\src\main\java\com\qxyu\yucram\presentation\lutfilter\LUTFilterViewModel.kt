package com.qxyu.yucram.presentation.lutfilter

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.domain.repository.LUTFilterRepository
import com.qxyu.yucram.domain.repository.PhotoRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * LUT滤镜ViewModel
 */
@HiltViewModel
class LUTFilterViewModel @Inject constructor(
    private val photoRepository: PhotoRepository,
    private val lutFilterRepository: LutFilterRepository
) : ViewModel() {
    
    // ========== UI状态 ==========
    
    private val _uiState = MutableStateFlow(LutFilterUiState())
    val uiState: StateFlow<LutFilterUiState> = _uiState.asStateFlow()
    
    private val _photo = MutableStateFlow<Photo?>(null)
    val photo: StateFlow<Photo?> = _photo.asStateFlow()
    
    private val _selectedFilter = MutableStateFlow<LutFilter?>(null)
    val selectedFilter: StateFlow<LutFilter?> = _selectedFilter.asStateFlow()

    private val _filterSettings = MutableStateFlow(LutFilterSettings())
    val filterSettings: StateFlow<LutFilterSettings> = _filterSettings.asStateFlow()

    private val _currentCategory = MutableStateFlow<LutCategory?>(null)
    val currentCategory: StateFlow<LutCategory?> = _currentCategory.asStateFlow()
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()
    
    // ========== 滤镜数据 ==========
    
    private val _allFilters = MutableStateFlow<List<LutFilter>>(emptyList())

    val filters: StateFlow<List<LutFilter>> = combine(
        _allFilters,
        _currentCategory,
        _searchQuery
    ) { allFilters, category, query ->
        var filteredList = allFilters
        
        // 按分类筛选
        if (category != null) {
            filteredList = filteredList.filter { it.category == category }
        }
        
        // 按搜索关键词筛选
        if (query.isNotBlank()) {
            filteredList = filteredList.filter { filter ->
                filter.displayName.contains(query, ignoreCase = true) ||
                filter.description.contains(query, ignoreCase = true) ||
                filter.tags.any { tag -> tag.contains(query, ignoreCase = true) }
            }
        }
        
        filteredList
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    // ========== 原始设置 ==========
    
    private val originalSettings = LutFilterSettings()
    
    // ========== 初始化 ==========
    
    init {
        loadBuiltInFilters()
        loadRecentFilters()
    }
    
    // ========== 照片加载 ==========
    
    fun loadPhoto(photoId: String) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, error = null) }
                
                val photo = photoRepository.getPhotoById(photoId)
                if (photo != null) {
                    _photo.value = photo
                    
                    // 加载照片的现有LUT设置（如果有）
                    loadExistingLUTSettings(photo)
                    
                    _uiState.update { it.copy(isLoading = false) }
                } else {
                    _uiState.update { 
                        it.copy(
                            isLoading = false,
                            error = "照片不存在"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = "加载照片失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 滤镜管理 ==========
    
    fun selectFilter(filter: LutFilter) {
        _selectedFilter.value = filter
        _filterSettings.update {
            it.copy(
                selectedFilter = filter,
                intensity = 1.0f,
                blendMode = LutBlendMode.NORMAL
            )
        }
        
        // 生成预览
        generatePreview()
    }
    
    fun resetFilter() {
        _selectedFilter.value = null
        _filterSettings.value = originalSettings
    }
    
    fun updateFilterSettings(newSettings: LutFilterSettings) {
        _filterSettings.value = newSettings
        
        // 如果启用实时预览，立即生成预览
        if (newSettings.realTimePreview) {
            generatePreview()
        }
    }
    
    fun hasChanges(): Boolean {
        return _selectedFilter.value != null || _filterSettings.value != originalSettings
    }
    
    // ========== 分类管理 ==========
    
    fun selectCategory(category: LutCategory?) {
        _currentCategory.value = category
    }
    
    fun searchFilters(query: String) {
        _searchQuery.value = query
    }
    
    // ========== 滤镜应用 ==========
    
    fun applyFilter() {
        val currentPhoto = _photo.value ?: return
        val currentFilter = _selectedFilter.value ?: return
        val currentSettings = _filterSettings.value
        
        viewModelScope.launch {
            try {
                _uiState.update { 
                    it.copy(
                        isProcessing = true,
                        processingProgress = 0f,
                        processingMessage = "正在应用滤镜..."
                    )
                }
                
                // 应用LUT滤镜
                val result = lutFilterRepository.applyFilterToPhoto(
                    photoPath = currentPhoto.filePath,
                    filter = currentFilter,
                    settings = currentSettings,
                    processingParams = LutProcessingParams(),
                    outputPath = generateOutputPath(currentPhoto, currentFilter),
                    onProgress = { progress ->
                        _uiState.update { 
                            it.copy(
                                processingProgress = progress,
                                processingMessage = "正在处理... ${(progress * 100).toInt()}%"
                            )
                        }
                    }
                )
                
                result.fold(
                    onSuccess = { outputPath ->
                        // 更新照片信息
                        val updatedPhoto = currentPhoto.copy(
                            filePath = outputPath,
                            processingInfo = currentPhoto.processingInfo?.copy(
                                lutApplied = currentFilter.name
                            )
                        )
                        
                        photoRepository.updatePhoto(updatedPhoto)
                        
                        // 添加历史记录
                        addFilterHistory(currentPhoto, currentFilter, currentSettings)
                        
                        _uiState.update { 
                            it.copy(
                                isProcessing = false,
                                isApplied = true,
                                processingMessage = "滤镜应用成功"
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(
                                isProcessing = false,
                                error = "应用滤镜失败: ${error.message}"
                            )
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isProcessing = false,
                        error = "应用滤镜失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 预览生成 ==========
    
    private fun generatePreview() {
        val currentPhoto = _photo.value ?: return
        val currentFilter = _selectedFilter.value ?: return
        val currentSettings = _filterSettings.value
        
        if (!currentSettings.previewEnabled) return
        
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isGeneratingPreview = true) }
                
                val result = lutFilterRepository.previewFilter(
                    photoPath = currentPhoto.filePath,
                    filter = currentFilter,
                    settings = currentSettings,
                    processingParams = LutProcessingParams()
                )
                
                result.fold(
                    onSuccess = { previewPath ->
                        _uiState.update { 
                            it.copy(
                                isGeneratingPreview = false,
                                previewPath = previewPath
                            )
                        }
                    },
                    onFailure = { error ->
                        _uiState.update { 
                            it.copy(
                                isGeneratingPreview = false,
                                error = "生成预览失败: ${error.message}"
                            )
                        }
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isGeneratingPreview = false,
                        error = "生成预览失败: ${e.message}"
                    )
                }
            }
        }
    }
    
    // ========== 滤镜详情 ==========
    
    fun showFilterDetails(filter: LutFilter) {
        viewModelScope.launch {
            try {
                // 获取滤镜统计信息
                val stats = lutFilterRepository.getFilterStats(filter.id)
                
                _uiState.update { 
                    it.copy(
                        selectedFilterDetails = filter,
                        selectedFilterStats = stats
                    )
                }
            } catch (e: Exception) {
                // 忽略统计信息获取失败
                _uiState.update { 
                    it.copy(selectedFilterDetails = filter)
                }
            }
        }
    }
    
    // ========== 数据加载 ==========
    
    private fun loadBuiltInFilters() {
        viewModelScope.launch {
            try {
                lutFilterRepository.getBuiltInFilters().collect { filters ->
                    _allFilters.value = filters
                }
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(error = "加载滤镜失败: ${e.message}")
                }
            }
        }
    }
    
    private fun loadRecentFilters() {
        viewModelScope.launch {
            try {
                lutFilterRepository.getRecentlyUsedFilters(5).collect { recentFilters ->
                    _uiState.update { 
                        it.copy(recentFilters = recentFilters)
                    }
                }
            } catch (e: Exception) {
                // 忽略最近使用滤镜加载失败
            }
        }
    }
    
    private fun loadExistingLUTSettings(photo: Photo) {
        // TODO: 从照片的处理信息中加载现有的LUT设置
        val lutApplied = photo.processingInfo?.lutApplied
        if (lutApplied != null) {
            viewModelScope.launch {
                try {
                    val existingFilter = _allFilters.value.find { it.name == lutApplied }
                    if (existingFilter != null) {
                        _selectedFilter.value = existingFilter
                        _filterSettings.update { 
                            it.copy(selectedFilter = existingFilter)
                        }
                    }
                } catch (e: Exception) {
                    // 忽略加载失败
                }
            }
        }
    }
    
    // ========== 辅助方法 ==========
    
    private fun generateOutputPath(photo: Photo, filter: LutFilter): String {
        val fileName = photo.fileName.substringBeforeLast(".")
        val extension = photo.fileName.substringAfterLast(".")
        return "${photo.filePath.substringBeforeLast("/")}/${fileName}_${filter.name}.$extension"
    }
    
    private suspend fun addFilterHistory(
        photo: Photo,
        filter: LutFilter,
        settings: LutFilterSettings
    ) {
        try {
            val history = LutFilterHistory(
                id = generateHistoryId(),
                photoId = photo.id,
                filterId = filter.id,
                settings = settings,
                processingParams = LutProcessingParams(),
                processingTime = 0 // TODO: 记录实际处理时间
            )
            
            lutFilterRepository.addFilterHistory(history)
        } catch (e: Exception) {
            // 忽略历史记录添加失败
        }
    }
    
    private fun generateHistoryId(): String {
        return "history_${System.currentTimeMillis()}_${(0..999).random()}"
    }
    
    // ========== 错误处理 ==========
    
    fun clearError() {
        _uiState.update { it.copy(error = null) }
    }
    
    fun clearMessage() {
        _uiState.update { it.copy(processingMessage = null) }
    }
}

/**
 * LUT滤镜UI状态
 */
data class LutFilterUiState(
    val isLoading: Boolean = false,
    val isProcessing: Boolean = false,
    val isGeneratingPreview: Boolean = false,
    val isApplied: Boolean = false,
    val processingProgress: Float = 0f,
    val processingMessage: String? = null,
    val previewPath: String? = null,
    val error: String? = null,
    val selectedFilterDetails: LutFilter? = null,
    val selectedFilterStats: LutFilterStats? = null,
    val recentFilters: List<LutFilter> = emptyList()
)
