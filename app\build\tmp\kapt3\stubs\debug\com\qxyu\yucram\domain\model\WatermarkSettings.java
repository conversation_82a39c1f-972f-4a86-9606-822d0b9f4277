package com.qxyu.yucram.domain.model;

/**
 * 水印设置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\bG\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u008b\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000f\u001a\u00020\r\u0012\u0014\b\u0002\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00120\u0011\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0018\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u001b\u001a\u00020\r\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u001d\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0012\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u0012\u0012\b\b\u0002\u0010 \u001a\u00020\u0012\u0012\b\b\u0002\u0010!\u001a\u00020\u0012\u0012\b\b\u0002\u0010\"\u001a\u00020\u0012\u0012\b\b\u0002\u0010#\u001a\u00020\u0012\u0012\b\b\u0002\u0010$\u001a\u00020%\u00a2\u0006\u0002\u0010&J\t\u0010J\u001a\u00020\u0003H\u00c6\u0003J\t\u0010K\u001a\u00020\u0012H\u00c6\u0003J\t\u0010L\u001a\u00020\u0007H\u00c6\u0003J\t\u0010M\u001a\u00020\u0012H\u00c6\u0003J\t\u0010N\u001a\u00020\u0012H\u00c6\u0003J\t\u0010O\u001a\u00020\u0018H\u00c6\u0003J\t\u0010P\u001a\u00020\u0012H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0012H\u00c6\u0003J\u0016\u0010R\u001a\u00020\rH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bS\u0010-J\t\u0010T\u001a\u00020\u001dH\u00c6\u0003J\t\u0010U\u001a\u00020\u0012H\u00c6\u0003J\t\u0010V\u001a\u00020\u0005H\u00c6\u0003J\t\u0010W\u001a\u00020\u0012H\u00c6\u0003J\t\u0010X\u001a\u00020\u0012H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0012H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0012H\u00c6\u0003J\t\u0010[\u001a\u00020\u0012H\u00c6\u0003J\t\u0010\\\u001a\u00020%H\u00c6\u0003J\t\u0010]\u001a\u00020\u0007H\u00c6\u0003J\u0016\u0010^\u001a\u00020\tH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b_\u0010-J\t\u0010`\u001a\u00020\u000bH\u00c6\u0003J\u0016\u0010a\u001a\u00020\rH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bb\u0010-J\t\u0010c\u001a\u00020\u0003H\u00c6\u0003J\u0016\u0010d\u001a\u00020\rH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\be\u0010-J\u0015\u0010f\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00120\u0011H\u00c6\u0003J\u0099\u0002\u0010g\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\r2\u0014\b\u0002\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00120\u00112\b\b\u0002\u0010\u0013\u001a\u00020\u00122\b\b\u0002\u0010\u0014\u001a\u00020\u00072\b\b\u0002\u0010\u0015\u001a\u00020\u00122\b\b\u0002\u0010\u0016\u001a\u00020\u00122\b\b\u0002\u0010\u0017\u001a\u00020\u00182\b\b\u0002\u0010\u0019\u001a\u00020\u00122\b\b\u0002\u0010\u001a\u001a\u00020\u00122\b\b\u0002\u0010\u001b\u001a\u00020\r2\b\b\u0002\u0010\u001c\u001a\u00020\u001d2\b\b\u0002\u0010\u001e\u001a\u00020\u00122\b\b\u0002\u0010\u001f\u001a\u00020\u00122\b\b\u0002\u0010 \u001a\u00020\u00122\b\b\u0002\u0010!\u001a\u00020\u00122\b\b\u0002\u0010\"\u001a\u00020\u00122\b\b\u0002\u0010#\u001a\u00020\u00122\b\b\u0002\u0010$\u001a\u00020%H\u00c6\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\bh\u0010iJ\u0013\u0010j\u001a\u00020\u00032\b\u0010k\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010l\u001a\u00020mH\u00d6\u0001J\t\u0010n\u001a\u00020\u0007H\u00d6\u0001R\u0011\u0010$\u001a\u00020%\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0011\u0010\u001e\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0011\u0010\u001f\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010*R\u0019\u0010\b\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010.\u001a\u0004\b,\u0010-R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0011\u0010\u0016\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010*R\u0011\u0010\u0014\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u00103R\u0011\u0010\u0015\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010*R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u00105R\u0019\u0010\u001b\u001a\u00020\r\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010.\u001a\u0004\b6\u0010-R\u0011\u0010\u001a\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010*R\u0011\u0010\u0019\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010*R\u0011\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0011\u0010 \u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010*R\u0011\u0010!\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010*R\u0011\u0010\u001c\u001a\u00020\u001d\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010>R\u0011\u0010\"\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010*R\u0011\u0010#\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010*R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u00103R\u0019\u0010\f\u001a\u00020\r\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010.\u001a\u0004\bB\u0010-R\u0011\u0010\u0013\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010*R\u0019\u0010\u000f\u001a\u00020\r\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010.\u001a\u0004\bD\u0010-R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u00105R\u001d\u0010\u0010\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00120\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010GR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010I\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006o"}, d2 = {"Lcom/qxyu/yucram/domain/model/WatermarkSettings;", "", "isEnabled", "", "watermarkType", "Lcom/qxyu/yucram/domain/model/WatermarkType;", "text", "", "fontSize", "Landroidx/compose/ui/unit/TextUnit;", "fontWeight", "Landroidx/compose/ui/text/font/FontWeight;", "textColor", "Landroidx/compose/ui/graphics/Color;", "textShadowEnabled", "textShadowColor", "textShadowOffset", "Lkotlin/Pair;", "", "textShadowBlur", "imagePath", "imageSize", "imageOpacity", "logoType", "Lcom/qxyu/yucram/domain/model/LogoType;", "logoSize", "logoOpacity", "logoColor", "position", "Lcom/qxyu/yucram/domain/model/WatermarkPosition;", "customX", "customY", "margin", "opacity", "rotation", "scale", "blendMode", "Lcom/qxyu/yucram/domain/model/WatermarkBlendMode;", "(ZLcom/qxyu/yucram/domain/model/WatermarkType;Ljava/lang/String;JLandroidx/compose/ui/text/font/FontWeight;JZJLkotlin/Pair;FLjava/lang/String;FFLcom/qxyu/yucram/domain/model/LogoType;FFJLcom/qxyu/yucram/domain/model/WatermarkPosition;FFFFFFLcom/qxyu/yucram/domain/model/WatermarkBlendMode;Lkotlin/jvm/internal/DefaultConstructorMarker;)V", "getBlendMode", "()Lcom/qxyu/yucram/domain/model/WatermarkBlendMode;", "getCustomX", "()F", "getCustomY", "getFontSize-XSAIIZE", "()J", "J", "getFontWeight", "()Landroidx/compose/ui/text/font/FontWeight;", "getImageOpacity", "getImagePath", "()Ljava/lang/String;", "getImageSize", "()Z", "getLogoColor-0d7_KjU", "getLogoOpacity", "getLogoSize", "getLogoType", "()Lcom/qxyu/yucram/domain/model/LogoType;", "getMargin", "getOpacity", "getPosition", "()Lcom/qxyu/yucram/domain/model/WatermarkPosition;", "getRotation", "getScale", "getText", "getTextColor-0d7_KjU", "getTextShadowBlur", "getTextShadowColor-0d7_KjU", "getTextShadowEnabled", "getTextShadowOffset", "()Lkotlin/Pair;", "getWatermarkType", "()Lcom/qxyu/yucram/domain/model/WatermarkType;", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component17-0d7_KjU", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component3", "component4", "component4-XSAIIZE", "component5", "component6", "component6-0d7_KjU", "component7", "component8", "component8-0d7_KjU", "component9", "copy", "copy-Y_N7Rng", "(ZLcom/qxyu/yucram/domain/model/WatermarkType;Ljava/lang/String;JLandroidx/compose/ui/text/font/FontWeight;JZJLkotlin/Pair;FLjava/lang/String;FFLcom/qxyu/yucram/domain/model/LogoType;FFJLcom/qxyu/yucram/domain/model/WatermarkPosition;FFFFFFLcom/qxyu/yucram/domain/model/WatermarkBlendMode;)Lcom/qxyu/yucram/domain/model/WatermarkSettings;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class WatermarkSettings {
    private final boolean isEnabled = false;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.WatermarkType watermarkType = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String text = null;
    private final long fontSize = 0L;
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.ui.text.font.FontWeight fontWeight = null;
    private final long textColor = 0L;
    private final boolean textShadowEnabled = false;
    private final long textShadowColor = 0L;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Pair<java.lang.Float, java.lang.Float> textShadowOffset = null;
    private final float textShadowBlur = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String imagePath = null;
    private final float imageSize = 0.0F;
    private final float imageOpacity = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LogoType logoType = null;
    private final float logoSize = 0.0F;
    private final float logoOpacity = 0.0F;
    private final long logoColor = 0L;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.WatermarkPosition position = null;
    private final float customX = 0.0F;
    private final float customY = 0.0F;
    private final float margin = 0.0F;
    private final float opacity = 0.0F;
    private final float rotation = 0.0F;
    private final float scale = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.WatermarkBlendMode blendMode = null;
    
    private WatermarkSettings(boolean isEnabled, com.qxyu.yucram.domain.model.WatermarkType watermarkType, java.lang.String text, long fontSize, androidx.compose.ui.text.font.FontWeight fontWeight, long textColor, boolean textShadowEnabled, long textShadowColor, kotlin.Pair<java.lang.Float, java.lang.Float> textShadowOffset, float textShadowBlur, java.lang.String imagePath, float imageSize, float imageOpacity, com.qxyu.yucram.domain.model.LogoType logoType, float logoSize, float logoOpacity, long logoColor, com.qxyu.yucram.domain.model.WatermarkPosition position, float customX, float customY, float margin, float opacity, float rotation, float scale, com.qxyu.yucram.domain.model.WatermarkBlendMode blendMode) {
        super();
    }
    
    public final boolean isEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkType getWatermarkType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.font.FontWeight getFontWeight() {
        return null;
    }
    
    public final boolean getTextShadowEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Float, java.lang.Float> getTextShadowOffset() {
        return null;
    }
    
    public final float getTextShadowBlur() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getImagePath() {
        return null;
    }
    
    public final float getImageSize() {
        return 0.0F;
    }
    
    public final float getImageOpacity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LogoType getLogoType() {
        return null;
    }
    
    public final float getLogoSize() {
        return 0.0F;
    }
    
    public final float getLogoOpacity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkPosition getPosition() {
        return null;
    }
    
    public final float getCustomX() {
        return 0.0F;
    }
    
    public final float getCustomY() {
        return 0.0F;
    }
    
    public final float getMargin() {
        return 0.0F;
    }
    
    public final float getOpacity() {
        return 0.0F;
    }
    
    public final float getRotation() {
        return 0.0F;
    }
    
    public final float getScale() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkBlendMode getBlendMode() {
        return null;
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    public final float component12() {
        return 0.0F;
    }
    
    public final float component13() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LogoType component14() {
        return null;
    }
    
    public final float component15() {
        return 0.0F;
    }
    
    public final float component16() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkPosition component18() {
        return null;
    }
    
    public final float component19() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkType component2() {
        return null;
    }
    
    public final float component20() {
        return 0.0F;
    }
    
    public final float component21() {
        return 0.0F;
    }
    
    public final float component22() {
        return 0.0F;
    }
    
    public final float component23() {
        return 0.0F;
    }
    
    public final float component24() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkBlendMode component25() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.text.font.FontWeight component5() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlin.Pair<java.lang.Float, java.lang.Float> component9() {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}