package com.qxyu.yucram.data.local.dao;

/**
 * 相册数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0005\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0018\u0010\u000b\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u001c\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00110\u00102\u0006\u0010\u0012\u001a\u00020\tH\'J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00110\u0010H\'J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00110\u0010H\'J\u000e\u0010\u0015\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0014\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00110\u0010H\'J\u0016\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0018\u001a\u00020\u00032\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00050\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u001aJ\u001c\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00110\u00102\u0006\u0010\u001c\u001a\u00020\tH\'J\u0016\u0010\u001d\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J(\u0010\u001e\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\b\u0010\u001f\u001a\u0004\u0018\u00010\t2\u0006\u0010 \u001a\u00020!H\u00a7@\u00a2\u0006\u0002\u0010\"J&\u0010#\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010$\u001a\u00020\r2\u0006\u0010 \u001a\u00020!H\u00a7@\u00a2\u0006\u0002\u0010%\u00a8\u0006&"}, d2 = {"Lcom/qxyu/yucram/data/local/dao/AlbumDao;", "", "deleteAlbum", "", "album", "Lcom/qxyu/yucram/domain/model/Album;", "(Lcom/qxyu/yucram/domain/model/Album;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAlbumById", "albumId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAlbumById", "getAlbumCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAlbumsByTag", "Lkotlinx/coroutines/flow/Flow;", "", "tag", "getAllAlbums", "getSystemAlbums", "getUserAlbumCount", "getUserAlbums", "insertAlbum", "insertAlbums", "albums", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchAlbums", "query", "updateAlbum", "updateCoverPhoto", "photoId", "modifyTime", "Ljava/time/LocalDateTime;", "(Ljava/lang/String;Ljava/lang/String;Ljava/time/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePhotoCount", "count", "(Ljava/lang/String;ILjava/time/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface AlbumDao {
    
    /**
     * 获取所有相册
     */
    @androidx.room.Query(value = "SELECT * FROM albums ORDER BY dateModified DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getAllAlbums();
    
    /**
     * 根据ID获取相册
     */
    @androidx.room.Query(value = "SELECT * FROM albums WHERE id = :albumId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlbumById(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.Album> $completion);
    
    /**
     * 获取用户创建的相册
     */
    @androidx.room.Query(value = "SELECT * FROM albums WHERE albumType = \'USER_CREATED\' ORDER BY dateModified DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getUserAlbums();
    
    /**
     * 获取系统相册
     */
    @androidx.room.Query(value = "SELECT * FROM albums WHERE isSystemAlbum = 1 ORDER BY albumType")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getSystemAlbums();
    
    /**
     * 插入相册
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAlbum(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Album album, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 批量插入相册
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAlbums(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.Album> albums, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 更新相册
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAlbum(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Album album, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 更新相册照片数量
     */
    @androidx.room.Query(value = "UPDATE albums SET photoCount = :count, dateModified = :modifyTime WHERE id = :albumId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePhotoCount(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, int count, @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime modifyTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 更新相册封面
     */
    @androidx.room.Query(value = "UPDATE albums SET coverPhotoId = :photoId, dateModified = :modifyTime WHERE id = :albumId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateCoverPhoto(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.Nullable()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    java.time.LocalDateTime modifyTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 删除相册
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAlbum(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Album album, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 根据ID删除相册
     */
    @androidx.room.Query(value = "DELETE FROM albums WHERE id = :albumId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAlbumById(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 搜索相册
     */
    @androidx.room.Query(value = "\n        SELECT * FROM albums \n        WHERE name LIKE \'%\' || :query || \'%\' \n        OR description LIKE \'%\' || :query || \'%\'\n        ORDER BY dateModified DESC\n    ")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> searchAlbums(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    /**
     * 根据标签搜索相册
     */
    @androidx.room.Query(value = "SELECT * FROM albums WHERE tags LIKE \'%\' || :tag || \'%\' ORDER BY dateModified DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getAlbumsByTag(@org.jetbrains.annotations.NotNull()
    java.lang.String tag);
    
    /**
     * 获取相册数量
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM albums")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlbumCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 获取用户相册数量
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM albums WHERE albumType = \'USER_CREATED\'")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserAlbumCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
}