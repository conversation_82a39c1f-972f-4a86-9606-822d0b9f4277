package com.qxyu.yucram.presentation.borderwater.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000n\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b!\n\u0002\u0018\u0002\n\u0002\b\t\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\b\u001a\u00020\u00012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\n2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u001a\u0010\f\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001aJ\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u00122\u0006\u0010\u0016\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0019\u0010\u001a\u001a.\u0010\u001b\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001fH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b \u0010!\u001a&\u0010\"\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b%\u0010&\u001a&\u0010\'\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b(\u0010&\u001a.\u0010)\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001fH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b*\u0010!\u001a.\u0010+\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010,\u001a\u00020-2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010.\u001a\u00020/H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b0\u00101\u001a.\u00102\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b4\u0010!\u001a6\u00105\u001a\u00020\u0001*\u00020\u001c2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b6\u00107\u001a&\u00108\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b9\u0010&\u001a6\u0010:\u001a\u00020\u0001*\u00020\u001c2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b;\u00107\u001a.\u0010<\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b=\u0010!\u001a&\u0010>\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b?\u0010&\u001a6\u0010@\u001a\u00020\u0001*\u00020\u001c2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bA\u00107\u001a&\u0010B\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010,\u001a\u00020-2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bC\u0010D\u001a&\u0010E\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bF\u0010&\u001a&\u0010G\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010#\u001a\u00020$2\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bH\u0010&\u001a.\u0010I\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010,\u001a\u00020-2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010.\u001a\u00020/H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bJ\u00101\u001a.\u0010K\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010,\u001a\u00020-2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010.\u001a\u00020/H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bL\u00101\u001a.\u0010M\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bN\u0010!\u001a6\u0010O\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010,\u001a\u00020-2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010P\u001a\u00020Q2\u0006\u0010.\u001a\u00020/H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bR\u0010S\u001a6\u0010T\u001a\u00020\u0001*\u00020\u001c2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bU\u00107\u001a.\u0010V\u001a\u00020\u0001*\u00020\u001c2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001fH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bW\u0010!\u001a6\u0010X\u001a\u00020\u0001*\u00020\u001c2\u0006\u00103\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u0017\u001a\u00020\u0018H\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\bY\u00107\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006Z"}, d2 = {"BorderWatermarkPreview", "", "photo", "Lcom/qxyu/yucram/domain/model/Photo;", "settings", "Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;", "modifier", "Landroidx/compose/ui/Modifier;", "PreviewControls", "onResetZoom", "Lkotlin/Function0;", "onToggleControls", "SettingsInfo", "calculateWatermarkPosition", "Landroidx/compose/ui/geometry/Offset;", "position", "Lcom/qxyu/yucram/domain/model/WatermarkPosition;", "customX", "", "customY", "margin", "watermarkWidth", "watermarkHeight", "canvasSize", "Landroidx/compose/ui/geometry/Size;", "calculateWatermarkPosition-TBoow8U", "(Lcom/qxyu/yucram/domain/model/WatermarkPosition;FFFFFJ)J", "drawApertureIcon", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "size", "color", "Landroidx/compose/ui/graphics/Color;", "drawApertureIcon-icVWoeI", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;JFJ)V", "drawBorder", "borderSettings", "Lcom/qxyu/yucram/domain/model/BorderSettings;", "drawBorder-cSwnlzA", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Lcom/qxyu/yucram/domain/model/BorderSettings;J)V", "drawBorderShadow", "drawBorderShadow-cSwnlzA", "drawCameraIcon", "drawCameraIcon-icVWoeI", "drawCameraInfoWatermark", "watermarkSettings", "Lcom/qxyu/yucram/domain/model/WatermarkSettings;", "textMeasurer", "Landroidx/compose/ui/text/TextMeasurer;", "drawCameraInfoWatermark-JM5-EMQ", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Lcom/qxyu/yucram/domain/model/WatermarkSettings;JLandroidx/compose/ui/text/TextMeasurer;)V", "drawCornerOrnaments", "borderWidth", "drawCornerOrnaments-3qE6Ttw", "drawDashPattern", "drawDashPattern-HilfTbk", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;FJFJ)V", "drawDecorativeBorder", "drawDecorativeBorder-cSwnlzA", "drawDotPattern", "drawDotPattern-HilfTbk", "drawEdgeOrnaments", "drawEdgeOrnaments-3qE6Ttw", "drawGradientBorder", "drawGradientBorder-cSwnlzA", "drawLinePattern", "drawLinePattern-HilfTbk", "drawLogoWatermark", "drawLogoWatermark-cSwnlzA", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Lcom/qxyu/yucram/domain/model/WatermarkSettings;J)V", "drawPatternBorder", "drawPatternBorder-cSwnlzA", "drawSolidBorder", "drawSolidBorder-cSwnlzA", "drawTextWatermark", "drawTextWatermark-JM5-EMQ", "drawTimestampWatermark", "drawTimestampWatermark-JM5-EMQ", "drawVintageCorners", "drawVintageCorners-3qE6Ttw", "drawWatermark", "density", "Landroidx/compose/ui/unit/Density;", "drawWatermark-QfoU1oo", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Lcom/qxyu/yucram/domain/model/WatermarkSettings;JLandroidx/compose/ui/unit/Density;Landroidx/compose/ui/text/TextMeasurer;)V", "drawWavePattern", "drawWavePattern-HilfTbk", "drawYucramLogo", "drawYucramLogo-icVWoeI", "drawZigzagPattern", "drawZigzagPattern-HilfTbk", "app_debug"})
public final class BorderWatermarkPreviewKt {
    
    /**
     * 边框和水印预览组件
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderWatermarkPreview(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Photo photo, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 预览控制栏
     */
    @androidx.compose.runtime.Composable()
    public static final void PreviewControls(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResetZoom, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onToggleControls, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 设置信息显示
     */
    @androidx.compose.runtime.Composable()
    public static final void SettingsInfo(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings settings, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}