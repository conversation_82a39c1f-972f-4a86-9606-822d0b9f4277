package com.qxyu.yucram.domain.repository;

/**
 * LUT滤镜Repository接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00a4\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0019\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J$\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0007\u0010\bJ$\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010\u000b\u001a\u00020\fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\r\u0010\u000eJ$\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0011\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0012\u0010\u0013Jf\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00150\u00032\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00040\u00152\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u00042\u0014\b\u0002\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\n0\u001dH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001f\u0010 JZ\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\"\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010#\u001a\u00020\u00042\u0014\b\u0002\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\n0\u001dH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b$\u0010%JJ\u0010&\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\"\u001a\u00020\u00042\u0006\u0010\'\u001a\u00020(2\u0006\u0010#\u001a\u00020\u00042\u0014\b\u0002\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\n0\u001dH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b)\u0010*J\"\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b,\u0010-J\u001c\u0010.\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b/\u0010-J\u001c\u00100\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b1\u0010-J$\u00102\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\'\u001a\u00020(H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b3\u00104J$\u00105\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u00106\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b7\u0010\u0013J$\u00108\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u00106\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b9\u0010\u0013J$\u0010:\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u00106\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b;\u0010\u0013J:\u0010<\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010=\u001a\u00020>2\u0014\b\u0002\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\n0\u001dH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b?\u0010@J,\u0010A\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u00106\u001a\u00020\u00042\u0006\u0010#\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bB\u0010CJ<\u0010D\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\"\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bE\u0010FJ0\u0010G\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010H\u001a\u0004\u0018\u00010\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bI\u0010JJ\u001a\u0010K\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020>0\u00150LH\u00a6@\u00a2\u0006\u0002\u0010-J\u001a\u0010M\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150LH\u00a6@\u00a2\u0006\u0002\u0010-J\u001a\u0010N\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020(0\u00150LH\u00a6@\u00a2\u0006\u0002\u0010-J\u001a\u0010O\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150LH\u00a6@\u00a2\u0006\u0002\u0010-J\u000e\u0010P\u001a\u00020QH\u00a6@\u00a2\u0006\u0002\u0010-J\u0018\u0010R\u001a\u0004\u0018\u00010\u00062\u0006\u00106\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\"\u0010S\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u00150L2\u0006\u0010T\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\u0018\u0010U\u001a\u0004\u0018\u00010>2\u0006\u00106\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\u0018\u0010V\u001a\u0004\u0018\u00010\u001e2\u0006\u0010W\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\u0018\u0010X\u001a\u0004\u0018\u00010Y2\u0006\u0010W\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J\"\u0010Z\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150L2\u0006\u0010[\u001a\u00020\\H\u00a6@\u00a2\u0006\u0002\u0010]J0\u0010^\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010_0\u00032\u0006\u0010\u0011\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b`\u0010\u0013J$\u0010a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150L2\b\b\u0002\u0010b\u001a\u00020cH\u00a6@\u00a2\u0006\u0002\u0010dJ\u001a\u0010e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150LH\u00a6@\u00a2\u0006\u0002\u0010-J\u0018\u0010f\u001a\u0004\u0018\u00010(2\u0006\u00106\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J$\u0010g\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150L2\b\b\u0002\u0010b\u001a\u00020cH\u00a6@\u00a2\u0006\u0002\u0010dJ0\u0010h\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150L2\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u00042\b\b\u0002\u0010b\u001a\u00020cH\u00a6@\u00a2\u0006\u0002\u0010iJ\u001a\u0010j\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150LH\u00a6@\u00a2\u0006\u0002\u0010-J\u0018\u0010k\u001a\u0004\u0018\u00010c2\u0006\u0010W\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0002\u0010\u0013J@\u0010l\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u0006\u0010\u0011\u001a\u00020\u00042\u0006\u0010m\u001a\u00020\u00042\u0006\u0010[\u001a\u00020\\2\n\b\u0002\u0010n\u001a\u0004\u0018\u00010\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bo\u0010pJ*\u0010q\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150\u00032\u0006\u0010r\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bs\u0010\u0013J\u001c\u0010t\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bu\u0010-J<\u0010v\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\"\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bw\u0010FJ,\u0010x\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010W\u001a\u00020\u00042\u0006\u0010y\u001a\u00020cH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\bz\u0010iJ\"\u0010{\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00150L2\u0006\u0010|\u001a\u00020}H\u00a6@\u00a2\u0006\u0002\u0010~J%\u0010\u007f\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010W\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0005\b\u0080\u0001\u0010\u0013J\u001e\u0010\u0081\u0001\u001a\b\u0012\u0004\u0012\u00020\n0\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0005\b\u0082\u0001\u0010-J&\u0010\u0083\u0001\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010r\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0005\b\u0084\u0001\u0010\u0013J&\u0010\u0085\u0001\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0005\b\u0086\u0001\u0010\bJ(\u0010\u0087\u0001\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0007\u0010\u0088\u0001\u001a\u00020YH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\b\u0089\u0001\u0010\u008a\u0001J&\u0010\u008b\u0001\u001a\b\u0012\u0004\u0012\u00020\n0\u00032\u0006\u0010\'\u001a\u00020(H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0005\b\u008c\u0001\u00104J=\u0010\u008d\u0001\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0014\b\u0002\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\n0\u001dH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0006\b\u008e\u0001\u0010\u008f\u0001J\'\u0010\u0090\u0001\u001a\t\u0012\u0005\u0012\u00030\u0091\u00010\u00032\u0006\u0010\u0011\u001a\u00020\u0004H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0005\b\u0092\u0001\u0010\u0013\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0093\u0001"}, d2 = {"Lcom/qxyu/yucram/domain/repository/LUTFilterRepository;", "", "addFilter", "Lkotlin/Result;", "", "filter", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "addFilter-gIAlu-s", "(Lcom/qxyu/yucram/domain/model/LUTFilter;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addFilterHistory", "", "history", "Lcom/qxyu/yucram/domain/model/LUTFilterHistory;", "addFilterHistory-gIAlu-s", "(Lcom/qxyu/yucram/domain/model/LUTFilterHistory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "analyzeLUTFile", "Lcom/qxyu/yucram/domain/model/LUTAnalysisResult;", "filePath", "analyzeLUTFile-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyFilterBatch", "", "photoPaths", "settings", "Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "processingParams", "Lcom/qxyu/yucram/domain/model/LUTProcessingParams;", "outputDir", "onProgress", "Lkotlin/Function1;", "", "applyFilterBatch-bMdYcbs", "(Ljava/util/List;Lcom/qxyu/yucram/domain/model/LUTFilter;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;Lcom/qxyu/yucram/domain/model/LUTProcessingParams;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyFilterToPhoto", "photoPath", "outputPath", "applyFilterToPhoto-bMdYcbs", "(Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LUTFilter;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;Lcom/qxyu/yucram/domain/model/LUTProcessingParams;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyPreset", "preset", "Lcom/qxyu/yucram/domain/model/LUTFilterPreset;", "applyPreset-yxL6bBk", "(Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LUTFilterPreset;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "checkFilterUpdates", "checkFilterUpdates-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearFilterCache", "clearFilterCache-IoAF18A", "clearFilterHistory", "clearFilterHistory-IoAF18A", "createPreset", "createPreset-gIAlu-s", "(Lcom/qxyu/yucram/domain/model/LUTFilterPreset;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFilter", "id", "deleteFilter-gIAlu-s", "deleteFilterHistory", "deleteFilterHistory-gIAlu-s", "deletePreset", "deletePreset-gIAlu-s", "downloadFilterPack", "pack", "Lcom/qxyu/yucram/domain/model/LUTFilterPack;", "downloadFilterPack-0E7RQCE", "(Lcom/qxyu/yucram/domain/model/LUTFilterPack;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "exportFilter", "exportFilter-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateBeforeAfterComparison", "generateBeforeAfterComparison-yxL6bBk", "(Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LUTFilter;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;Lcom/qxyu/yucram/domain/model/LUTProcessingParams;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateLUTThumbnail", "sampleImagePath", "generateLUTThumbnail-0E7RQCE", "(Lcom/qxyu/yucram/domain/model/LUTFilter;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllFilterPacks", "Lkotlinx/coroutines/flow/Flow;", "getAllFilters", "getAllPresets", "getBuiltInFilters", "getCacheSize", "", "getFilterById", "getFilterHistory", "photoId", "getFilterPackById", "getFilterRating", "filterId", "getFilterStats", "Lcom/qxyu/yucram/domain/model/LUTFilterStats;", "getFiltersByCategory", "category", "Lcom/qxyu/yucram/domain/model/LUTCategory;", "(Lcom/qxyu/yucram/domain/model/LUTCategory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLUTFileInfo", "", "getLUTFileInfo-gIAlu-s", "getPopularFilters", "limit", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPremiumFilters", "getPresetById", "getRecentlyUsedFilters", "getRecommendedFilters", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserCustomFilters", "getUserFilterRating", "importLUTFile", "name", "description", "importLUTFile-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LUTCategory;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "installFilterPack", "packId", "installFilterPack-gIAlu-s", "preloadPopularFilters", "preloadPopularFilters-IoAF18A", "previewFilter", "previewFilter-yxL6bBk", "rateFilter", "rating", "rateFilter-0E7RQCE", "searchFilters", "criteria", "Lcom/qxyu/yucram/domain/model/LUTFilterSearchCriteria;", "(Lcom/qxyu/yucram/domain/model/LUTFilterSearchCriteria;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "shareFilter", "shareFilter-gIAlu-s", "syncOnlineFilters", "syncOnlineFilters-IoAF18A", "uninstallFilterPack", "uninstallFilterPack-gIAlu-s", "updateFilter", "updateFilter-gIAlu-s", "updateFilterStats", "stats", "updateFilterStats-gIAlu-s", "(Lcom/qxyu/yucram/domain/model/LUTFilterStats;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updatePreset", "updatePreset-gIAlu-s", "uploadUserFilter", "uploadUserFilter-0E7RQCE", "(Lcom/qxyu/yucram/domain/model/LUTFilter;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateLUTFile", "", "validateLUTFile-gIAlu-s", "app_debug"})
public abstract interface LUTFilterRepository {
    
    /**
     * 获取所有LUT滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllFilters(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 根据ID获取LUT滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFilterById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.LUTFilter> $completion);
    
    /**
     * 根据分类获取LUT滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFiltersByCategory(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTCategory category, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 搜索LUT滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object searchFilters(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSearchCriteria criteria, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 获取内置滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBuiltInFilters(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 获取用户自定义滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserCustomFilters(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 获取付费滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPremiumFilters(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 获取所有滤镜包
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllFilterPacks(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilterPack>>> $completion);
    
    /**
     * 根据ID获取滤镜包
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFilterPackById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.LUTFilterPack> $completion);
    
    /**
     * 获取所有滤镜预设
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllPresets(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilterPreset>>> $completion);
    
    /**
     * 根据ID获取滤镜预设
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPresetById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.LUTFilterPreset> $completion);
    
    /**
     * 获取滤镜使用历史
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFilterHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilterHistory>>> $completion);
    
    /**
     * 获取滤镜统计信息
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFilterStats(@org.jetbrains.annotations.NotNull()
    java.lang.String filterId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.LUTFilterStats> $completion);
    
    /**
     * 获取热门滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPopularFilters(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 获取最近使用的滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecentlyUsedFilters(int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 获取推荐滤镜
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRecommendedFilters(@org.jetbrains.annotations.Nullable()
    java.lang.String photoPath, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlinx.coroutines.flow.Flow<? extends java.util.List<com.qxyu.yucram.domain.model.LUTFilter>>> $completion);
    
    /**
     * 获取滤镜评分
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFilterRating(@org.jetbrains.annotations.NotNull()
    java.lang.String filterId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Float> $completion);
    
    /**
     * 获取用户对滤镜的评分
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserFilterRating(@org.jetbrains.annotations.NotNull()
    java.lang.String filterId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 获取缓存大小
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCacheSize(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * LUT滤镜Repository接口
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}