package com.qxyu.yucram.presentation.borderwater.components

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab

/**
 * 边框和水印编辑面板组合
 */
@Composable
fun BorderWatermarkEditPanel(
    currentTab: BorderWatermarkTab,
    settings: BorderWatermarkSettings,
    onSettingsChanged: (BorderWatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    when (currentTab) {
        BorderWatermarkTab.BORDER -> {
            BorderEditPanel(
                borderSettings = settings.borderSettings,
                onSettingsChanged = { newBorderSettings ->
                    onSettingsChanged(settings.copy(borderSettings = newBorderSettings))
                },
                modifier = modifier
            )
        }
        
        BorderWatermarkTab.WATERMARK -> {
            WatermarkEditPanel(
                watermarkSettings = settings.watermarkSettings,
                onSettingsChanged = { newWatermarkSettings ->
                    onSettingsChanged(settings.copy(watermarkSettings = newWatermarkSettings))
                },
                modifier = modifier
            )
        }
        
        BorderWatermarkTab.BOTH -> {
            CombinedEditPanel(
                settings = settings,
                onSettingsChanged = onSettingsChanged,
                modifier = modifier
            )
        }
    }
}

/**
 * 组合编辑面板
 */
@Composable
fun CombinedEditPanel(
    settings: BorderWatermarkSettings,
    onSettingsChanged: (BorderWatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 快速开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            // 边框开关
            QuickToggleCard(
                title = "边框",
                isEnabled = settings.borderSettings.isEnabled,
                onToggle = { enabled ->
                    onSettingsChanged(
                        settings.copy(
                            borderSettings = settings.borderSettings.copy(isEnabled = enabled)
                        )
                    )
                },
                modifier = Modifier.weight(1f)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // 水印开关
            QuickToggleCard(
                title = "水印",
                isEnabled = settings.watermarkSettings.isEnabled,
                onToggle = { enabled ->
                    onSettingsChanged(
                        settings.copy(
                            watermarkSettings = settings.watermarkSettings.copy(isEnabled = enabled)
                        )
                    )
                },
                modifier = Modifier.weight(1f)
            )
        }
        
        // 快速预设
        QuickPresetSection(
            settings = settings,
            onSettingsChanged = onSettingsChanged
        )
        
        // 导出设置
        ExportSettingsSection(
            settings = settings,
            onSettingsChanged = onSettingsChanged
        )
    }
}

/**
 * 快速切换卡片
 */
@Composable
fun QuickToggleCard(
    title: String,
    isEnabled: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现快速切换卡片
}

/**
 * 快速预设区域
 */
@Composable
fun QuickPresetSection(
    settings: BorderWatermarkSettings,
    onSettingsChanged: (BorderWatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现快速预设选择
}

/**
 * 导出设置区域
 */
@Composable
fun ExportSettingsSection(
    settings: BorderWatermarkSettings,
    onSettingsChanged: (BorderWatermarkSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现导出设置
}
