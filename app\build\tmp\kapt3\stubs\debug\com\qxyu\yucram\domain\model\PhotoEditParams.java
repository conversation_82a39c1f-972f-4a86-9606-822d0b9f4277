package com.qxyu.yucram.domain.model;

/**
 * 专业照片编辑参数
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\\\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b@\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0097\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\u0003\u0012\b\b\u0002\u0010\n\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\u0003\u0012\b\b\u0002\u0010\r\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0019\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u001c\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u001e\u0012\u000e\b\u0002\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020!0 \u0012\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010#\u0012\n\b\u0002\u0010$\u001a\u0004\u0018\u00010%\u00a2\u0006\u0002\u0010&J\t\u0010J\u001a\u00020\u0003H\u00c6\u0003J\t\u0010K\u001a\u00020\u0003H\u00c6\u0003J\t\u0010L\u001a\u00020\u0003H\u00c6\u0003J\t\u0010M\u001a\u00020\u000fH\u00c6\u0003J\t\u0010N\u001a\u00020\u0011H\u00c6\u0003J\t\u0010O\u001a\u00020\u0003H\u00c6\u0003J\t\u0010P\u001a\u00020\u0003H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0003H\u00c6\u0003J\t\u0010R\u001a\u00020\u0003H\u00c6\u0003J\t\u0010S\u001a\u00020\u0003H\u00c6\u0003J\t\u0010T\u001a\u00020\u0003H\u00c6\u0003J\t\u0010U\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010V\u001a\u0004\u0018\u00010\u0019H\u00c6\u0003J\t\u0010W\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010X\u001a\u0004\u0018\u00010\u001cH\u00c6\u0003J\t\u0010Y\u001a\u00020\u001eH\u00c6\u0003J\u000f\u0010Z\u001a\b\u0012\u0004\u0012\u00020!0 H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010#H\u00c6\u0003J\u000b\u0010\\\u001a\u0004\u0018\u00010%H\u00c6\u0003J\t\u0010]\u001a\u00020\u0003H\u00c6\u0003J\t\u0010^\u001a\u00020\u0003H\u00c6\u0003J\t\u0010_\u001a\u00020\u0003H\u00c6\u0003J\t\u0010`\u001a\u00020\u0003H\u00c6\u0003J\t\u0010a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010c\u001a\u00020\u0003H\u00c6\u0003J\u009b\u0002\u0010d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0013\u001a\u00020\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u00032\b\b\u0002\u0010\u0016\u001a\u00020\u00032\b\b\u0002\u0010\u0017\u001a\u00020\u00032\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00192\b\b\u0002\u0010\u001a\u001a\u00020\u00032\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\b\b\u0002\u0010\u001d\u001a\u00020\u001e2\u000e\b\u0002\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020!0 2\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010#2\n\b\u0002\u0010$\u001a\u0004\u0018\u00010%H\u00c6\u0001J\u0013\u0010e\u001a\u00020f2\b\u0010g\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010h\u001a\u00020iH\u00d6\u0001J\t\u0010j\u001a\u00020\u0019H\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0013\u0010\"\u001a\u0004\u0018\u00010#\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010(R\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010(R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010(R\u0013\u0010\u001b\u001a\u0004\u0018\u00010\u001c\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0011\u0010\u0015\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010(R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010(R\u0013\u0010\u0018\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00105R\u0011\u0010\u0017\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010(R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010(R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u00109R\u0017\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020!0 \u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010;R\u0013\u0010$\u001a\u0004\u0018\u00010%\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010=R\u0011\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010(R\u0011\u0010\u001d\u001a\u00020\u001e\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010@R\u0011\u0010\u001a\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010(R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010(R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010(R\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u0010(R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010(R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010(R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u0010(R\u0011\u0010\u0016\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010(R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u0010(\u00a8\u0006k"}, d2 = {"Lcom/qxyu/yucram/domain/model/PhotoEditParams;", "", "exposure", "", "highlights", "shadows", "whites", "blacks", "contrast", "brightness", "vibrance", "saturation", "temperature", "tint", "hslAdjustments", "Lcom/qxyu/yucram/domain/model/HSLAdjustments;", "curves", "Lcom/qxyu/yucram/domain/model/CurveAdjustments;", "sharpness", "noiseReduction", "clarity", "dehaze", "vignette", "grain", "filmEmulation", "", "rotation", "cropRatio", "Lcom/qxyu/yucram/domain/model/CropRatio;", "perspective", "Lcom/qxyu/yucram/domain/model/PerspectiveAdjustment;", "localAdjustments", "", "Lcom/qxyu/yucram/domain/model/LocalAdjustment;", "borderWatermarkSettings", "Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;", "lutFilterSettings", "Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "(FFFFFFFFFFFLcom/qxyu/yucram/domain/model/HSLAdjustments;Lcom/qxyu/yucram/domain/model/CurveAdjustments;FFFFFFLjava/lang/String;FLcom/qxyu/yucram/domain/model/CropRatio;Lcom/qxyu/yucram/domain/model/PerspectiveAdjustment;Ljava/util/List;Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;)V", "getBlacks", "()F", "getBorderWatermarkSettings", "()Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;", "getBrightness", "getClarity", "getContrast", "getCropRatio", "()Lcom/qxyu/yucram/domain/model/CropRatio;", "getCurves", "()Lcom/qxyu/yucram/domain/model/CurveAdjustments;", "getDehaze", "getExposure", "getFilmEmulation", "()Ljava/lang/String;", "getGrain", "getHighlights", "getHslAdjustments", "()Lcom/qxyu/yucram/domain/model/HSLAdjustments;", "getLocalAdjustments", "()Ljava/util/List;", "getLutFilterSettings", "()Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "getNoiseReduction", "getPerspective", "()Lcom/qxyu/yucram/domain/model/PerspectiveAdjustment;", "getRotation", "getSaturation", "getShadows", "getSharpness", "getTemperature", "getTint", "getVibrance", "getVignette", "getWhites", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class PhotoEditParams {
    private final float exposure = 0.0F;
    private final float highlights = 0.0F;
    private final float shadows = 0.0F;
    private final float whites = 0.0F;
    private final float blacks = 0.0F;
    private final float contrast = 0.0F;
    private final float brightness = 0.0F;
    private final float vibrance = 0.0F;
    private final float saturation = 0.0F;
    private final float temperature = 0.0F;
    private final float tint = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.HSLAdjustments hslAdjustments = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.CurveAdjustments curves = null;
    private final float sharpness = 0.0F;
    private final float noiseReduction = 0.0F;
    private final float clarity = 0.0F;
    private final float dehaze = 0.0F;
    private final float vignette = 0.0F;
    private final float grain = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String filmEmulation = null;
    private final float rotation = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.CropRatio cropRatio = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.PerspectiveAdjustment perspective = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.qxyu.yucram.domain.model.LocalAdjustment> localAdjustments = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.BorderWatermarkSettings borderWatermarkSettings = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.LUTFilterSettings lutFilterSettings = null;
    
    public PhotoEditParams(float exposure, float highlights, float shadows, float whites, float blacks, float contrast, float brightness, float vibrance, float saturation, float temperature, float tint, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.HSLAdjustments hslAdjustments, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.CurveAdjustments curves, float sharpness, float noiseReduction, float clarity, float dehaze, float vignette, float grain, @org.jetbrains.annotations.Nullable()
    java.lang.String filmEmulation, float rotation, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.CropRatio cropRatio, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PerspectiveAdjustment perspective, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LocalAdjustment> localAdjustments, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings borderWatermarkSettings, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilterSettings lutFilterSettings) {
        super();
    }
    
    public final float getExposure() {
        return 0.0F;
    }
    
    public final float getHighlights() {
        return 0.0F;
    }
    
    public final float getShadows() {
        return 0.0F;
    }
    
    public final float getWhites() {
        return 0.0F;
    }
    
    public final float getBlacks() {
        return 0.0F;
    }
    
    public final float getContrast() {
        return 0.0F;
    }
    
    public final float getBrightness() {
        return 0.0F;
    }
    
    public final float getVibrance() {
        return 0.0F;
    }
    
    public final float getSaturation() {
        return 0.0F;
    }
    
    public final float getTemperature() {
        return 0.0F;
    }
    
    public final float getTint() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.HSLAdjustments getHslAdjustments() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.CurveAdjustments getCurves() {
        return null;
    }
    
    public final float getSharpness() {
        return 0.0F;
    }
    
    public final float getNoiseReduction() {
        return 0.0F;
    }
    
    public final float getClarity() {
        return 0.0F;
    }
    
    public final float getDehaze() {
        return 0.0F;
    }
    
    public final float getVignette() {
        return 0.0F;
    }
    
    public final float getGrain() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFilmEmulation() {
        return null;
    }
    
    public final float getRotation() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.CropRatio getCropRatio() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PerspectiveAdjustment getPerspective() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LocalAdjustment> getLocalAdjustments() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.BorderWatermarkSettings getBorderWatermarkSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilterSettings getLutFilterSettings() {
        return null;
    }
    
    public PhotoEditParams() {
        super();
    }
    
    public final float component1() {
        return 0.0F;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.HSLAdjustments component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.CurveAdjustments component13() {
        return null;
    }
    
    public final float component14() {
        return 0.0F;
    }
    
    public final float component15() {
        return 0.0F;
    }
    
    public final float component16() {
        return 0.0F;
    }
    
    public final float component17() {
        return 0.0F;
    }
    
    public final float component18() {
        return 0.0F;
    }
    
    public final float component19() {
        return 0.0F;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component20() {
        return null;
    }
    
    public final float component21() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.CropRatio component22() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PerspectiveAdjustment component23() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LocalAdjustment> component24() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.BorderWatermarkSettings component25() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.LUTFilterSettings component26() {
        return null;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final float component6() {
        return 0.0F;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoEditParams copy(float exposure, float highlights, float shadows, float whites, float blacks, float contrast, float brightness, float vibrance, float saturation, float temperature, float tint, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.HSLAdjustments hslAdjustments, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.CurveAdjustments curves, float sharpness, float noiseReduction, float clarity, float dehaze, float vignette, float grain, @org.jetbrains.annotations.Nullable()
    java.lang.String filmEmulation, float rotation, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.CropRatio cropRatio, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PerspectiveAdjustment perspective, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LocalAdjustment> localAdjustments, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.BorderWatermarkSettings borderWatermarkSettings, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilterSettings lutFilterSettings) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}