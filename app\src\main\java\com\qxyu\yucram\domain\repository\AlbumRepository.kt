package com.qxyu.yucram.domain.repository

import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 相册仓库接口
 */
interface AlbumRepository {
    
    // ========== 基础操作 ==========
    
    /**
     * 获取所有相册
     */
    fun getAllAlbums(): Flow<List<Album>>
    
    /**
     * 根据ID获取相册
     */
    suspend fun getAlbumById(albumId: String): Album?
    
    /**
     * 创建相册
     */
    suspend fun createAlbum(request: CreateAlbumRequest): Result<Album>
    
    /**
     * 更新相册
     */
    suspend fun updateAlbum(albumId: String, request: UpdateAlbumRequest): Result<Album>
    
    /**
     * 删除相册
     */
    suspend fun deleteAlbum(albumId: String): Result<Unit>
    
    /**
     * 获取用户创建的相册
     */
    fun getUserAlbums(): Flow<List<Album>>
    
    /**
     * 获取系统相册
     */
    fun getSystemAlbums(): Flow<List<Album>>
    
    // ========== 照片管理 ==========
    
    /**
     * 获取相册中的照片
     */
    fun getPhotosInAlbum(albumId: String): Flow<List<Photo>>
    
    /**
     * 添加照片到相册
     */
    suspend fun addPhotoToAlbum(albumId: String, photoId: String): Result<Unit>
    
    /**
     * 批量添加照片到相册
     */
    suspend fun addPhotosToAlbum(albumId: String, photoIds: List<String>): Result<Unit>
    
    /**
     * 从相册移除照片
     */
    suspend fun removePhotoFromAlbum(albumId: String, photoId: String): Result<Unit>
    
    /**
     * 批量从相册移除照片
     */
    suspend fun removePhotosFromAlbum(albumId: String, photoIds: List<String>): Result<Unit>
    
    /**
     * 获取照片所属的相册
     */
    suspend fun getAlbumsForPhoto(photoId: String): List<Album>
    
    // ========== 搜索 ==========
    
    /**
     * 搜索相册
     */
    fun searchAlbums(query: String): Flow<List<Album>>
    
    /**
     * 根据标签获取相册
     */
    fun getAlbumsByTag(tag: String): Flow<List<Album>>
    
    // ========== 统计 ==========
    
    /**
     * 获取相册统计信息
     */
    suspend fun getAlbumStats(albumId: String): AlbumStats
    
    /**
     * 获取相册数量
     */
    suspend fun getAlbumCount(): Int
    
    // ========== 智能相册 ==========
    
    /**
     * 创建智能相册
     */
    suspend fun createSmartAlbum(album: Album, rules: List<SmartAlbumRule>): Result<Album>
    
    /**
     * 更新智能相册规则
     */
    suspend fun updateSmartAlbumRules(albumId: String, rules: List<SmartAlbumRule>): Result<Unit>
    
    /**
     * 刷新智能相册
     */
    suspend fun refreshSmartAlbum(albumId: String): Result<Unit>
    
    // ========== 系统相册初始化 ==========
    
    /**
     * 初始化系统相册
     */
    suspend fun initializeSystemAlbums(): Result<Unit>
}
