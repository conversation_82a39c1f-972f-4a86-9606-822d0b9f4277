package com.qxyu.yucram.presentation.photoedit.components;

/**
 * HSL颜色枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0019\u0010\u0004\u001a\u00020\u0005\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\t\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012j\u0002\b\u0013\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006\u0014"}, d2 = {"Lcom/qxyu/yucram/presentation/photoedit/components/HSLColor;", "", "displayName", "", "color", "Landroidx/compose/ui/graphics/Color;", "(Ljava/lang/String;ILjava/lang/String;J)V", "getColor-0d7_KjU", "()J", "J", "getDisplayName", "()Ljava/lang/String;", "RED", "ORANGE", "YELLOW", "GREEN", "CYAN", "BLUE", "PURPLE", "MAGENTA", "app_debug"})
public enum HSLColor {
    /*public static final*/ RED /* = new RED(null, 0L) */,
    /*public static final*/ ORANGE /* = new ORANGE(null, 0L) */,
    /*public static final*/ YELLOW /* = new YELLOW(null, 0L) */,
    /*public static final*/ GREEN /* = new GREEN(null, 0L) */,
    /*public static final*/ CYAN /* = new CYAN(null, 0L) */,
    /*public static final*/ BLUE /* = new BLUE(null, 0L) */,
    /*public static final*/ PURPLE /* = new PURPLE(null, 0L) */,
    /*public static final*/ MAGENTA /* = new MAGENTA(null, 0L) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    private final long color = 0L;
    
    HSLColor(java.lang.String displayName, long color) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.presentation.photoedit.components.HSLColor> getEntries() {
        return null;
    }
}