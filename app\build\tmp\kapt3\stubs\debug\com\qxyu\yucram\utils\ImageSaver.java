package com.qxyu.yucram.utils;

/**
 * 图像保存工具类
 * 支持JPEG和RAW(DNG)格式保存
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u0000 \u001c2\u00020\u0001:\u0001\u001cB\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bJ0\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\r\u001a\u00020\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0011\u0010\u0012J0\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\r\u001a\u00020\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0012J@\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0018\u001a\u00020\u00192\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\u001bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u001d"}, d2 = {"Lcom/qxyu/yucram/utils/ImageSaver;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "isHdrSupported", "", "characteristics", "Landroid/hardware/camera2/CameraCharacteristics;", "isRawSupported", "saveHdrImage", "Lkotlin/Result;", "Landroid/net/Uri;", "imageBytes", "", "filename", "", "saveHdrImage-0E7RQCE", "([BLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveJpegImage", "saveJpegImage-0E7RQCE", "saveRawImage", "image", "Landroid/media/Image;", "captureResult", "Landroid/hardware/camera2/TotalCaptureResult;", "saveRawImage-yxL6bBk", "(Landroid/media/Image;Landroid/hardware/camera2/CameraCharacteristics;Landroid/hardware/camera2/TotalCaptureResult;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class ImageSaver {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ImageSaver";
    private static final int JPEG_QUALITY = 100;
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.utils.ImageSaver.Companion Companion = null;
    
    public ImageSaver(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 检查设备是否支持RAW格式
     */
    public final boolean isRawSupported(@org.jetbrains.annotations.NotNull()
    android.hardware.camera2.CameraCharacteristics characteristics) {
        return false;
    }
    
    /**
     * 检查设备是否支持HDR
     */
    public final boolean isHdrSupported(@org.jetbrains.annotations.NotNull()
    android.hardware.camera2.CameraCharacteristics characteristics) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0007"}, d2 = {"Lcom/qxyu/yucram/utils/ImageSaver$Companion;", "", "()V", "JPEG_QUALITY", "", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}