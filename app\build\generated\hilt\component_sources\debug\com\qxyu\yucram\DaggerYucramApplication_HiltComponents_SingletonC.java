// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.qxyu.yucram.camera.CameraManager;
import com.qxyu.yucram.camera.RawCaptureManager;
import com.qxyu.yucram.data.local.YucramDatabase;
import com.qxyu.yucram.data.local.dao.PhotoDao;
import com.qxyu.yucram.data.repository.AlbumRepositoryImpl;
import com.qxyu.yucram.data.repository.PhotoRepositoryImpl;
import com.qxyu.yucram.di.AppModule;
import com.qxyu.yucram.di.AppModule_ProvideCameraManagerFactory;
import com.qxyu.yucram.di.DatabaseModule;
import com.qxyu.yucram.di.DatabaseModule_ProvidePhotoDaoFactory;
import com.qxyu.yucram.di.DatabaseModule_ProvideYucramDatabaseFactory;
import com.qxyu.yucram.di.FilterSystemModule;
import com.qxyu.yucram.di.FilterSystemModule_ProvideAlbumRepositoryFactory;
import com.qxyu.yucram.di.FilterSystemModule_ProvideDeviceCapabilityDetectorFactory;
import com.qxyu.yucram.di.FilterSystemModule_ProvideImageProcessingPipelineFactory;
import com.qxyu.yucram.di.FilterSystemModule_ProvideLutFileManagerFactory;
import com.qxyu.yucram.di.FilterSystemModule_ProvideLutProcessorFactory;
import com.qxyu.yucram.di.FilterSystemModule_ProvidePhotoRepositoryFactory;
import com.qxyu.yucram.di.FilterSystemModule_ProvideRawCaptureManagerFactory;
import com.qxyu.yucram.di.FilterSystemModule_ProvideRawToLogProcessorFactory;
import com.qxyu.yucram.domain.repository.AlbumRepository;
import com.qxyu.yucram.domain.repository.PhotoRepository;
import com.qxyu.yucram.film.LutProcessor;
import com.qxyu.yucram.filter.DeviceCapabilityDetector;
import com.qxyu.yucram.filter.ImageProcessingPipeline;
import com.qxyu.yucram.filter.LutFileManager;
import com.qxyu.yucram.presentation.album.AlbumDetailViewModel;
import com.qxyu.yucram.presentation.album.AlbumDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.album.AlbumListViewModel;
import com.qxyu.yucram.presentation.album.AlbumListViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.album.SmartAlbumViewModel;
import com.qxyu.yucram.presentation.album.SmartAlbumViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.borderwater.BorderWatermarkViewModel;
import com.qxyu.yucram.presentation.borderwater.BorderWatermarkViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.camera.CameraViewModel;
import com.qxyu.yucram.presentation.camera.CameraViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.filter.FilterSelectionViewModel;
import com.qxyu.yucram.presentation.filter.FilterSelectionViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.gallery.GalleryViewModel;
import com.qxyu.yucram.presentation.gallery.GalleryViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.permission.PermissionViewModel;
import com.qxyu.yucram.presentation.permission.PermissionViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.photodetail.PhotoDetailViewModel;
import com.qxyu.yucram.presentation.photodetail.PhotoDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.presentation.photoedit.PhotoEditViewModel;
import com.qxyu.yucram.presentation.photoedit.PhotoEditViewModel_HiltModules_KeyModule_ProvideFactory;
import com.qxyu.yucram.processing.RawToLogProcessor;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideApplicationFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.MapBuilder;
import dagger.internal.Preconditions;
import dagger.internal.SetBuilder;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.inject.Provider;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerYucramApplication_HiltComponents_SingletonC {
  private DaggerYucramApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder appModule(AppModule appModule) {
      Preconditions.checkNotNull(appModule);
      return this;
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder databaseModule(DatabaseModule databaseModule) {
      Preconditions.checkNotNull(databaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder filterSystemModule(FilterSystemModule filterSystemModule) {
      Preconditions.checkNotNull(filterSystemModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    public YucramApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements YucramApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public YucramApplication_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements YucramApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public YucramApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements YucramApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public YucramApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements YucramApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public YucramApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements YucramApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public YucramApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements YucramApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public YucramApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements YucramApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public YucramApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends YucramApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends YucramApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends YucramApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends YucramApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
      injectMainActivity2(mainActivity);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return SetBuilder.<String>newSetBuilder(10).add(AlbumDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(AlbumListViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(BorderWatermarkViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(CameraViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(FilterSelectionViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(GalleryViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(PermissionViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(PhotoDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(PhotoEditViewModel_HiltModules_KeyModule_ProvideFactory.provide()).add(SmartAlbumViewModel_HiltModules_KeyModule_ProvideFactory.provide()).build();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectCameraManager(instance, singletonCImpl.provideCameraManagerProvider.get());
      return instance;
    }
  }

  private static final class ViewModelCImpl extends YucramApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<AlbumDetailViewModel> albumDetailViewModelProvider;

    private Provider<AlbumListViewModel> albumListViewModelProvider;

    private Provider<BorderWatermarkViewModel> borderWatermarkViewModelProvider;

    private Provider<CameraViewModel> cameraViewModelProvider;

    private Provider<FilterSelectionViewModel> filterSelectionViewModelProvider;

    private Provider<GalleryViewModel> galleryViewModelProvider;

    private Provider<PermissionViewModel> permissionViewModelProvider;

    private Provider<PhotoDetailViewModel> photoDetailViewModelProvider;

    private Provider<PhotoEditViewModel> photoEditViewModelProvider;

    private Provider<SmartAlbumViewModel> smartAlbumViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.albumDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.albumListViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.borderWatermarkViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.cameraViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.filterSelectionViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.galleryViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.permissionViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.photoDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.photoEditViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.smartAlbumViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return MapBuilder.<String, Provider<ViewModel>>newMapBuilder(10).put("com.qxyu.yucram.presentation.album.AlbumDetailViewModel", ((Provider) albumDetailViewModelProvider)).put("com.qxyu.yucram.presentation.album.AlbumListViewModel", ((Provider) albumListViewModelProvider)).put("com.qxyu.yucram.presentation.borderwater.BorderWatermarkViewModel", ((Provider) borderWatermarkViewModelProvider)).put("com.qxyu.yucram.presentation.camera.CameraViewModel", ((Provider) cameraViewModelProvider)).put("com.qxyu.yucram.presentation.filter.FilterSelectionViewModel", ((Provider) filterSelectionViewModelProvider)).put("com.qxyu.yucram.presentation.gallery.GalleryViewModel", ((Provider) galleryViewModelProvider)).put("com.qxyu.yucram.presentation.permission.PermissionViewModel", ((Provider) permissionViewModelProvider)).put("com.qxyu.yucram.presentation.photodetail.PhotoDetailViewModel", ((Provider) photoDetailViewModelProvider)).put("com.qxyu.yucram.presentation.photoedit.PhotoEditViewModel", ((Provider) photoEditViewModelProvider)).put("com.qxyu.yucram.presentation.album.SmartAlbumViewModel", ((Provider) smartAlbumViewModelProvider)).build();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.qxyu.yucram.presentation.album.AlbumDetailViewModel 
          return (T) new AlbumDetailViewModel(singletonCImpl.provideAlbumRepositoryProvider.get());

          case 1: // com.qxyu.yucram.presentation.album.AlbumListViewModel 
          return (T) new AlbumListViewModel(singletonCImpl.provideAlbumRepositoryProvider.get());

          case 2: // com.qxyu.yucram.presentation.borderwater.BorderWatermarkViewModel 
          return (T) new BorderWatermarkViewModel(singletonCImpl.providePhotoRepositoryProvider.get());

          case 3: // com.qxyu.yucram.presentation.camera.CameraViewModel 
          return (T) new CameraViewModel(singletonCImpl.provideCameraManagerProvider.get());

          case 4: // com.qxyu.yucram.presentation.filter.FilterSelectionViewModel 
          return (T) new FilterSelectionViewModel(singletonCImpl.provideLutFileManagerProvider.get(), singletonCImpl.provideDeviceCapabilityDetectorProvider.get());

          case 5: // com.qxyu.yucram.presentation.gallery.GalleryViewModel 
          return (T) new GalleryViewModel(singletonCImpl.providePhotoRepositoryProvider.get(), singletonCImpl.provideAlbumRepositoryProvider.get());

          case 6: // com.qxyu.yucram.presentation.permission.PermissionViewModel 
          return (T) new PermissionViewModel(ApplicationContextModule_ProvideApplicationFactory.provideApplication(singletonCImpl.applicationContextModule));

          case 7: // com.qxyu.yucram.presentation.photodetail.PhotoDetailViewModel 
          return (T) new PhotoDetailViewModel(singletonCImpl.providePhotoRepositoryProvider.get());

          case 8: // com.qxyu.yucram.presentation.photoedit.PhotoEditViewModel 
          return (T) new PhotoEditViewModel(singletonCImpl.providePhotoRepositoryProvider.get());

          case 9: // com.qxyu.yucram.presentation.album.SmartAlbumViewModel 
          return (T) new SmartAlbumViewModel(singletonCImpl.provideAlbumRepositoryProvider.get(), singletonCImpl.providePhotoRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends YucramApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends YucramApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends YucramApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<LutProcessor> provideLutProcessorProvider;

    private Provider<DeviceCapabilityDetector> provideDeviceCapabilityDetectorProvider;

    private Provider<RawToLogProcessor> provideRawToLogProcessorProvider;

    private Provider<RawCaptureManager> provideRawCaptureManagerProvider;

    private Provider<ImageProcessingPipeline> provideImageProcessingPipelineProvider;

    private Provider<CameraManager> provideCameraManagerProvider;

    private Provider<AlbumRepositoryImpl> albumRepositoryImplProvider;

    private Provider<AlbumRepository> provideAlbumRepositoryProvider;

    private Provider<YucramDatabase> provideYucramDatabaseProvider;

    private Provider<PhotoRepositoryImpl> photoRepositoryImplProvider;

    private Provider<PhotoRepository> providePhotoRepositoryProvider;

    private Provider<LutFileManager> provideLutFileManagerProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private PhotoDao photoDao() {
      return DatabaseModule_ProvidePhotoDaoFactory.providePhotoDao(provideYucramDatabaseProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.provideLutProcessorProvider = DoubleCheck.provider(new SwitchingProvider<LutProcessor>(singletonCImpl, 2));
      this.provideDeviceCapabilityDetectorProvider = DoubleCheck.provider(new SwitchingProvider<DeviceCapabilityDetector>(singletonCImpl, 3));
      this.provideRawToLogProcessorProvider = DoubleCheck.provider(new SwitchingProvider<RawToLogProcessor>(singletonCImpl, 4));
      this.provideRawCaptureManagerProvider = DoubleCheck.provider(new SwitchingProvider<RawCaptureManager>(singletonCImpl, 5));
      this.provideImageProcessingPipelineProvider = DoubleCheck.provider(new SwitchingProvider<ImageProcessingPipeline>(singletonCImpl, 1));
      this.provideCameraManagerProvider = DoubleCheck.provider(new SwitchingProvider<CameraManager>(singletonCImpl, 0));
      this.albumRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<AlbumRepositoryImpl>(singletonCImpl, 7));
      this.provideAlbumRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<AlbumRepository>(singletonCImpl, 6));
      this.provideYucramDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<YucramDatabase>(singletonCImpl, 10));
      this.photoRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<PhotoRepositoryImpl>(singletonCImpl, 9));
      this.providePhotoRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<PhotoRepository>(singletonCImpl, 8));
      this.provideLutFileManagerProvider = DoubleCheck.provider(new SwitchingProvider<LutFileManager>(singletonCImpl, 11));
    }

    @Override
    public void injectYucramApplication(YucramApplication yucramApplication) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.qxyu.yucram.camera.CameraManager 
          return (T) AppModule_ProvideCameraManagerFactory.provideCameraManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideImageProcessingPipelineProvider.get());

          case 1: // com.qxyu.yucram.filter.ImageProcessingPipeline 
          return (T) FilterSystemModule_ProvideImageProcessingPipelineFactory.provideImageProcessingPipeline(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideLutProcessorProvider.get(), singletonCImpl.provideDeviceCapabilityDetectorProvider.get(), singletonCImpl.provideRawToLogProcessorProvider.get(), singletonCImpl.provideRawCaptureManagerProvider.get());

          case 2: // com.qxyu.yucram.film.LutProcessor 
          return (T) FilterSystemModule_ProvideLutProcessorFactory.provideLutProcessor(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 3: // com.qxyu.yucram.filter.DeviceCapabilityDetector 
          return (T) FilterSystemModule_ProvideDeviceCapabilityDetectorFactory.provideDeviceCapabilityDetector(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 4: // com.qxyu.yucram.processing.RawToLogProcessor 
          return (T) FilterSystemModule_ProvideRawToLogProcessorFactory.provideRawToLogProcessor(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 5: // com.qxyu.yucram.camera.RawCaptureManager 
          return (T) FilterSystemModule_ProvideRawCaptureManagerFactory.provideRawCaptureManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 6: // com.qxyu.yucram.domain.repository.AlbumRepository 
          return (T) FilterSystemModule_ProvideAlbumRepositoryFactory.provideAlbumRepository(singletonCImpl.albumRepositoryImplProvider.get());

          case 7: // com.qxyu.yucram.data.repository.AlbumRepositoryImpl 
          return (T) new AlbumRepositoryImpl();

          case 8: // com.qxyu.yucram.domain.repository.PhotoRepository 
          return (T) FilterSystemModule_ProvidePhotoRepositoryFactory.providePhotoRepository(singletonCImpl.photoRepositoryImplProvider.get());

          case 9: // com.qxyu.yucram.data.repository.PhotoRepositoryImpl 
          return (T) new PhotoRepositoryImpl(singletonCImpl.photoDao());

          case 10: // com.qxyu.yucram.data.local.YucramDatabase 
          return (T) DatabaseModule_ProvideYucramDatabaseFactory.provideYucramDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 11: // com.qxyu.yucram.filter.LutFileManager 
          return (T) FilterSystemModule_ProvideLutFileManagerFactory.provideLutFileManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
