package com.qxyu.yucram.presentation.photoedit;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\r\u001a8\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a0\u0010\f\u001a\u00020\u00012\b\u0010\r\u001a\u0004\u0018\u00010\u00032\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a6\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00052\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001aD\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00052\u000e\b\u0002\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\u000e\b\u0002\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\u0017\u001a\u00020\u0018H\u0007\u001az\u0010\u0019\u001a\u00020\u00012\b\u0010\u001a\u001a\u0004\u0018\u00010\u001b2\u0006\u0010\u001c\u001a\u00020\u00072\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\t2\u0006\u0010!\u001a\u00020\u00072\u0006\u0010\"\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0007\u001a$\u0010#\u001a\u00020\u00012\f\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u001a2\u0010&\u001a\u00020\u00012\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00010\t2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u00a8\u0006("}, d2 = {"EditToolButton", "", "tool", "Lcom/qxyu/yucram/domain/model/EditTool;", "name", "", "isSelected", "", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "EditToolSelector", "currentTool", "onToolSelected", "Lkotlin/Function1;", "PhotoEditError", "error", "onRetry", "onNavigateBack", "PhotoEditScreen", "photoId", "onSaveComplete", "viewModel", "Lcom/qxyu/yucram/presentation/photoedit/PhotoEditViewModel;", "PhotoEditTopBar", "photo", "Lcom/qxyu/yucram/domain/model/Photo;", "hasChanges", "onReset", "onSave", "onUndo", "onRedo", "canUndo", "canRedo", "ResetConfirmationDialog", "onConfirm", "onCancel", "SaveConfirmationDialog", "onDiscard", "app_debug"})
public final class PhotoEditScreenKt {
    
    /**
     * 专业照片编辑界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PhotoEditScreen(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSaveComplete, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.PhotoEditViewModel viewModel) {
    }
    
    /**
     * 编辑顶部工具栏
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void PhotoEditTopBar(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.Photo photo, boolean hasChanges, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onReset, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSave, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onUndo, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRedo, boolean canUndo, boolean canRedo, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 工具选择器
     */
    @androidx.compose.runtime.Composable()
    public static final void EditToolSelector(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.EditTool currentTool, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.EditTool, kotlin.Unit> onToolSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 工具按钮
     */
    @androidx.compose.runtime.Composable()
    public static final void EditToolButton(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.EditTool tool, @org.jetbrains.annotations.NotNull()
    java.lang.String name, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 保存确认对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void SaveConfirmationDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSave, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDiscard, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel) {
    }
    
    /**
     * 重置确认对话框
     */
    @androidx.compose.runtime.Composable()
    public static final void ResetConfirmationDialog(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onCancel) {
    }
    
    /**
     * 编辑错误状态
     */
    @androidx.compose.runtime.Composable()
    public static final void PhotoEditError(@org.jetbrains.annotations.NotNull()
    java.lang.String error, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onRetry, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}