package com.qxyu.yucram.presentation.photoedit.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000H\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0005\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a0\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\r2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0012\u0010\u0011\u001a\u000e\u0012\u0004\u0012\u00020\u0010\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a>\u0010\u0012\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u00142\u0012\u0010\u001a\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a0\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\r2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u0010\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0015\u001a\u00020\u0016H\u0002\u001a \u0010\u001e\u001a\u00020\u00162\u0006\u0010\u001f\u001a\u00020\u00102\u0006\u0010\t\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0014H\u0002\u001a\u0018\u0010 \u001a\u00020\u00102\u0006\u0010\u001f\u001a\u00020\u00102\u0006\u0010\t\u001a\u00020\u0003H\u0002\u001a(\u0010!\u001a\u00020\u00102\u0006\u0010\u001f\u001a\u00020\u00102\u0006\u0010\t\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\u0015\u001a\u00020\u0016H\u0002\u00a8\u0006\""}, d2 = {"ColorSelector", "", "selectedColor", "Lcom/qxyu/yucram/presentation/photoedit/components/HSLColor;", "onColorSelected", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "ColorSelectorItem", "color", "isSelected", "", "onClick", "Lkotlin/Function0;", "HSLAdjustmentPanel", "hslAdjustments", "Lcom/qxyu/yucram/domain/model/HSLAdjustments;", "onAdjustmentsChanged", "HSLSlider", "property", "Lcom/qxyu/yucram/presentation/photoedit/components/HSLProperty;", "value", "", "onValueChanged", "PropertySelector", "selectedProperty", "onPropertySelected", "PropertySelectorButton", "formatSliderValue", "", "getCurrentValue", "adjustments", "resetColorAdjustments", "updateHSLValue", "app_debug"})
public final class HSLAdjustmentPanelKt {
    
    /**
     * HSL颜色调整面板
     */
    @androidx.compose.runtime.Composable()
    public static final void HSLAdjustmentPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.HSLAdjustments hslAdjustments, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.HSLAdjustments, kotlin.Unit> onAdjustmentsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 颜色选择器
     */
    @androidx.compose.runtime.Composable()
    public static final void ColorSelector(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.HSLColor selectedColor, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.HSLColor, kotlin.Unit> onColorSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 颜色选择项
     */
    @androidx.compose.runtime.Composable()
    public static final void ColorSelectorItem(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.HSLColor color, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 属性选择器
     */
    @androidx.compose.runtime.Composable()
    public static final void PropertySelector(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.HSLProperty selectedProperty, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.HSLProperty, kotlin.Unit> onPropertySelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 属性选择按钮
     */
    @androidx.compose.runtime.Composable()
    public static final void PropertySelectorButton(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.HSLProperty property, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * HSL调整滑块
     */
    @androidx.compose.runtime.Composable()
    public static final void HSLSlider(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.HSLColor color, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.HSLProperty property, float value, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onValueChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 获取当前HSL值
     */
    private static final float getCurrentValue(com.qxyu.yucram.domain.model.HSLAdjustments adjustments, com.qxyu.yucram.presentation.photoedit.components.HSLColor color, com.qxyu.yucram.presentation.photoedit.components.HSLProperty property) {
        return 0.0F;
    }
    
    /**
     * 更新HSL值
     */
    private static final com.qxyu.yucram.domain.model.HSLAdjustments updateHSLValue(com.qxyu.yucram.domain.model.HSLAdjustments adjustments, com.qxyu.yucram.presentation.photoedit.components.HSLColor color, com.qxyu.yucram.presentation.photoedit.components.HSLProperty property, float value) {
        return null;
    }
    
    /**
     * 重置颜色调整
     */
    private static final com.qxyu.yucram.domain.model.HSLAdjustments resetColorAdjustments(com.qxyu.yucram.domain.model.HSLAdjustments adjustments, com.qxyu.yucram.presentation.photoedit.components.HSLColor color) {
        return null;
    }
    
    /**
     * 格式化滑块数值
     */
    private static final java.lang.String formatSliderValue(float value) {
        return null;
    }
}