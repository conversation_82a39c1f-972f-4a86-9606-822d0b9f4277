{"logs": [{"outputFile": "com.qxyu.yucram.app-mergeDebugResources-74:/values-in/values-in.xml", "map": [{"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\90847ba8583f288576ac60503f85644e\\transformed\\core-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3255,3350,3452,3549,3646,3752,3870,9235", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3345,3447,3544,3641,3747,3865,3980,9331"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\df3071bcb830d4a3b4d94201c338cea1\\transformed\\jetified-ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,977,1044,1126,1209,1281,1359,1425", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,972,1039,1121,1204,1276,1354,1420,1539"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3985,4075,7831,7929,8101,8264,8347,8438,8525,8610,8680,8747,8829,9082,9431,9509,9575", "endColumns": "89,81,97,99,85,82,90,86,84,69,66,81,82,71,77,65,118", "endOffsets": "4070,4152,7924,8024,8182,8342,8433,8520,8605,8675,8742,8824,8907,9149,9504,9570,9689"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\e6ffc869bf3a530da82c75f86303dcbf\\transformed\\jetified-material3-1.1.2\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,281,389,501,579,681,801,944,1063,1209,1292,1392,1481,1581,1695,1811,1916,2052,2186,2311,2485,2603,2719,2839,2959,3049,3146,3262,3391,3489,3592,3695,3832,3975,4080,4175,4247,4324,4409,4490,4585,4661,4740,4835,4930,5023,5119,5202,5301,5396,5495,5608,5684,5785", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "165,276,384,496,574,676,796,939,1058,1204,1287,1387,1476,1576,1690,1806,1911,2047,2181,2306,2480,2598,2714,2834,2954,3044,3141,3257,3386,3484,3587,3690,3827,3970,4075,4170,4242,4319,4404,4485,4580,4656,4735,4830,4925,5018,5114,5197,5296,5391,5490,5603,5679,5780,5871"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2809,2924,3035,3143,4157,4235,4337,4457,4600,4719,4865,4948,5048,5137,5237,5351,5467,5572,5708,5842,5967,6141,6259,6375,6495,6615,6705,6802,6918,7047,7145,7248,7351,7488,7631,7736,8029,8187,8912,9154,9336,9694,9770,9849,9944,10039,10132,10228,10311,10410,10505,10604,10717,10793,10894", "endColumns": "114,110,107,111,77,101,119,142,118,145,82,99,88,99,113,115,104,135,133,124,173,117,115,119,119,89,96,115,128,97,102,102,136,142,104,94,71,76,84,80,94,75,78,94,94,92,95,82,98,94,98,112,75,100,90", "endOffsets": "2919,3030,3138,3250,4230,4332,4452,4595,4714,4860,4943,5043,5132,5232,5346,5462,5567,5703,5837,5962,6136,6254,6370,6490,6610,6700,6797,6913,7042,7140,7243,7346,7483,7626,7731,7826,8096,8259,8992,9230,9426,9765,9844,9939,10034,10127,10223,10306,10405,10500,10599,10712,10788,10889,10980"}}, {"source": "D:\\XSBDownload\\SDK\\.gradle\\caches\\transforms-3\\b687dbcc02c458c0942b5ddaef0cb144\\transformed\\appcompat-1.6.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,2889"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1920,2023,2128,2229,2333,2442,2550,2710,8997", "endColumns": "114,103,104,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,424,511,615,731,814,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1915,2018,2123,2224,2328,2437,2545,2705,2804,9077"}}]}]}