package com.qxyu.yucram.domain.model;

/**
 * 边框图案
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"}, d2 = {"Lcom/qxyu/yucram/domain/model/BorderPattern;", "", "(Ljava/lang/String;I)V", "NONE", "DOTS", "LINES", "DASHES", "WAVES", "ZIGZAG", "HEARTS", "STARS", "FLOWERS", "GEOMETRIC", "app_debug"})
public enum BorderPattern {
    /*public static final*/ NONE /* = new NONE() */,
    /*public static final*/ DOTS /* = new DOTS() */,
    /*public static final*/ LINES /* = new LINES() */,
    /*public static final*/ DASHES /* = new DASHES() */,
    /*public static final*/ WAVES /* = new WAVES() */,
    /*public static final*/ ZIGZAG /* = new ZIGZAG() */,
    /*public static final*/ HEARTS /* = new HEARTS() */,
    /*public static final*/ STARS /* = new STARS() */,
    /*public static final*/ FLOWERS /* = new FLOWERS() */,
    /*public static final*/ GEOMETRIC /* = new GEOMETRIC() */;
    
    BorderPattern() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.BorderPattern> getEntries() {
        return null;
    }
}