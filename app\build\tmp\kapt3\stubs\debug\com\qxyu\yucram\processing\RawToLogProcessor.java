package com.qxyu.yucram.processing;

/**
 * RAW到LOG处理器
 * 实现专业的RAW解码和LOG色彩空间转换
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0012\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\b\u0007\u0018\u0000 (2\u00020\u0001:\u0001(B\u0011\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\n\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u0002J \u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u00062\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002J(\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH\u0002J8\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u000fH\u0002J8\u0010\u001b\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u000fH\u0002J8\u0010\u001c\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u000fH\u0002J\u0010\u0010\u001d\u001a\u00020\u00152\u0006\u0010\u001e\u001a\u00020\u0015H\u0002J\u0010\u0010\u001f\u001a\u00020\u00152\u0006\u0010 \u001a\u00020\u0015H\u0002J<\u0010!\u001a\b\u0012\u0004\u0012\u00020\f0\"2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b#\u0010$J\u000e\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\u0015R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006)"}, d2 = {"Lcom/qxyu/yucram/processing/RawToLogProcessor;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "applyColorCorrection", "", "linearRgbData", "captureResult", "Landroid/hardware/camera2/CaptureResult;", "convertLinearToLog", "createBitmapFromLogData", "Landroid/graphics/Bitmap;", "logData", "width", "", "height", "decodeRawToLinearRgb", "rawData", "", "interpolateBlue", "", "x", "y", "buffer", "Ljava/nio/ByteBuffer;", "whiteLevel", "interpolateGreen", "interpolateRed", "linearToLog", "linearValue", "logToLinear", "logValue", "processRawToLog", "Lkotlin/Result;", "processRawToLog-yxL6bBk", "([BLandroid/hardware/camera2/CaptureResult;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "verifyLogConversion", "", "originalLinear", "Companion", "app_debug"})
public final class RawToLogProcessor {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "RawToLogProcessor";
    private static final float LOG_CUT = 0.011361F;
    private static final float LOG_A = 5.555556F;
    private static final float LOG_B = 0.047996F;
    private static final float LOG_C = 0.244161F;
    private static final float LOG_D = 0.386036F;
    @org.jetbrains.annotations.NotNull()
    private static final float[] COLOR_MATRIX_SRGB_TO_REC709 = {0.4124564F, 0.3575761F, 0.1804375F, 0.2126729F, 0.7151522F, 0.072175F, 0.0193339F, 0.119192F, 0.9503041F};
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.processing.RawToLogProcessor.Companion Companion = null;
    
    @javax.inject.Inject()
    public RawToLogProcessor(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * 解码RAW数据为线性RGB
     */
    private final float[] decodeRawToLinearRgb(byte[] rawData, android.hardware.camera2.CaptureResult captureResult, int width, int height) {
        return null;
    }
    
    /**
     * 应用色彩校正
     */
    private final float[] applyColorCorrection(float[] linearRgbData, android.hardware.camera2.CaptureResult captureResult) {
        return null;
    }
    
    /**
     * 转换线性RGB到LOG色彩空间
     */
    private final float[] convertLinearToLog(float[] linearRgbData) {
        return null;
    }
    
    /**
     * 线性值转LOG值
     */
    private final float linearToLog(float linearValue) {
        return 0.0F;
    }
    
    /**
     * LOG值转线性值（用于验证）
     */
    private final float logToLinear(float logValue) {
        return 0.0F;
    }
    
    /**
     * 从LOG数据创建Bitmap
     */
    private final android.graphics.Bitmap createBitmapFromLogData(float[] logData, int width, int height) {
        return null;
    }
    
    private final float interpolateRed(int x, int y, int width, int height, java.nio.ByteBuffer buffer, int whiteLevel) {
        return 0.0F;
    }
    
    private final float interpolateGreen(int x, int y, int width, int height, java.nio.ByteBuffer buffer, int whiteLevel) {
        return 0.0F;
    }
    
    private final float interpolateBlue(int x, int y, int width, int height, java.nio.ByteBuffer buffer, int whiteLevel) {
        return 0.0F;
    }
    
    /**
     * 验证LOG转换的可逆性
     */
    public final boolean verifyLogConversion(float originalLinear) {
        return false;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0014\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0006X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/qxyu/yucram/processing/RawToLogProcessor$Companion;", "", "()V", "COLOR_MATRIX_SRGB_TO_REC709", "", "LOG_A", "", "LOG_B", "LOG_C", "LOG_CUT", "LOG_D", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}