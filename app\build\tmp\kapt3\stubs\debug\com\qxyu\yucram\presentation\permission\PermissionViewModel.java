package com.qxyu.yucram.presentation.permission;

/**
 * 权限管理ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u000f\u001a\u00020\u0010J\u0006\u0010\u0011\u001a\u00020\u0012J\u0006\u0010\u0013\u001a\u00020\u0012R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\b\u001a\n \n*\u0004\u0018\u00010\t0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00070\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0014"}, d2 = {"Lcom/qxyu/yucram/presentation/permission/PermissionViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "(Landroid/app/Application;)V", "_permissionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/presentation/permission/PermissionState;", "context", "Landroid/content/Context;", "kotlin.jvm.PlatformType", "permissionState", "Lkotlinx/coroutines/flow/StateFlow;", "getPermissionState", "()Lkotlinx/coroutines/flow/StateFlow;", "canUseCamera", "", "checkPermissions", "", "onPermissionResult", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class PermissionViewModel extends androidx.lifecycle.AndroidViewModel {
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.permission.PermissionState> _permissionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.permission.PermissionState> permissionState = null;
    
    @javax.inject.Inject()
    public PermissionViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.permission.PermissionState> getPermissionState() {
        return null;
    }
    
    /**
     * 检查所有权限状态
     */
    public final void checkPermissions() {
    }
    
    /**
     * 权限请求完成后的回调
     */
    public final void onPermissionResult() {
    }
    
    /**
     * 检查是否可以使用相机
     */
    public final boolean canUseCamera() {
        return false;
    }
}