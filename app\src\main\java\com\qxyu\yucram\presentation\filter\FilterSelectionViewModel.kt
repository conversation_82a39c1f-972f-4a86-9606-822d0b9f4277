package com.qxyu.yucram.presentation.filter

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.filter.LutFileManager
import com.qxyu.yucram.filter.DeviceCapabilityDetector
import com.qxyu.yucram.filter.DeviceInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 滤镜选择界面的ViewModel
 */
@HiltViewModel
class FilterSelectionViewModel @Inject constructor(
    private val lutFileManager: LutFileManager,
    private val deviceCapabilityDetector: DeviceCapabilityDetector
) : ViewModel() {
    
    // UI状态
    private val _uiState = MutableStateFlow(FilterSelectionUiState())
    val uiState: StateFlow<FilterSelectionUiState> = _uiState.asStateFlow()
    
    // 滤镜列表
    private val _filters = MutableStateFlow<List<FilterPreset>>(emptyList())
    val filters: StateFlow<List<FilterPreset>> = _filters.asStateFlow()
    
    // 设备信息
    private val _deviceInfo = MutableStateFlow<DeviceInfo?>(null)
    val deviceInfo: StateFlow<DeviceInfo?> = _deviceInfo.asStateFlow()
    
    init {
        loadDeviceInfo()
        loadFilters()
    }
    
    /**
     * 加载滤镜列表
     */
    fun loadFilters() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            // 初始化LUT文件管理器
            lutFileManager.initialize().fold(
                onSuccess = {
                    // 加载可用滤镜
                    lutFileManager.getAvailableFilters().fold(
                        onSuccess = { filterList ->
                            _filters.value = filterList
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = null
                            )
                        },
                        onFailure = { error ->
                            _uiState.value = _uiState.value.copy(
                                isLoading = false,
                                error = error.message
                            )
                        }
                    )
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message
                    )
                }
            )
        }
    }
    
    /**
     * 选择滤镜
     */
    fun selectFilter(filter: FilterPreset) {
        _uiState.value = _uiState.value.copy(selectedFilter = filter)
    }
    
    /**
     * 更新后处理参数
     */
    fun updatePostProcessingParam(paramName: String, value: Float) {
        val currentFilter = _uiState.value.selectedFilter ?: return
        val currentParams = currentFilter.postProcessingParams
        
        val newParams = when (paramName) {
            "highlights" -> currentParams.copy(highlights = value)
            "shadows" -> currentParams.copy(shadows = value)
            "vignette" -> currentParams.copy(vignette = value)
            "chromaAberration" -> currentParams.copy(chromaAberration = value)
            "grain" -> currentParams.copy(grain = value)
            "sharpness" -> currentParams.copy(sharpness = value)
            else -> currentParams
        }
        
        val updatedFilter = currentFilter.copy(postProcessingParams = newParams)
        _uiState.value = _uiState.value.copy(selectedFilter = updatedFilter)
        
        // 保存参数到文件
        viewModelScope.launch {
            lutFileManager.updateFilterParams(currentFilter.id, newParams)
        }
    }
    
    /**
     * 导入LUT文件
     */
    fun importLut(
        lutFileUri: android.net.Uri,
        iconUri: android.net.Uri?,
        filterName: String
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isImporting = true)
            
            lutFileManager.importLut(lutFileUri, iconUri, filterName).fold(
                onSuccess = { newFilter ->
                    // 重新加载滤镜列表
                    loadFilters()
                    _uiState.value = _uiState.value.copy(
                        isImporting = false,
                        selectedFilter = newFilter
                    )
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(
                        isImporting = false,
                        error = error.message
                    )
                }
            )
        }
    }
    
    /**
     * 删除用户滤镜
     */
    fun deleteFilter(filterId: String) {
        viewModelScope.launch {
            lutFileManager.deleteUserLut(filterId).fold(
                onSuccess = {
                    // 重新加载滤镜列表
                    loadFilters()
                    
                    // 如果删除的是当前选中的滤镜，清除选择
                    if (_uiState.value.selectedFilter?.id == filterId) {
                        _uiState.value = _uiState.value.copy(selectedFilter = null)
                    }
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 导出滤镜
     */
    fun exportFilter(filterId: String, outputDir: java.io.File) {
        viewModelScope.launch {
            lutFileManager.exportLut(filterId, outputDir).fold(
                onSuccess = { exportedFile ->
                    _uiState.value = _uiState.value.copy(
                        exportedFilePath = exportedFile.absolutePath
                    )
                },
                onFailure = { error ->
                    _uiState.value = _uiState.value.copy(error = error.message)
                }
            )
        }
    }
    
    /**
     * 重置参数
     */
    fun resetPostProcessingParams() {
        val currentFilter = _uiState.value.selectedFilter ?: return
        val defaultParams = PostProcessingParams()
        
        val updatedFilter = currentFilter.copy(postProcessingParams = defaultParams)
        _uiState.value = _uiState.value.copy(selectedFilter = updatedFilter)
        
        // 保存到文件
        viewModelScope.launch {
            lutFileManager.updateFilterParams(currentFilter.id, defaultParams)
        }
    }
    
    /**
     * 清除错误
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    /**
     * 清除导出路径
     */
    fun clearExportedPath() {
        _uiState.value = _uiState.value.copy(exportedFilePath = null)
    }
    
    // 私有方法
    
    /**
     * 加载设备信息
     */
    private fun loadDeviceInfo() {
        viewModelScope.launch {
            val info = deviceCapabilityDetector.getDeviceInfo()
            _deviceInfo.value = info
        }
    }
}

/**
 * 滤镜选择界面UI状态
 */
data class FilterSelectionUiState(
    val isLoading: Boolean = false,
    val isImporting: Boolean = false,
    val selectedFilter: FilterPreset? = null,
    val error: String? = null,
    val exportedFilePath: String? = null
)
