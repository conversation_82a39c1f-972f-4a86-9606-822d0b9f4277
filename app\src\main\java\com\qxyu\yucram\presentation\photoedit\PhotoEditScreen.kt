package com.qxyu.yucram.presentation.photoedit

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.photoedit.components.*

/**
 * 专业照片编辑界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhotoEditScreen(
    photoId: String,
    onNavigateBack: () -> Unit = {},
    onSaveComplete: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: PhotoEditViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val photo by viewModel.photo.collectAsStateWithLifecycle()
    val editParams by viewModel.editParams.collectAsStateWithLifecycle()
    val currentTool by viewModel.currentTool.collectAsStateWithLifecycle()
    
    var showSaveDialog by remember { mutableStateOf(false) }
    var showResetDialog by remember { mutableStateOf(false) }
    
    LaunchedEffect(photoId) {
        viewModel.loadPhoto(photoId)
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // 顶部工具栏
        PhotoEditTopBar(
            photo = photo,
            hasChanges = viewModel.hasChanges(),
            onNavigateBack = {
                if (viewModel.hasChanges()) {
                    showSaveDialog = true
                } else {
                    onNavigateBack()
                }
            },
            onReset = { showResetDialog = true },
            onSave = { viewModel.saveEdit() },
            onUndo = { viewModel.undo() },
            onRedo = { viewModel.redo() },
            canUndo = viewModel.canUndo(),
            canRedo = viewModel.canRedo()
        )
        
        // 主要编辑区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            when {
                uiState.isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                photo != null -> {
                    // 照片预览区域
                    PhotoEditPreview(
                        photo = photo!!,
                        editParams = editParams,
                        currentTool = currentTool,
                        onToolInteraction = { interaction ->
                            viewModel.handleToolInteraction(interaction)
                        },
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                uiState.error != null -> {
                    PhotoEditError(
                        error = uiState.error!!,
                        onRetry = { viewModel.loadPhoto(photoId) },
                        onNavigateBack = onNavigateBack,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
        
        // 工具选择栏
        EditToolSelector(
            currentTool = currentTool,
            onToolSelected = { tool ->
                viewModel.selectTool(tool)
            },
            modifier = Modifier.fillMaxWidth()
        )
        
        // 工具参数调整区域
        currentTool?.let { tool ->
            EditToolPanel(
                tool = tool,
                editParams = editParams,
                onParamsChanged = { newParams ->
                    viewModel.updateEditParams(newParams)
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(200.dp)
            )
        }
    }
    
    // 保存确认对话框
    if (showSaveDialog) {
        SaveConfirmationDialog(
            onSave = {
                viewModel.saveEdit()
                showSaveDialog = false
                onSaveComplete()
            },
            onDiscard = {
                showSaveDialog = false
                onNavigateBack()
            },
            onCancel = {
                showSaveDialog = false
            }
        )
    }
    
    // 重置确认对话框
    if (showResetDialog) {
        ResetConfirmationDialog(
            onConfirm = {
                viewModel.resetEdit()
                showResetDialog = false
            },
            onCancel = {
                showResetDialog = false
            }
        )
    }
    
    // 处理保存完成
    LaunchedEffect(uiState.isSaved) {
        if (uiState.isSaved) {
            onSaveComplete()
        }
    }
}

/**
 * 编辑顶部工具栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhotoEditTopBar(
    photo: Photo?,
    hasChanges: Boolean,
    onNavigateBack: () -> Unit,
    onReset: () -> Unit,
    onSave: () -> Unit,
    onUndo: () -> Unit,
    onRedo: () -> Unit,
    canUndo: Boolean,
    canRedo: Boolean,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Column {
                Text(
                    text = photo?.fileName ?: "编辑照片",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White
                )
                if (hasChanges) {
                    Text(
                        text = "已修改",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
        },
        actions = {
            // 撤销
            IconButton(
                onClick = onUndo,
                enabled = canUndo
            ) {
                Icon(
                    imageVector = Icons.Filled.Undo,
                    contentDescription = "撤销",
                    tint = if (canUndo) Color.White else Color.White.copy(alpha = 0.3f)
                )
            }
            
            // 重做
            IconButton(
                onClick = onRedo,
                enabled = canRedo
            ) {
                Icon(
                    imageVector = Icons.Filled.Redo,
                    contentDescription = "重做",
                    tint = if (canRedo) Color.White else Color.White.copy(alpha = 0.3f)
                )
            }
            
            // 重置
            IconButton(
                onClick = onReset,
                enabled = hasChanges
            ) {
                Icon(
                    imageVector = Icons.Filled.Refresh,
                    contentDescription = "重置",
                    tint = if (hasChanges) Color.White else Color.White.copy(alpha = 0.3f)
                )
            }
            
            // 保存
            IconButton(
                onClick = onSave,
                enabled = hasChanges
            ) {
                Icon(
                    imageVector = Icons.Filled.Check,
                    contentDescription = "保存",
                    tint = if (hasChanges) MaterialTheme.colorScheme.primary else Color.White.copy(alpha = 0.3f)
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        modifier = modifier
    )
}

/**
 * 工具选择器
 */
@Composable
fun EditToolSelector(
    currentTool: EditTool?,
    onToolSelected: (EditTool) -> Unit,
    modifier: Modifier = Modifier
) {
    val tools = listOf(
        EditTool.EXPOSURE to "曝光",
        EditTool.HIGHLIGHTS_SHADOWS to "高光阴影",
        EditTool.VIBRANCE_SATURATION to "饱和度",
        EditTool.TEMPERATURE_TINT to "色温",
        EditTool.HSL to "HSL",
        EditTool.CURVES to "曲线",
        EditTool.SHARPNESS to "锐度",
        EditTool.VIGNETTE to "暗角",
        EditTool.CROP_ROTATE to "裁剪",
        EditTool.LUT_FILTERS to "滤镜",
        EditTool.BORDER_WATERMARK to "边框水印"
    )
    
    LazyRow(
        modifier = modifier
            .background(Color.Black.copy(alpha = 0.8f))
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(tools) { (tool, name) ->
            EditToolButton(
                tool = tool,
                name = name,
                isSelected = currentTool == tool,
                onClick = { onToolSelected(tool) }
            )
        }
    }
}

/**
 * 工具按钮
 */
@Composable
fun EditToolButton(
    tool: EditTool,
    name: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primary
            } else {
                Color.Transparent
            },
            contentColor = if (isSelected) {
                MaterialTheme.colorScheme.onPrimary
            } else {
                Color.White
            }
        ),
        modifier = modifier
    ) {
        Text(
            text = name,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * 保存确认对话框
 */
@Composable
fun SaveConfirmationDialog(
    onSave: () -> Unit,
    onDiscard: () -> Unit,
    onCancel: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onCancel,
        title = {
            Text("保存更改？")
        },
        text = {
            Text("您有未保存的更改，是否要保存？")
        },
        confirmButton = {
            TextButton(onClick = onSave) {
                Text("保存")
            }
        },
        dismissButton = {
            Row {
                TextButton(onClick = onDiscard) {
                    Text("放弃")
                }
                TextButton(onClick = onCancel) {
                    Text("取消")
                }
            }
        }
    )
}

/**
 * 重置确认对话框
 */
@Composable
fun ResetConfirmationDialog(
    onConfirm: () -> Unit,
    onCancel: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onCancel,
        title = {
            Text("重置所有更改？")
        },
        text = {
            Text("这将撤销所有编辑操作，此操作无法恢复。")
        },
        confirmButton = {
            TextButton(onClick = onConfirm) {
                Text("重置")
            }
        },
        dismissButton = {
            TextButton(onClick = onCancel) {
                Text("取消")
            }
        }
    )
}

/**
 * 编辑错误状态
 */
@Composable
fun PhotoEditError(
    error: String,
    onRetry: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Filled.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "编辑器加载失败",
            style = MaterialTheme.typography.headlineSmall,
            color = Color.White
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                onClick = onNavigateBack,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.White
                )
            ) {
                Text("返回")
            }
            
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}
