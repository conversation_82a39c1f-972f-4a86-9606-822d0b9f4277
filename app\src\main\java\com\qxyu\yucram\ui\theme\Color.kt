package com.qxyu.yucram.ui.theme

import androidx.compose.ui.graphics.Color

// Primary Colors - Orange Theme
val PrimaryOrange = Color(0xFFFF6600)
val PrimaryOrangeDark = Color(0xFFE55A00)
val PrimaryOrangeLight = Color(0xFFFF8533)

// Secondary Colors
val SecondaryBlack = Color(0xFF1A1A1A)
val SecondaryGray = Color(0xFF2D2D2D)
val SecondaryLightGray = Color(0xFF4A4A4A)

// Background Colors
val BackgroundDark = Color(0xFF0D0D0D)
val BackgroundMedium = Color(0xFF1A1A1A)
val BackgroundLight = Color(0xFF2D2D2D)

// Surface Colors
val SurfaceDark = Color(0xFF1A1A1A)
val SurfaceMedium = Color(0xFF2D2D2D)
val SurfaceLight = Color(0xFF4A4A4A)

// Text Colors
val TextPrimary = Color(0xFFFFFFFF)
val TextSecondary = Color(0xFFCCCCCC)
val TextTertiary = Color(0xFF999999)

// Accent Colors
val AccentGreen = Color(0xFF4CAF50)
val AccentRed = Color(0xFFF44336)
val AccentBlue = Color(0xFF2196F3)
val AccentYellow = Color(0xFFFFC107)

// Transparent Colors
val Black50 = Color(0x80000000)
val Black30 = Color(0x4D000000)
val White50 = Color(0x80FFFFFF)
val White30 = Color(0x4DFFFFFF)
