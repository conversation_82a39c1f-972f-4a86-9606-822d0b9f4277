package com.qxyu.yucram.presentation.gallery.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

/**
 * 图库顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GalleryTopBar(
    title: String,
    isSelectionMode: Boolean = false,
    onSortClick: () -> Unit = {},
    onFilterClick: () -> Unit = {},
    onSearchClick: () -> Unit = {},
    onSelectAllClick: () -> Unit = {},
    onClearSelectionClick: () -> Unit = {},
    onDeleteSelectedClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    if (isSelectionMode) {
        // 选择模式的顶部栏
        TopAppBar(
            title = {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Medium
                )
            },
            navigationIcon = {
                IconButton(onClick = onClearSelectionClick) {
                    Icon(
                        imageVector = Icons.Filled.Close,
                        contentDescription = "取消选择"
                    )
                }
            },
            actions = {
                IconButton(onClick = onSelectAllClick) {
                    Icon(
                        imageVector = Icons.Filled.SelectAll,
                        contentDescription = "全选"
                    )
                }
                IconButton(onClick = onDeleteSelectedClick) {
                    Icon(
                        imageVector = Icons.Filled.Delete,
                        contentDescription = "删除",
                        tint = MaterialTheme.colorScheme.error
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ),
            modifier = modifier
        )
    } else {
        // 普通模式的顶部栏
        CenterAlignedTopAppBar(
            title = {
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
            },
            actions = {
                IconButton(onClick = onSearchClick) {
                    Icon(
                        imageVector = Icons.Filled.Search,
                        contentDescription = "搜索"
                    )
                }
                IconButton(onClick = onFilterClick) {
                    Icon(
                        imageVector = Icons.Filled.FilterList,
                        contentDescription = "筛选"
                    )
                }
                IconButton(onClick = onSortClick) {
                    Icon(
                        imageVector = Icons.Filled.Sort,
                        contentDescription = "排序"
                    )
                }
            },
            modifier = modifier
        )
    }
}

/**
 * 搜索栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GallerySearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onClearClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        placeholder = {
            Text("搜索照片...")
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.Filled.Search,
                contentDescription = "搜索"
            )
        },
        trailingIcon = {
            if (query.isNotEmpty()) {
                IconButton(onClick = onClearClick) {
                    Icon(
                        imageVector = Icons.Filled.Clear,
                        contentDescription = "清除"
                    )
                }
            }
        },
        singleLine = true,
        modifier = modifier.fillMaxWidth()
    )
}

/**
 * 排序菜单
 */
@Composable
fun SortMenu(
    expanded: Boolean,
    onDismiss: () -> Unit,
    currentSortOrder: com.qxyu.yucram.domain.model.PhotoSortOrder,
    onSortOrderChange: (com.qxyu.yucram.domain.model.PhotoSortOrder) -> Unit,
    modifier: Modifier = Modifier
) {
    DropdownMenu(
        expanded = expanded,
        onDismissRequest = onDismiss,
        modifier = modifier
    ) {
        val sortOptions = listOf(
            com.qxyu.yucram.domain.model.PhotoSortOrder.DATE_TAKEN_DESC to "拍摄时间 (新到旧)",
            com.qxyu.yucram.domain.model.PhotoSortOrder.DATE_TAKEN_ASC to "拍摄时间 (旧到新)",
            com.qxyu.yucram.domain.model.PhotoSortOrder.DATE_ADDED_DESC to "添加时间 (新到旧)",
            com.qxyu.yucram.domain.model.PhotoSortOrder.DATE_ADDED_ASC to "添加时间 (旧到新)",
            com.qxyu.yucram.domain.model.PhotoSortOrder.NAME_ASC to "文件名 (A-Z)",
            com.qxyu.yucram.domain.model.PhotoSortOrder.NAME_DESC to "文件名 (Z-A)",
            com.qxyu.yucram.domain.model.PhotoSortOrder.SIZE_DESC to "文件大小 (大到小)",
            com.qxyu.yucram.domain.model.PhotoSortOrder.SIZE_ASC to "文件大小 (小到大)"
        )
        
        sortOptions.forEach { (sortOrder, label) ->
            DropdownMenuItem(
                text = {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(label)
                        if (currentSortOrder == sortOrder) {
                            Spacer(modifier = Modifier.weight(1f))
                            Icon(
                                imageVector = Icons.Filled.Check,
                                contentDescription = "已选择",
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(16.dp)
                            )
                        }
                    }
                },
                onClick = {
                    onSortOrderChange(sortOrder)
                    onDismiss()
                }
            )
        }
    }
}

/**
 * 显示模式切换按钮
 */
@Composable
fun DisplayModeToggle(
    currentMode: com.qxyu.yucram.domain.model.PhotoDisplayMode,
    onModeChange: (com.qxyu.yucram.domain.model.PhotoDisplayMode) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        val modes = listOf(
            com.qxyu.yucram.domain.model.PhotoDisplayMode.GRID_SMALL to Icons.Filled.GridView,
            com.qxyu.yucram.domain.model.PhotoDisplayMode.GRID_MEDIUM to Icons.Filled.Apps,
            com.qxyu.yucram.domain.model.PhotoDisplayMode.GRID_LARGE to Icons.Filled.ViewModule,
            com.qxyu.yucram.domain.model.PhotoDisplayMode.LIST to Icons.Filled.List
        )
        
        modes.forEach { (mode, icon) ->
            IconButton(
                onClick = { onModeChange(mode) },
                colors = IconButtonDefaults.iconButtonColors(
                    containerColor = if (currentMode == mode) {
                        MaterialTheme.colorScheme.primaryContainer
                    } else {
                        MaterialTheme.colorScheme.surface
                    }
                )
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = mode.name,
                    tint = if (currentMode == mode) {
                        MaterialTheme.colorScheme.onPrimaryContainer
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
            }
        }
    }
}
