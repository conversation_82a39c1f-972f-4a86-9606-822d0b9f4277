// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.camera;

import android.content.Context;
import com.qxyu.yucram.filter.ImageProcessingPipeline;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CameraManager_Factory implements Factory<CameraManager> {
  private final Provider<Context> contextProvider;

  private final Provider<ImageProcessingPipeline> imageProcessingPipelineProvider;

  public CameraManager_Factory(Provider<Context> contextProvider,
      Provider<ImageProcessingPipeline> imageProcessingPipelineProvider) {
    this.contextProvider = contextProvider;
    this.imageProcessingPipelineProvider = imageProcessingPipelineProvider;
  }

  @Override
  public CameraManager get() {
    return newInstance(contextProvider.get(), imageProcessingPipelineProvider.get());
  }

  public static CameraManager_Factory create(Provider<Context> contextProvider,
      Provider<ImageProcessingPipeline> imageProcessingPipelineProvider) {
    return new CameraManager_Factory(contextProvider, imageProcessingPipelineProvider);
  }

  public static CameraManager newInstance(Context context,
      ImageProcessingPipeline imageProcessingPipeline) {
    return new CameraManager(context, imageProcessingPipeline);
  }
}
