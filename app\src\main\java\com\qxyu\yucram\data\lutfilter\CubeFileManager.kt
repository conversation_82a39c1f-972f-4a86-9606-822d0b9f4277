package com.qxyu.yucram.data.lutfilter

import android.content.Context
import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.InputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * CUBE文件管理器
 * 负责管理和解析CUBE LUT文件
 */
@Singleton
class CubeFileManager @Inject constructor(
    private val context: Context
) {
    
    companion object {
        private const val CUBE_ASSETS_DIR = "luts"
        private const val CUBE_INTERNAL_DIR = "luts"
    }
    
    /**
     * 初始化内置CUBE文件
     */
    suspend fun initializeBuiltInCubeFiles(): List<LutFilter> = withContext(Dispatchers.IO) {
        val filters = mutableListOf<LutFilter>()
        
        // 从assets目录加载所有CUBE文件
        val cubeFiles = listOf(
            // 英文CUBE文件
            "Candy color style.cube" to CubeFilterInfo(
                displayName = "糖果色彩",
                description = "甜美糖果色彩风格，明亮饱和的色调",
                category = LutCategory.BRIGHT,
                author = "Yucram Studio"
            ),
            "HasselbladBlueFilm_Booz.cube" to CubeFilterInfo(
                displayName = "哈苏蓝调胶片",
                description = "经典哈苏相机蓝色胶片风格",
                category = LutCategory.VINTAGE,
                author = "Hasselblad"
            ),
            "TXD Grainy Film.cube" to CubeFilterInfo(
                displayName = "TXD颗粒胶片",
                description = "复古颗粒质感胶片效果",
                category = LutCategory.VINTAGE,
                author = "TXD Studio"
            ),
            "fuji.nc.cube" to CubeFilterInfo(
                displayName = "富士胶片",
                description = "经典富士胶片色彩风格",
                category = LutCategory.CINEMATIC,
                author = "Fujifilm"
            ),

            // 中文CUBE文件
            "夏日.cube" to CubeFilterInfo(
                displayName = "夏日清新",
                description = "夏日清新明亮的色彩风格",
                category = LutCategory.BRIGHT,
                author = "Yucram Studio"
            ),
            "清新美食.cube" to CubeFilterInfo(
                displayName = "清新美食",
                description = "专为美食摄影设计的清新色调",
                category = LutCategory.BRIGHT,
                author = "Yucram Studio"
            ),
            "温暖乡村.cube" to CubeFilterInfo(
                displayName = "温暖乡村",
                description = "温暖的乡村田园风格色调",
                category = LutCategory.WARM,
                author = "Yucram Studio"
            ),
            "灰度电影.cube" to CubeFilterInfo(
                displayName = "灰度电影",
                description = "经典黑白电影风格",
                category = LutCategory.MONOCHROME,
                author = "Yucram Studio"
            ),
            "白色负片.cube" to CubeFilterInfo(
                displayName = "白色负片",
                description = "白色负片胶片效果",
                category = LutCategory.VINTAGE,
                author = "Yucram Studio"
            ),
            "稻田胶片.cube" to CubeFilterInfo(
                displayName = "稻田胶片",
                description = "金黄稻田胶片风格",
                category = LutCategory.WARM,
                author = "Yucram Studio"
            ),
            "胶片印象.cube" to CubeFilterInfo(
                displayName = "胶片印象",
                description = "经典胶片印象风格",
                category = LutCategory.VINTAGE,
                author = "Yucram Studio"
            ),
            "胶片复古.cube" to CubeFilterInfo(
                displayName = "胶片复古",
                description = "复古胶片色彩风格",
                category = LutCategory.VINTAGE,
                author = "Yucram Studio"
            ),
            "金色稻田.cube" to CubeFilterInfo(
                displayName = "金色稻田",
                description = "金色稻田暖色调风格",
                category = LutCategory.WARM,
                author = "Yucram Studio"
            ),
            "露营氛围.cube" to CubeFilterInfo(
                displayName = "露营氛围",
                description = "户外露营氛围色调",
                category = LutCategory.MOODY,
                author = "Yucram Studio"
            ),
            "黄昏氛围.cube" to CubeFilterInfo(
                displayName = "黄昏氛围",
                description = "温暖黄昏时光氛围",
                category = LutCategory.WARM,
                author = "Yucram Studio"
            )
        )
        
        cubeFiles.forEach { (fileName, info) ->
            try {
                val filter = createLutFilterFromCube(fileName, info)
                if (filter != null) {
                    filters.add(filter)
                }
            } catch (e: Exception) {
                // 记录错误但继续处理其他文件
                println("Failed to load CUBE file: $fileName, error: ${e.message}")
            }
        }
        
        filters
    }
    
    /**
     * 从CUBE文件创建LUT滤镜
     */
    private suspend fun createLutFilterFromCube(
        fileName: String,
        info: CubeFilterInfo
    ): LutFilter? = withContext(Dispatchers.IO) {
        try {
            // 复制CUBE文件到内部存储
            val internalFile = copyToInternalStorage(fileName)
            if (internalFile == null || !internalFile.exists()) {
                return@withContext null
            }
            
            // 解析CUBE文件
            val cubeData = parseCubeFile(internalFile)
            if (cubeData == null) {
                return@withContext null
            }
            
            // 生成滤镜ID
            val filterId = generateFilterId(fileName)
            
            // 创建LUT滤镜对象
            LutFilter(
                id = filterId,
                name = fileName.substringBeforeLast("."),
                displayName = info.displayName,
                description = info.description,
                category = info.category,
                lutFilePath = internalFile.absolutePath,
                previewImagePath = null, // 可以后续生成预览图
                thumbnailPath = null,
                isBuiltIn = true,
                isPremium = false,
                lutSize = cubeData.size,
                lutFormat = LutFormat.CUBE,
                author = info.author,
                tags = listOf(info.category.displayName, "胶片", "专业"),
                version = "1.0"
            )
        } catch (e: Exception) {
            println("Error creating LUT filter from CUBE: ${e.message}")
            null
        }
    }
    
    /**
     * 复制CUBE文件到内部存储
     */
    private fun copyToInternalStorage(fileName: String): File? {
        try {
            // 创建内部存储目录
            val lutDir = File(context.filesDir, CUBE_INTERNAL_DIR)
            if (!lutDir.exists()) {
                lutDir.mkdirs()
            }

            val targetFile = File(lutDir, fileName)

            // 如果文件已存在，直接返回
            if (targetFile.exists()) {
                return targetFile
            }

            // 从assets目录读取文件
            try {
                context.assets.open("$CUBE_ASSETS_DIR/$fileName").use { inputStream ->
                    targetFile.outputStream().use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                println("Successfully copied CUBE file: $fileName")
                return targetFile
            } catch (e: Exception) {
                println("Error copying CUBE file from assets: $fileName, error: ${e.message}")
                return null
            }
        } catch (e: Exception) {
            println("Error copying CUBE file: ${e.message}")
            return null
        }
    }
    
    /**
     * 解析CUBE文件
     */
    private fun parseCubeFile(file: File): CubeData? {
        try {
            val lines = file.readLines()
            var title = ""
            var size = 0
            var domainMin = floatArrayOf(0f, 0f, 0f)
            var domainMax = floatArrayOf(1f, 1f, 1f)
            val lutData = mutableListOf<FloatArray>()
            
            for (line in lines) {
                val trimmedLine = line.trim()
                
                when {
                    trimmedLine.startsWith("TITLE") -> {
                        title = trimmedLine.substringAfter("TITLE").trim().trim('"')
                    }
                    
                    trimmedLine.startsWith("LUT_3D_SIZE") -> {
                        size = trimmedLine.substringAfter("LUT_3D_SIZE").trim().toIntOrNull() ?: 0
                    }
                    
                    trimmedLine.startsWith("DOMAIN_MIN") -> {
                        val values = trimmedLine.substringAfter("DOMAIN_MIN").trim().split("\\s+".toRegex())
                        if (values.size >= 3) {
                            domainMin = floatArrayOf(
                                values[0].toFloatOrNull() ?: 0f,
                                values[1].toFloatOrNull() ?: 0f,
                                values[2].toFloatOrNull() ?: 0f
                            )
                        }
                    }
                    
                    trimmedLine.startsWith("DOMAIN_MAX") -> {
                        val values = trimmedLine.substringAfter("DOMAIN_MAX").trim().split("\\s+".toRegex())
                        if (values.size >= 3) {
                            domainMax = floatArrayOf(
                                values[0].toFloatOrNull() ?: 1f,
                                values[1].toFloatOrNull() ?: 1f,
                                values[2].toFloatOrNull() ?: 1f
                            )
                        }
                    }
                    
                    trimmedLine.matches("^[0-9.-]+\\s+[0-9.-]+\\s+[0-9.-]+$".toRegex()) -> {
                        // LUT数据行
                        val values = trimmedLine.split("\\s+".toRegex())
                        if (values.size >= 3) {
                            val rgb = floatArrayOf(
                                values[0].toFloatOrNull() ?: 0f,
                                values[1].toFloatOrNull() ?: 0f,
                                values[2].toFloatOrNull() ?: 0f
                            )
                            lutData.add(rgb)
                        }
                    }
                }
            }
            
            // 验证数据完整性
            val expectedDataSize = size * size * size
            if (size > 0 && lutData.size == expectedDataSize) {
                return CubeData(
                    title = title,
                    size = when (size) {
                        16 -> LutSize.SIZE_16
                        32 -> LutSize.SIZE_32
                        64 -> LutSize.SIZE_64
                        128 -> LutSize.SIZE_128
                        else -> LutSize.SIZE_32
                    },
                    domainMin = domainMin,
                    domainMax = domainMax,
                    lutData = lutData.toTypedArray()
                )
            }
            
            return null
        } catch (e: Exception) {
            println("Error parsing CUBE file: ${e.message}")
            return null
        }
    }
    
    /**
     * 生成滤镜ID
     */
    private fun generateFilterId(fileName: String): String {
        return "cube_${fileName.substringBeforeLast(".").replace(" ", "_").lowercase()}"
    }
    
    /**
     * 获取所有可用的CUBE文件
     */
    suspend fun getAvailableCubeFiles(): List<String> = withContext(Dispatchers.IO) {
        val cubeFiles = mutableListOf<String>()
        
        // 检查内部存储
        val lutDir = File(context.filesDir, CUBE_INTERNAL_DIR)
        if (lutDir.exists()) {
            lutDir.listFiles { file -> file.extension.lowercase() == "cube" }
                ?.forEach { file ->
                    cubeFiles.add(file.name)
                }
        }
        
        // 检查assets目录
        try {
            context.assets.list(CUBE_ASSETS_DIR)?.forEach { fileName ->
                if (fileName.endsWith(".cube", ignoreCase = true)) {
                    cubeFiles.add(fileName)
                }
            }
        } catch (e: Exception) {
            // assets目录可能不存在
        }
        
        cubeFiles.distinct()
    }
    
    /**
     * 导入外部CUBE文件
     */
    suspend fun importCubeFile(
        inputStream: InputStream,
        fileName: String,
        displayName: String,
        description: String,
        category: LutCategory
    ): Result<LutFilter> = withContext(Dispatchers.IO) {
        try {
            // 创建内部存储目录
            val lutDir = File(context.filesDir, CUBE_INTERNAL_DIR)
            if (!lutDir.exists()) {
                lutDir.mkdirs()
            }
            
            val targetFile = File(lutDir, fileName)
            
            // 复制文件
            targetFile.outputStream().use { outputStream ->
                inputStream.copyTo(outputStream)
            }
            
            // 解析文件
            val cubeData = parseCubeFile(targetFile)
            if (cubeData == null) {
                targetFile.delete()
                return@withContext Result.failure(Exception("Invalid CUBE file format"))
            }
            
            // 创建LUT滤镜
            val filter = LutFilter(
                id = generateFilterId(fileName),
                name = fileName.substringBeforeLast("."),
                displayName = displayName,
                description = description,
                category = category,
                lutFilePath = targetFile.absolutePath,
                isBuiltIn = false,
                isPremium = false,
                lutSize = cubeData.size,
                lutFormat = LutFormat.CUBE,
                tags = listOf(category.displayName, "用户导入"),
                version = "1.0"
            )
            
            Result.success(filter)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

/**
 * CUBE滤镜信息
 */
data class CubeFilterInfo(
    val displayName: String,
    val description: String,
    val category: LutCategory,
    val author: String? = null
)

/**
 * CUBE数据
 */
data class CubeData(
    val title: String,
    val size: LutSize,
    val domainMin: FloatArray,
    val domainMax: FloatArray,
    val lutData: Array<FloatArray>
)
