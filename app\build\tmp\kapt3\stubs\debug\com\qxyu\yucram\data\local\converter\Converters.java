package com.qxyu.yucram.data.local.converter;

/**
 * Room数据库类型转换器
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0016\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0014\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0007J\u0014\u0010\t\u001a\u0004\u0018\u00010\u00062\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0007J\u0014\u0010\f\u001a\u0004\u0018\u00010\u00062\b\u0010\r\u001a\u0004\u0018\u00010\u000eH\u0007J\u0014\u0010\u000f\u001a\u0004\u0018\u00010\u00062\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0007J\u0014\u0010\u0012\u001a\u0004\u0018\u00010\u00062\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u0007J\u0014\u0010\u0015\u001a\u0004\u0018\u00010\u00062\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0007J\u0014\u0010\u0018\u001a\u0004\u0018\u00010\u00062\b\u0010\u0019\u001a\u0004\u0018\u00010\u001aH\u0007J\u0014\u0010\u001b\u001a\u0004\u0018\u00010\u00062\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0007J\u0014\u0010\u001e\u001a\u0004\u0018\u00010\u00062\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u0007J\u0014\u0010!\u001a\u0004\u0018\u00010\u00062\b\u0010\"\u001a\u0004\u0018\u00010#H\u0007J\u001a\u0010$\u001a\u0004\u0018\u00010\u00062\u000e\u0010%\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010&H\u0007J\u0014\u0010\'\u001a\u0004\u0018\u00010\b2\b\u0010(\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u0010)\u001a\u0004\u0018\u00010\u000b2\b\u0010*\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u0010+\u001a\u0004\u0018\u00010\u000e2\b\u0010,\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u0010-\u001a\u0004\u0018\u00010\u00112\b\u0010.\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u0010/\u001a\u0004\u0018\u00010\u00142\b\u00100\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u00101\u001a\u0004\u0018\u00010\u00172\b\u00102\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u00103\u001a\u0004\u0018\u00010\u001a2\b\u00104\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u00105\u001a\u0004\u0018\u00010\u001d2\b\u00106\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u00107\u001a\u0004\u0018\u00010 2\b\u00108\u001a\u0004\u0018\u00010\u0006H\u0007J\u0014\u00109\u001a\u0004\u0018\u00010#2\b\u0010:\u001a\u0004\u0018\u00010\u0006H\u0007J\u0018\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00060&2\b\u0010%\u001a\u0004\u0018\u00010\u0006H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006<"}, d2 = {"Lcom/qxyu/yucram/data/local/converter/Converters;", "", "()V", "gson", "Lcom/google/gson/Gson;", "fromAlbumColor", "", "color", "Lcom/qxyu/yucram/domain/model/AlbumColor;", "fromAlbumType", "albumType", "Lcom/qxyu/yucram/domain/model/AlbumType;", "fromExifData", "exifData", "Lcom/qxyu/yucram/domain/model/ExifData;", "fromImageSourceFormat", "format", "Lcom/qxyu/yucram/domain/model/ImageSourceFormat;", "fromLocalDateTime", "dateTime", "Ljava/time/LocalDateTime;", "fromPhotoAdjustments", "adjustments", "Lcom/qxyu/yucram/domain/model/PhotoAdjustments;", "fromPhotoLocation", "location", "Lcom/qxyu/yucram/domain/model/PhotoLocation;", "fromPhotoSortOrder", "sortOrder", "Lcom/qxyu/yucram/domain/model/PhotoSortOrder;", "fromProcessingInfo", "processingInfo", "Lcom/qxyu/yucram/domain/model/ProcessingInfo;", "fromSmartAlbumRuleType", "ruleType", "Lcom/qxyu/yucram/domain/model/SmartAlbumRuleType;", "fromStringList", "value", "", "toAlbumColor", "colorString", "toAlbumType", "albumTypeString", "toExifData", "exifDataString", "toImageSourceFormat", "formatString", "toLocalDateTime", "dateTimeString", "toPhotoAdjustments", "adjustmentsString", "toPhotoLocation", "locationString", "toPhotoSortOrder", "sortOrderString", "toProcessingInfo", "processingInfoString", "toSmartAlbumRuleType", "ruleTypeString", "toStringList", "app_debug"})
public final class Converters {
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    
    public Converters() {
        super();
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromLocalDateTime(@org.jetbrains.annotations.Nullable()
    java.time.LocalDateTime dateTime) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.time.LocalDateTime toLocalDateTime(@org.jetbrains.annotations.Nullable()
    java.lang.String dateTimeString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromStringList(@org.jetbrains.annotations.Nullable()
    java.util.List<java.lang.String> value) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> toStringList(@org.jetbrains.annotations.Nullable()
    java.lang.String value) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromPhotoLocation(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoLocation location) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoLocation toPhotoLocation(@org.jetbrains.annotations.Nullable()
    java.lang.String locationString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromExifData(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ExifData exifData) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ExifData toExifData(@org.jetbrains.annotations.Nullable()
    java.lang.String exifDataString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromProcessingInfo(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ProcessingInfo processingInfo) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ProcessingInfo toProcessingInfo(@org.jetbrains.annotations.Nullable()
    java.lang.String processingInfoString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromImageSourceFormat(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.ImageSourceFormat format) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.ImageSourceFormat toImageSourceFormat(@org.jetbrains.annotations.Nullable()
    java.lang.String formatString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromPhotoAdjustments(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoAdjustments adjustments) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoAdjustments toPhotoAdjustments(@org.jetbrains.annotations.Nullable()
    java.lang.String adjustmentsString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromAlbumType(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.AlbumType albumType) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.AlbumType toAlbumType(@org.jetbrains.annotations.Nullable()
    java.lang.String albumTypeString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromAlbumColor(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.AlbumColor color) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.AlbumColor toAlbumColor(@org.jetbrains.annotations.Nullable()
    java.lang.String colorString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromPhotoSortOrder(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoSortOrder sortOrder) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoSortOrder toPhotoSortOrder(@org.jetbrains.annotations.Nullable()
    java.lang.String sortOrderString) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String fromSmartAlbumRuleType(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.SmartAlbumRuleType ruleType) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.SmartAlbumRuleType toSmartAlbumRuleType(@org.jetbrains.annotations.Nullable()
    java.lang.String ruleTypeString) {
        return null;
    }
}