<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.Yucram" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary_orange</item>
        <item name="colorPrimaryDark">@color/primary_orange_dark</item>
        <item name="colorAccent">@color/primary_orange_light</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        <!-- Status bar and navigation bar -->
        <item name="android:statusBarColor">@color/status_bar</item>
        <item name="android:navigationBarColor">@color/navigation_bar</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- Full screen settings -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style>

    <style name="Theme.Yucram.Splash" parent="Theme.Yucram">
        <item name="android:windowBackground">@color/background_dark</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
    </style>
</resources>
