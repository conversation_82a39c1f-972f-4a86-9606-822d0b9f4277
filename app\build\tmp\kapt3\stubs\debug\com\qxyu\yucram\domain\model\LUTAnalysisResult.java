package com.qxyu.yucram.domain.model;

/**
 * LUT分析结果
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b.\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0099\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\u0014\b\u0002\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\f\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0014\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0002\u0010\u001aJ\t\u00101\u001a\u00020\u0003H\u00c6\u0003J\t\u00102\u001a\u00020\u0014H\u00c6\u0003J\t\u00103\u001a\u00020\u0014H\u00c6\u0003J\t\u00104\u001a\u00020\u0014H\u00c6\u0003J\t\u00105\u001a\u00020\u0014H\u00c6\u0003J\u000b\u00106\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\t\u00107\u001a\u00020\u0005H\u00c6\u0003J\t\u00108\u001a\u00020\u0007H\u00c6\u0003J\t\u00109\u001a\u00020\tH\u00c6\u0003J\t\u0010:\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010;\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\fH\u00c6\u0003J\t\u0010<\u001a\u00020\u000fH\u00c6\u0003J\u000f\u0010=\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011H\u00c6\u0003J\t\u0010>\u001a\u00020\u0014H\u00c6\u0003J\u00a9\u0001\u0010?\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u00032\u0014\b\u0002\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\u000e\b\u0002\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u00112\b\b\u0002\u0010\u0013\u001a\u00020\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u00142\b\b\u0002\u0010\u0016\u001a\u00020\u00142\b\b\u0002\u0010\u0017\u001a\u00020\u00142\b\b\u0002\u0010\u0018\u001a\u00020\u00142\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\rH\u00c6\u0001J\u0013\u0010@\u001a\u00020\u00032\b\u0010A\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010B\u001a\u00020CH\u00d6\u0001J\t\u0010D\u001a\u00020\rH\u00d6\u0001R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u0015\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001cR\u0011\u0010\u0016\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001cR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010$R\u0013\u0010\u0019\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010*R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010,R\u001d\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.R\u0011\u0010\u0017\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u001cR\u0011\u0010\u0018\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\u001c\u00a8\u0006E"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTAnalysisResult;", "", "isValid", "", "lutSize", "Lcom/qxyu/yucram/domain/model/LUTSize;", "format", "Lcom/qxyu/yucram/domain/model/LUTFormat;", "colorProfile", "Lcom/qxyu/yucram/domain/model/ColorProfile;", "hasMetadata", "metadata", "", "", "colorRange", "Lcom/qxyu/yucram/domain/model/ColorRange;", "dominantColors", "", "Landroidx/compose/ui/graphics/Color;", "averageBrightness", "", "averageContrast", "averageSaturation", "temperatureBias", "tintBias", "errorMessage", "(ZLcom/qxyu/yucram/domain/model/LUTSize;Lcom/qxyu/yucram/domain/model/LUTFormat;Lcom/qxyu/yucram/domain/model/ColorProfile;ZLjava/util/Map;Lcom/qxyu/yucram/domain/model/ColorRange;Ljava/util/List;FFFFFLjava/lang/String;)V", "getAverageBrightness", "()F", "getAverageContrast", "getAverageSaturation", "getColorProfile", "()Lcom/qxyu/yucram/domain/model/ColorProfile;", "getColorRange", "()Lcom/qxyu/yucram/domain/model/ColorRange;", "getDominantColors", "()Ljava/util/List;", "getErrorMessage", "()Ljava/lang/String;", "getFormat", "()Lcom/qxyu/yucram/domain/model/LUTFormat;", "getHasMetadata", "()Z", "getLutSize", "()Lcom/qxyu/yucram/domain/model/LUTSize;", "getMetadata", "()Ljava/util/Map;", "getTemperatureBias", "getTintBias", "component1", "component10", "component11", "component12", "component13", "component14", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class LUTAnalysisResult {
    private final boolean isValid = false;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LUTSize lutSize = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LUTFormat format = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ColorProfile colorProfile = null;
    private final boolean hasMetadata = false;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.String> metadata = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ColorRange colorRange = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<androidx.compose.ui.graphics.Color> dominantColors = null;
    private final float averageBrightness = 0.0F;
    private final float averageContrast = 0.0F;
    private final float averageSaturation = 0.0F;
    private final float temperatureBias = 0.0F;
    private final float tintBias = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String errorMessage = null;
    
    public LUTAnalysisResult(boolean isValid, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTSize lutSize, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFormat format, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorProfile colorProfile, boolean hasMetadata, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> metadata, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorRange colorRange, @org.jetbrains.annotations.NotNull()
    java.util.List<androidx.compose.ui.graphics.Color> dominantColors, float averageBrightness, float averageContrast, float averageSaturation, float temperatureBias, float tintBias, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        super();
    }
    
    public final boolean isValid() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTSize getLutSize() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFormat getFormat() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorProfile getColorProfile() {
        return null;
    }
    
    public final boolean getHasMetadata() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> getMetadata() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorRange getColorRange() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<androidx.compose.ui.graphics.Color> getDominantColors() {
        return null;
    }
    
    public final float getAverageBrightness() {
        return 0.0F;
    }
    
    public final float getAverageContrast() {
        return 0.0F;
    }
    
    public final float getAverageSaturation() {
        return 0.0F;
    }
    
    public final float getTemperatureBias() {
        return 0.0F;
    }
    
    public final float getTintBias() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getErrorMessage() {
        return null;
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final float component10() {
        return 0.0F;
    }
    
    public final float component11() {
        return 0.0F;
    }
    
    public final float component12() {
        return 0.0F;
    }
    
    public final float component13() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTSize component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFormat component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorProfile component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.String> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ColorRange component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<androidx.compose.ui.graphics.Color> component8() {
        return null;
    }
    
    public final float component9() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTAnalysisResult copy(boolean isValid, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTSize lutSize, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFormat format, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorProfile colorProfile, boolean hasMetadata, @org.jetbrains.annotations.NotNull()
    java.util.Map<java.lang.String, java.lang.String> metadata, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ColorRange colorRange, @org.jetbrains.annotations.NotNull()
    java.util.List<androidx.compose.ui.graphics.Color> dominantColors, float averageBrightness, float averageContrast, float averageSaturation, float temperatureBias, float tintBias, @org.jetbrains.annotations.Nullable()
    java.lang.String errorMessage) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}