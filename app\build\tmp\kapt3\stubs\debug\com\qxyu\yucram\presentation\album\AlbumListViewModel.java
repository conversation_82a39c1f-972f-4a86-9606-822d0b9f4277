package com.qxyu.yucram.presentation.album;

/**
 * 相册列表ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0015J\u0016\u0010\u0017\u001a\u00020\u00152\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0019J\u0006\u0010\u001b\u001a\u00020\u0015J\u000e\u0010\u001c\u001a\u00020\u00152\u0006\u0010\u001d\u001a\u00020\u0019R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00070\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\rR\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\r\u00a8\u0006\u001e"}, d2 = {"Lcom/qxyu/yucram/presentation/album/AlbumListViewModel;", "Landroidx/lifecycle/ViewModel;", "albumRepository", "Lcom/qxyu/yucram/domain/repository/AlbumRepository;", "(Lcom/qxyu/yucram/domain/repository/AlbumRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/presentation/album/AlbumListUiState;", "albums", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/qxyu/yucram/domain/model/Album;", "getAlbums", "()Lkotlinx/coroutines/flow/StateFlow;", "systemAlbums", "getSystemAlbums", "uiState", "getUiState", "userAlbums", "getUserAlbums", "clearError", "", "clearMessage", "createAlbum", "name", "", "description", "loadAlbums", "searchAlbums", "query", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AlbumListViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.AlbumRepository albumRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.album.AlbumListUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.album.AlbumListUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> albums = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> systemAlbums = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> userAlbums = null;
    
    @javax.inject.Inject()
    public AlbumListViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.AlbumRepository albumRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.album.AlbumListUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> getAlbums() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> getSystemAlbums() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.Album>> getUserAlbums() {
        return null;
    }
    
    public final void loadAlbums() {
    }
    
    public final void createAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description) {
    }
    
    public final void searchAlbums(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}