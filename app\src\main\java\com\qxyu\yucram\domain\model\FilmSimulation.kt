package com.qxyu.yucram.domain.model

import androidx.annotation.FloatRange
import kotlinx.serialization.Serializable

/**
 * 滤镜预设数据模型
 */
@Serializable
data class FilterPreset(
    val id: String,
    val name: String,
    val lutFilePath: String,
    val iconPath: String,
    val isBuiltIn: Boolean = true,
    val postProcessingParams: PostProcessingParams = PostProcessingParams(),
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 后处理参数
 */
@Serializable
data class PostProcessingParams(
    @FloatRange(from = -1.0, to = 1.0)
    val highlights: Float = 0.0f,      // 高光 -1.0 ~ +1.0

    @FloatRange(from = -1.0, to = 1.0)
    val shadows: Float = 0.0f,         // 阴影 -1.0 ~ +1.0

    @FloatRange(from = 0.0, to = 1.0)
    val vignette: Float = 0.0f,        // 暗角 0.0 ~ 1.0

    @FloatRange(from = 0.0, to = 1.0)
    val chromaAberration: Float = 0.0f, // 色散 0.0 ~ 1.0

    @FloatRange(from = 0.0, to = 1.0)
    val grain: Float = 0.0f,           // 颗粒 0.0 ~ 1.0

    @FloatRange(from = -1.0, to = 1.0)
    val sharpness: Float = 0.0f        // 锐度 -1.0 ~ +1.0
)

/**
 * 图像源格式
 */
enum class ImageSourceFormat(val displayName: String) {
    RAW("RAW格式"),
    LOG("LOG静帧"),
    JPEG("JPEG格式")
}

/**
 * LOG能力支持
 */
enum class LogCapability(val displayName: String) {
    NONE("不支持"),
    OPPO_LOG("OPPO LOG"),
    SONY_SLOG("Sony S-Log"),
    CANON_LOG("Canon Log"),
    PANASONIC_VLOG("Panasonic V-Log")
}

/**
 * LUT数据模型
 */
@Serializable
data class LutData(
    val id: String,
    val name: String,
    val size: Int,              // 17, 33, 65 等
    val format: LutFormat,
    val data: FloatArray,
    val filePath: String
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as LutData

        if (id != other.id) return false
        if (name != other.name) return false
        if (size != other.size) return false
        if (format != other.format) return false
        if (!data.contentEquals(other.data)) return false
        if (filePath != other.filePath) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + size
        result = 31 * result + format.hashCode()
        result = 31 * result + data.contentHashCode()
        result = 31 * result + filePath.hashCode()
        return result
    }
}

/**
 * LUT信息
 */
@Serializable
data class LutInfo(
    val id: String,
    val name: String,
    val description: String,
    val size: Int,
    val format: LutFormat,
    val fileSize: Long,
    val checksum: String
)

/**
 * LUT格式
 */
enum class LutFormat(val extension: String, val displayName: String) {
    CUBE(".cube", "CUBE文件"),
    THREE_DL(".3dl", "3DL文件"),
    LUT(".lut", "LUT文件"),
    PNG_LUT(".png", "PNG LUT")
}

/**
 * RAW处理参数
 */
@Serializable
data class RawProcessingParams(
    val whiteBalance: WhiteBalanceParams = WhiteBalanceParams(),
    val exposure: ExposureParams = ExposureParams(),
    val toneMapping: ToneMappingParams = ToneMappingParams(),
    val colorGrading: ColorGradingParams = ColorGradingParams(),
    val noiseReduction: NoiseReductionParams = NoiseReductionParams(),
    val sharpening: SharpeningParams = SharpeningParams()
)

/**
 * LOG处理参数
 */
@Serializable
data class LogProcessingParams(
    val logCurve: LogCurve = LogCurve.LOG_C,
    val colorSpace: ColorSpace = ColorSpace.SRGB,
    val gammaCorrection: Float = 2.2f,
    val colorGrading: ColorGradingParams = ColorGradingParams()
)

/**
 * 白平衡参数
 */
@Serializable
data class WhiteBalanceParams(
    val temperature: Float = 0.0f,    // -1.0 ~ +1.0
    val tint: Float = 0.0f           // -1.0 ~ +1.0
)

/**
 * 曝光参数
 */
@Serializable
data class ExposureParams(
    val exposure: Float = 0.0f,       // -2.0 ~ +2.0
    val highlights: Float = 0.0f,     // -1.0 ~ +1.0
    val shadows: Float = 0.0f,        // -1.0 ~ +1.0
    val whites: Float = 0.0f,         // -1.0 ~ +1.0
    val blacks: Float = 0.0f          // -1.0 ~ +1.0
)

/**
 * 色调映射参数
 */
@Serializable
data class ToneMappingParams(
    val method: ToneMappingMethod = ToneMappingMethod.REINHARD,
    val exposure: Float = 0.0f,
    val gamma: Float = 2.2f,
    val whitePoint: Float = 1.0f
)

/**
 * 色彩分级参数
 */
@Serializable
data class ColorGradingParams(
    val shadows: ColorWheelParams = ColorWheelParams(),
    val midtones: ColorWheelParams = ColorWheelParams(),
    val highlights: ColorWheelParams = ColorWheelParams(),
    val globalSaturation: Float = 0.0f,
    val globalContrast: Float = 0.0f
)

/**
 * 色轮参数
 */
@Serializable
data class ColorWheelParams(
    val hue: Float = 0.0f,           // -180 ~ +180
    val saturation: Float = 0.0f,    // -1.0 ~ +1.0
    val luminance: Float = 0.0f      // -1.0 ~ +1.0
)

/**
 * 降噪参数
 */
@Serializable
data class NoiseReductionParams(
    val luminanceNoise: Float = 0.0f,    // 0.0 ~ 1.0
    val colorNoise: Float = 0.0f,        // 0.0 ~ 1.0
    val detail: Float = 0.5f,            // 0.0 ~ 1.0
    val contrast: Float = 0.5f           // 0.0 ~ 1.0
)

/**
 * 锐化参数
 */
@Serializable
data class SharpeningParams(
    val amount: Float = 0.0f,        // 0.0 ~ 2.0
    val radius: Float = 1.0f,        // 0.5 ~ 3.0
    val detail: Float = 0.5f,        // 0.0 ~ 1.0
    val masking: Float = 0.0f        // 0.0 ~ 1.0
)

/**
 * 色彩空间
 */
enum class ColorSpace(val displayName: String) {
    SRGB("sRGB"),
    ADOBE_RGB("Adobe RGB"),
    PROPHOTO_RGB("ProPhoto RGB"),
    REC2020("Rec. 2020"),
    DCI_P3("DCI-P3")
}

/**
 * LOG曲线
 */
enum class LogCurve(val displayName: String) {
    LOG_C("Canon Log C"),
    S_LOG2("Sony S-Log2"),
    S_LOG3("Sony S-Log3"),
    V_LOG("Panasonic V-Log"),
    CUSTOM("自定义")
}

/**
 * 色调映射方法
 */
enum class ToneMappingMethod(val displayName: String) {
    REINHARD("Reinhard"),
    FILMIC("Filmic"),
    ACES("ACES"),
    UNCHARTED2("Uncharted 2"),
    CUSTOM("自定义")
}

/**
 * 处理后的图像
 */
data class ProcessedImage(
    val data: ByteArray,
    val width: Int,
    val height: Int,
    val format: ProcessedImageFormat,
    val colorSpace: ColorSpace,
    val metadata: ImageMetadata? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ProcessedImage

        if (!data.contentEquals(other.data)) return false
        if (width != other.width) return false
        if (height != other.height) return false
        if (format != other.format) return false
        if (colorSpace != other.colorSpace) return false
        if (metadata != other.metadata) return false

        return true
    }

    override fun hashCode(): Int {
        var result = data.contentHashCode()
        result = 31 * result + width
        result = 31 * result + height
        result = 31 * result + format.hashCode()
        result = 31 * result + colorSpace.hashCode()
        result = 31 * result + (metadata?.hashCode() ?: 0)
        return result
    }
}

/**
 * 图像格式
 */
enum class ProcessedImageFormat(val mimeType: String) {
    JPEG("image/jpeg"),
    PNG("image/png"),
    WEBP("image/webp"),
    RAW_DNG("image/x-adobe-dng"),
    TIFF("image/tiff")
}

/**
 * 图像元数据
 */
@Serializable
data class ImageMetadata(
    val exif: Map<String, String> = emptyMap(),
    val processingHistory: List<String> = emptyList(),
    val filmPresetUsed: String? = null,
    val lutApplied: String? = null
)
