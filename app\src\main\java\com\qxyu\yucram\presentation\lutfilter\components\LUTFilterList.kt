package com.qxyu.yucram.presentation.lutfilter.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.LUTFilter

/**
 * LUT滤镜列表组件
 */
@Composable
fun LUTFilterList(
    filters: List<LUTFilter>,
    selectedFilter: LUTFilter?,
    onFilterSelected: (LUTFilter) -> Unit,
    onFilterLongPress: (LUTFilter) -> Unit = {},
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier
            .background(Color.Black.copy(alpha = 0.8f))
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        // 无滤镜选项
        item {
            NoFilterItem(
                isSelected = selectedFilter == null,
                onClick = { onFilterSelected(LUTFilter.NONE) }
            )
        }
        
        // 滤镜列表
        items(filters) { filter ->
            LUTFilterItem(
                filter = filter,
                isSelected = selectedFilter?.id == filter.id,
                onClick = { onFilterSelected(filter) },
                onLongClick = { onFilterLongPress(filter) }
            )
        }
    }
}

/**
 * 无滤镜选项
 */
@Composable
fun NoFilterItem(
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .width(80.dp)
            .clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(
                    if (isSelected) MaterialTheme.colorScheme.primary
                    else Color.White.copy(alpha = 0.1f)
                )
                .border(
                    width = if (isSelected) 2.dp else 1.dp,
                    color = if (isSelected) MaterialTheme.colorScheme.primary
                    else Color.White.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(8.dp)
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Filled.Block,
                contentDescription = "无滤镜",
                tint = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
                modifier = Modifier.size(32.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = "原图",
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

/**
 * LUT滤镜项
 */
@Composable
fun LUTFilterItem(
    filter: LUTFilter,
    isSelected: Boolean,
    onClick: () -> Unit,
    onLongClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .width(80.dp)
            .clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(80.dp)
                .clip(RoundedCornerShape(8.dp))
                .border(
                    width = if (isSelected) 3.dp else 1.dp,
                    color = if (isSelected) MaterialTheme.colorScheme.primary
                    else Color.White.copy(alpha = 0.3f),
                    shape = RoundedCornerShape(8.dp)
                )
        ) {
            // 滤镜预览图
            if (filter.thumbnailPath != null) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(filter.thumbnailPath)
                        .crossfade(true)
                        .build(),
                    contentDescription = filter.displayName,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                // 默认滤镜图标
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            getFilterCategoryColor(filter.category).copy(alpha = 0.3f)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = getFilterCategoryIcon(filter.category),
                        contentDescription = filter.displayName,
                        tint = Color.White,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
            
            // 付费标识
            if (filter.isPremium) {
                Card(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(4.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFFFD700)
                    ),
                    shape = RoundedCornerShape(4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Star,
                        contentDescription = "付费",
                        tint = Color.Black,
                        modifier = Modifier
                            .size(12.dp)
                            .padding(2.dp)
                    )
                }
            }
            
            // 选中指示器
            if (isSelected) {
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(4.dp)
                        .size(16.dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            RoundedCornerShape(8.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Filled.Check,
                        contentDescription = "已选中",
                        tint = Color.White,
                        modifier = Modifier.size(10.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
            text = filter.displayName,
            style = MaterialTheme.typography.bodySmall,
            color = if (isSelected) Color.White else Color.White.copy(alpha = 0.7f),
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

/**
 * 滤镜网格视图
 */
@Composable
fun LUTFilterGrid(
    filters: List<LUTFilter>,
    selectedFilter: LUTFilter?,
    onFilterSelected: (LUTFilter) -> Unit,
    onFilterLongPress: (LUTFilter) -> Unit = {},
    columns: Int = 3,
    modifier: Modifier = Modifier
) {
    // TODO: 实现网格布局的滤镜列表
}

/**
 * 滤镜详情卡片
 */
@Composable
fun LUTFilterDetailCard(
    filter: LUTFilter,
    isSelected: Boolean,
    onClick: () -> Unit,
    onInfoClick: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
            else Color.White.copy(alpha = 0.05f)
        ),
        border = CardDefaults.outlinedCardBorder().copy(
            brush = if (isSelected) androidx.compose.ui.graphics.SolidColor(MaterialTheme.colorScheme.primary)
            else androidx.compose.ui.graphics.SolidColor(Color.White.copy(alpha = 0.2f))
        )
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 滤镜缩略图
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(RoundedCornerShape(8.dp))
            ) {
                if (filter.thumbnailPath != null) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(filter.thumbnailPath)
                            .crossfade(true)
                            .build(),
                        contentDescription = filter.displayName,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize()
                    )
                } else {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(
                                getFilterCategoryColor(filter.category).copy(alpha = 0.3f)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = getFilterCategoryIcon(filter.category),
                            contentDescription = filter.displayName,
                            tint = Color.White,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // 滤镜信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = filter.displayName,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Text(
                    text = filter.category.displayName,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
                
                if (filter.description.isNotEmpty()) {
                    Text(
                        text = filter.description,
                        style = MaterialTheme.typography.bodySmall,
                        color = Color.White.copy(alpha = 0.6f),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // 操作按钮
            Column {
                if (filter.isPremium) {
                    Icon(
                        imageVector = Icons.Filled.Star,
                        contentDescription = "付费",
                        tint = Color(0xFFFFD700),
                        modifier = Modifier.size(16.dp)
                    )
                }
                
                IconButton(onClick = onInfoClick) {
                    Icon(
                        imageVector = Icons.Filled.Info,
                        contentDescription = "详情",
                        tint = Color.White.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}

/**
 * 获取滤镜分类颜色
 */
private fun getFilterCategoryColor(category: com.qxyu.yucram.domain.model.LUTCategory): Color {
    return when (category) {
        com.qxyu.yucram.domain.model.LUTCategory.CINEMATIC -> Color(0xFF1E90FF)
        com.qxyu.yucram.domain.model.LUTCategory.VINTAGE -> Color(0xFFD2691E)
        com.qxyu.yucram.domain.model.LUTCategory.PORTRAIT -> Color(0xFFFF69B4)
        com.qxyu.yucram.domain.model.LUTCategory.LANDSCAPE -> Color(0xFF32CD32)
        com.qxyu.yucram.domain.model.LUTCategory.STREET -> Color(0xFF696969)
        com.qxyu.yucram.domain.model.LUTCategory.FASHION -> Color(0xFF9370DB)
        com.qxyu.yucram.domain.model.LUTCategory.MOODY -> Color(0xFF8B0000)
        com.qxyu.yucram.domain.model.LUTCategory.BRIGHT -> Color(0xFFFFD700)
        com.qxyu.yucram.domain.model.LUTCategory.DARK -> Color(0xFF2F4F4F)
        com.qxyu.yucram.domain.model.LUTCategory.WARM -> Color(0xFFFF4500)
        com.qxyu.yucram.domain.model.LUTCategory.COOL -> Color(0xFF00CED1)
        com.qxyu.yucram.domain.model.LUTCategory.MONOCHROME -> Color(0xFF808080)
        com.qxyu.yucram.domain.model.LUTCategory.CREATIVE -> Color(0xFFFF1493)
        com.qxyu.yucram.domain.model.LUTCategory.PROFESSIONAL -> Color(0xFF4169E1)
        com.qxyu.yucram.domain.model.LUTCategory.USER_CUSTOM -> Color(0xFF20B2AA)
    }
}

/**
 * 获取滤镜分类图标
 */
private fun getFilterCategoryIcon(category: com.qxyu.yucram.domain.model.LUTCategory): androidx.compose.ui.graphics.vector.ImageVector {
    return when (category) {
        com.qxyu.yucram.domain.model.LUTCategory.CINEMATIC -> Icons.Filled.Movie
        com.qxyu.yucram.domain.model.LUTCategory.VINTAGE -> Icons.Filled.CameraRoll
        com.qxyu.yucram.domain.model.LUTCategory.PORTRAIT -> Icons.Filled.Person
        com.qxyu.yucram.domain.model.LUTCategory.LANDSCAPE -> Icons.Filled.Landscape
        com.qxyu.yucram.domain.model.LUTCategory.STREET -> Icons.Filled.LocationCity
        com.qxyu.yucram.domain.model.LUTCategory.FASHION -> Icons.Filled.Style
        com.qxyu.yucram.domain.model.LUTCategory.MOODY -> Icons.Filled.Mood
        com.qxyu.yucram.domain.model.LUTCategory.BRIGHT -> Icons.Filled.WbSunny
        com.qxyu.yucram.domain.model.LUTCategory.DARK -> Icons.Filled.Brightness2
        com.qxyu.yucram.domain.model.LUTCategory.WARM -> Icons.Filled.Whatshot
        com.qxyu.yucram.domain.model.LUTCategory.COOL -> Icons.Filled.AcUnit
        com.qxyu.yucram.domain.model.LUTCategory.MONOCHROME -> Icons.Filled.Contrast
        com.qxyu.yucram.domain.model.LUTCategory.CREATIVE -> Icons.Filled.AutoAwesome
        com.qxyu.yucram.domain.model.LUTCategory.PROFESSIONAL -> Icons.Filled.WorkspacePremium
        com.qxyu.yucram.domain.model.LUTCategory.USER_CUSTOM -> Icons.Filled.PersonalVideo
    }
}

/**
 * 扩展：无滤镜常量
 */
private val LUTFilter.Companion.NONE: LUTFilter
    get() = LUTFilter(
        id = "none",
        name = "none",
        displayName = "原图",
        description = "不应用任何滤镜",
        category = com.qxyu.yucram.domain.model.LUTCategory.USER_CUSTOM,
        lutFilePath = ""
    )
