package com.qxyu.yucram.processing

import android.content.Context
import android.graphics.*
import android.hardware.camera2.CaptureResult
import android.media.Image
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.ByteArrayInputStream
import java.nio.ByteBuffer
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * RAW到LOG处理器
 * 实现专业的RAW解码和LOG色彩空间转换
 */
@Singleton
class RawToLogProcessor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "RawToLogProcessor"
        
        // LOG曲线参数 (基于标准LOG-C)
        private const val LOG_CUT = 0.011361f
        private const val LOG_A = 5.555556f
        private const val LOG_B = 0.047996f
        private const val LOG_C = 0.244161f
        private const val LOG_D = 0.386036f
        
        // 色彩空间转换矩阵 (sRGB to Rec.709)
        private val COLOR_MATRIX_SRGB_TO_REC709 = floatArrayOf(
            0.4124564f, 0.3575761f, 0.1804375f,
            0.2126729f, 0.7151522f, 0.0721750f,
            0.0193339f, 0.1191920f, 0.9503041f
        )
    }
    
    /**
     * 处理RAW图像到LOG色彩空间
     */
    suspend fun processRawToLog(
        rawData: ByteArray,
        captureResult: CaptureResult,
        width: Int,
        height: Int
    ): Result<Bitmap> = withContext(Dispatchers.Default) {
        try {
            Log.d(TAG, "开始RAW到LOG处理，尺寸: ${width}x${height}")
            
            // 1. 解码RAW数据为线性RGB
            val linearRgbData = decodeRawToLinearRgb(rawData, captureResult, width, height)
            
            // 2. 应用色彩校正
            val correctedRgbData = applyColorCorrection(linearRgbData, captureResult)
            
            // 3. 转换到LOG色彩空间
            val logRgbData = convertLinearToLog(correctedRgbData)
            
            // 4. 创建LOG Bitmap
            val logBitmap = createBitmapFromLogData(logRgbData, width, height)
            
            Log.d(TAG, "RAW到LOG处理完成")
            Result.success(logBitmap)
            
        } catch (e: Exception) {
            Log.e(TAG, "RAW到LOG处理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 解码RAW数据为线性RGB
     */
    private fun decodeRawToLinearRgb(
        rawData: ByteArray,
        captureResult: CaptureResult,
        width: Int,
        height: Int
    ): FloatArray {
        Log.d(TAG, "解码RAW数据为线性RGB")
        
        // 获取传感器信息（使用兼容的API）
        val whiteLevel = 1023 // 使用典型的白电平值，避免API兼容性问题
        
        // 简化的RAW解码（实际应用中需要更复杂的去马赛克算法）
        val linearRgbData = FloatArray(width * height * 3)
        val buffer = ByteBuffer.wrap(rawData)
        
        for (y in 0 until height) {
            for (x in 0 until width) {
                val pixelIndex = y * width + x
                val rgbIndex = pixelIndex * 3
                
                // 读取RAW像素值（假设16位）
                val rawValue = if (buffer.hasRemaining()) {
                    buffer.short.toInt() and 0xFFFF
                } else {
                    0
                }
                
                // 黑电平校正（简化实现）
                val blackLevelOffset = 64 // 典型的黑电平偏移
                val correctedValue = maxOf(0, rawValue - blackLevelOffset)
                
                // 归一化到0-1范围
                val normalizedValue = correctedValue.toFloat() / whiteLevel.toFloat()
                
                // 简化的Bayer模式解码 (RGGB)
                when {
                    y % 2 == 0 && x % 2 == 0 -> { // R
                        linearRgbData[rgbIndex] = normalizedValue
                        linearRgbData[rgbIndex + 1] = interpolateGreen(x, y, width, height, buffer, whiteLevel)
                        linearRgbData[rgbIndex + 2] = interpolateBlue(x, y, width, height, buffer, whiteLevel)
                    }
                    y % 2 == 0 && x % 2 == 1 -> { // G1
                        linearRgbData[rgbIndex] = interpolateRed(x, y, width, height, buffer, whiteLevel)
                        linearRgbData[rgbIndex + 1] = normalizedValue
                        linearRgbData[rgbIndex + 2] = interpolateBlue(x, y, width, height, buffer, whiteLevel)
                    }
                    y % 2 == 1 && x % 2 == 0 -> { // G2
                        linearRgbData[rgbIndex] = interpolateRed(x, y, width, height, buffer, whiteLevel)
                        linearRgbData[rgbIndex + 1] = normalizedValue
                        linearRgbData[rgbIndex + 2] = interpolateBlue(x, y, width, height, buffer, whiteLevel)
                    }
                    else -> { // B
                        linearRgbData[rgbIndex] = interpolateRed(x, y, width, height, buffer, whiteLevel)
                        linearRgbData[rgbIndex + 1] = interpolateGreen(x, y, width, height, buffer, whiteLevel)
                        linearRgbData[rgbIndex + 2] = normalizedValue
                    }
                }
            }
        }
        
        return linearRgbData
    }
    
    /**
     * 应用色彩校正
     */
    private fun applyColorCorrection(
        linearRgbData: FloatArray,
        captureResult: CaptureResult
    ): FloatArray {
        Log.d(TAG, "应用色彩校正")
        
        // 获取白平衡增益
        val colorCorrectionGains = captureResult.get(CaptureResult.COLOR_CORRECTION_GAINS)
        val rGain = colorCorrectionGains?.getRed() ?: 1.0f
        val gGain = colorCorrectionGains?.getGreenEven() ?: 1.0f
        val bGain = colorCorrectionGains?.getBlue() ?: 1.0f
        
        // 获取色彩变换矩阵
        val colorCorrectionTransform = captureResult.get(CaptureResult.COLOR_CORRECTION_TRANSFORM)
        
        val correctedData = FloatArray(linearRgbData.size)
        
        for (i in linearRgbData.indices step 3) {
            // 应用白平衡增益
            var r = linearRgbData[i] * rGain
            var g = linearRgbData[i + 1] * gGain
            var b = linearRgbData[i + 2] * bGain
            
            // 应用色彩变换矩阵（如果可用）
            if (colorCorrectionTransform != null) {
                val newR = r * colorCorrectionTransform.getElement(0, 0).toFloat() +
                          g * colorCorrectionTransform.getElement(0, 1).toFloat() +
                          b * colorCorrectionTransform.getElement(0, 2).toFloat()
                
                val newG = r * colorCorrectionTransform.getElement(1, 0).toFloat() +
                          g * colorCorrectionTransform.getElement(1, 1).toFloat() +
                          b * colorCorrectionTransform.getElement(1, 2).toFloat()
                
                val newB = r * colorCorrectionTransform.getElement(2, 0).toFloat() +
                          g * colorCorrectionTransform.getElement(2, 1).toFloat() +
                          b * colorCorrectionTransform.getElement(2, 2).toFloat()
                
                r = newR
                g = newG
                b = newB
            }
            
            // 限制在有效范围内
            correctedData[i] = r.coerceIn(0.0f, 1.0f)
            correctedData[i + 1] = g.coerceIn(0.0f, 1.0f)
            correctedData[i + 2] = b.coerceIn(0.0f, 1.0f)
        }
        
        return correctedData
    }
    
    /**
     * 转换线性RGB到LOG色彩空间
     */
    private fun convertLinearToLog(linearRgbData: FloatArray): FloatArray {
        Log.d(TAG, "转换线性RGB到LOG色彩空间")
        
        val logData = FloatArray(linearRgbData.size)
        
        for (i in linearRgbData.indices) {
            val linearValue = linearRgbData[i]
            logData[i] = linearToLog(linearValue)
        }
        
        return logData
    }
    
    /**
     * 线性值转LOG值
     */
    private fun linearToLog(linearValue: Float): Float {
        return if (linearValue < LOG_CUT) {
            LOG_C * linearValue + LOG_D
        } else {
            LOG_A * ln(linearValue + LOG_B) + LOG_D
        }
    }
    
    /**
     * LOG值转线性值（用于验证）
     */
    private fun logToLinear(logValue: Float): Float {
        return if (logValue < LOG_C * LOG_CUT + LOG_D) {
            (logValue - LOG_D) / LOG_C
        } else {
            exp((logValue - LOG_D) / LOG_A) - LOG_B
        }
    }
    
    /**
     * 从LOG数据创建Bitmap
     */
    private fun createBitmapFromLogData(
        logData: FloatArray,
        width: Int,
        height: Int
    ): Bitmap {
        Log.d(TAG, "从LOG数据创建Bitmap")
        
        val pixels = IntArray(width * height)
        
        for (i in pixels.indices) {
            val rgbIndex = i * 3
            
            // LOG值转换为显示用的RGB值（应用显示变换）
            val r = (logData[rgbIndex] * 255).toInt().coerceIn(0, 255)
            val g = (logData[rgbIndex + 1] * 255).toInt().coerceIn(0, 255)
            val b = (logData[rgbIndex + 2] * 255).toInt().coerceIn(0, 255)
            
            pixels[i] = Color.rgb(r, g, b)
        }
        
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.setPixels(pixels, 0, width, 0, 0, width, height)
        
        return bitmap
    }
    
    // 简化的插值方法（实际应用中需要更复杂的算法）
    private fun interpolateRed(x: Int, y: Int, width: Int, height: Int, buffer: ByteBuffer, whiteLevel: Int): Float {
        // 简化实现：返回相邻红色像素的平均值
        return 0.5f // 占位符
    }
    
    private fun interpolateGreen(x: Int, y: Int, width: Int, height: Int, buffer: ByteBuffer, whiteLevel: Int): Float {
        // 简化实现：返回相邻绿色像素的平均值
        return 0.5f // 占位符
    }
    
    private fun interpolateBlue(x: Int, y: Int, width: Int, height: Int, buffer: ByteBuffer, whiteLevel: Int): Float {
        // 简化实现：返回相邻蓝色像素的平均值
        return 0.5f // 占位符
    }
    
    /**
     * 验证LOG转换的可逆性
     */
    fun verifyLogConversion(originalLinear: Float): Boolean {
        val logValue = linearToLog(originalLinear)
        val recoveredLinear = logToLinear(logValue)
        val error = abs(originalLinear - recoveredLinear)
        return error < 0.001f // 允许小的数值误差
    }
}
