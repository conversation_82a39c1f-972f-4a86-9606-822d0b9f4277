package com.qxyu.yucram.domain.model;

/**
 * LUT滤镜搜索条件
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b$\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0095\u0001\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u0012\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0005\u0012\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0005\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\u0002\u0010\u0017J\u000b\u0010+\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u0014H\u00c6\u0003J\t\u0010-\u001a\u00020\u0016H\u00c6\u0003J\u000f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005H\u00c6\u0003J\u000f\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005H\u00c6\u0003J\u0010\u00100\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001bJ\u0010\u00101\u001a\u0004\u0018\u00010\tH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001bJ\u0010\u00102\u001a\u0004\u0018\u00010\fH\u00c6\u0003\u00a2\u0006\u0002\u0010\"J\u0010\u00103\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u0010\u001fJ\u000f\u00104\u001a\b\u0012\u0004\u0012\u00020\u00100\u0005H\u00c6\u0003J\u000f\u00105\u001a\b\u0012\u0004\u0012\u00020\u00120\u0005H\u00c6\u0003J\u009e\u0001\u00106\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u000e\b\u0002\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u00052\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\u00052\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e2\u000e\b\u0002\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00052\u000e\b\u0002\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u00052\b\b\u0002\u0010\u0013\u001a\u00020\u00142\b\b\u0002\u0010\u0015\u001a\u00020\u0016H\u00c6\u0001\u00a2\u0006\u0002\u00107J\u0013\u00108\u001a\u00020\t2\b\u00109\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010:\u001a\u00020;H\u00d6\u0001J\t\u0010<\u001a\u00020\u0003H\u00d6\u0001R\u0017\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019R\u0015\u0010\n\u001a\u0004\u0018\u00010\t\u00a2\u0006\n\n\u0002\u0010\u001c\u001a\u0004\b\n\u0010\u001bR\u0015\u0010\b\u001a\u0004\u0018\u00010\t\u00a2\u0006\n\n\u0002\u0010\u001c\u001a\u0004\b\b\u0010\u001bR\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0019R\u0015\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\n\n\u0002\u0010 \u001a\u0004\b\u001e\u0010\u001fR\u0015\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b!\u0010\"R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u0013\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\u0015\u001a\u00020\u0016\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00030\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0019\u00a8\u0006="}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTFilterSearchCriteria;", "", "query", "", "categories", "", "Lcom/qxyu/yucram/domain/model/LUTCategory;", "tags", "isPremium", "", "isBuiltIn", "minRating", "", "maxFileSize", "", "colorProfiles", "Lcom/qxyu/yucram/domain/model/ColorProfile;", "lutSizes", "Lcom/qxyu/yucram/domain/model/LUTSize;", "sortBy", "Lcom/qxyu/yucram/domain/model/LUTSortBy;", "sortOrder", "Lcom/qxyu/yucram/domain/model/SortOrder;", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Float;Ljava/lang/Long;Ljava/util/List;Ljava/util/List;Lcom/qxyu/yucram/domain/model/LUTSortBy;Lcom/qxyu/yucram/domain/model/SortOrder;)V", "getCategories", "()Ljava/util/List;", "getColorProfiles", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getLutSizes", "getMaxFileSize", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getMinRating", "()Ljava/lang/Float;", "Ljava/lang/Float;", "getQuery", "()Ljava/lang/String;", "getSortBy", "()Lcom/qxyu/yucram/domain/model/LUTSortBy;", "getSortOrder", "()Lcom/qxyu/yucram/domain/model/SortOrder;", "getTags", "component1", "component10", "component11", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Float;Ljava/lang/Long;Ljava/util/List;Ljava/util/List;Lcom/qxyu/yucram/domain/model/LUTSortBy;Lcom/qxyu/yucram/domain/model/SortOrder;)Lcom/qxyu/yucram/domain/model/LUTFilterSearchCriteria;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class LUTFilterSearchCriteria {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String query = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.qxyu.yucram.domain.model.LUTCategory> categories = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> tags = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean isPremium = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Boolean isBuiltIn = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Float minRating = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long maxFileSize = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.qxyu.yucram.domain.model.ColorProfile> colorProfiles = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.qxyu.yucram.domain.model.LUTSize> lutSizes = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LUTSortBy sortBy = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.SortOrder sortOrder = null;
    
    public LUTFilterSearchCriteria(@org.jetbrains.annotations.Nullable()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.LUTCategory> categories, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> tags, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isPremium, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isBuiltIn, @org.jetbrains.annotations.Nullable()
    java.lang.Float minRating, @org.jetbrains.annotations.Nullable()
    java.lang.Long maxFileSize, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.ColorProfile> colorProfiles, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.LUTSize> lutSizes, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTSortBy sortBy, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SortOrder sortOrder) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getQuery() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LUTCategory> getCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getTags() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean isPremium() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean isBuiltIn() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float getMinRating() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getMaxFileSize() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.ColorProfile> getColorProfiles() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LUTSize> getLutSizes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTSortBy getSortBy() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.SortOrder getSortOrder() {
        return null;
    }
    
    public LUTFilterSearchCriteria() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTSortBy component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.SortOrder component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LUTCategory> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Boolean component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.ColorProfile> component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.qxyu.yucram.domain.model.LUTSize> component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFilterSearchCriteria copy(@org.jetbrains.annotations.Nullable()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.LUTCategory> categories, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> tags, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isPremium, @org.jetbrains.annotations.Nullable()
    java.lang.Boolean isBuiltIn, @org.jetbrains.annotations.Nullable()
    java.lang.Float minRating, @org.jetbrains.annotations.Nullable()
    java.lang.Long maxFileSize, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.ColorProfile> colorProfiles, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.qxyu.yucram.domain.model.LUTSize> lutSizes, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTSortBy sortBy, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.SortOrder sortOrder) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}