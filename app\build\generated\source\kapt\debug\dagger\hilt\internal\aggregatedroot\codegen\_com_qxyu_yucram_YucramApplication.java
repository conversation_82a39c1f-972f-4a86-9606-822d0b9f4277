package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.qxyu.yucram.YucramApplication",
    rootPackage = "com.qxyu.yucram",
    originatingRoot = "com.qxyu.yucram.YucramApplication",
    originatingRootPackage = "com.qxyu.yucram",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "YucramApplication",
    originatingRootSimpleNames = "YucramApplication"
)
public class _com_qxyu_yucram_YucramApplication {
}
