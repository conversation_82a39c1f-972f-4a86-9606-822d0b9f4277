package com.qxyu.yucram.utils

import android.content.Context
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.os.Build
import android.util.Log

/**
 * 设备适配器
 * 检测和适配特定设备的专有相机功能
 */
object DeviceAdapter {
    
    private const val TAG = "DeviceAdapter"
    
    // OPPO设备标识
    private val OPPO_BRANDS = listOf("OPPO", "OnePlus", "Realme")
    private val OPPO_FIND_X8_MODELS = listOf("CPH2581", "CPH2583", "CPH2585") // Find X8 Ultra型号
    
    /**
     * 检测是否为OPPO设备
     */
    fun isOppoDevice(): Boolean {
        return OPPO_BRANDS.any { brand ->
            Build.BRAND.equals(brand, ignoreCase = true) ||
            Build.MANUFACTURER.equals(brand, ignoreCase = true)
        }
    }
    
    /**
     * 检测是否为OPPO Find X8 Ultra
     */
    fun isOppoFindX8Ultra(): Boolean {
        return isOppoDevice() && (
            Build.MODEL.contains("Find X8", ignoreCase = true) ||
            OPPO_FIND_X8_MODELS.any { model -> 
                Build.MODEL.equals(model, ignoreCase = true)
            }
        )
    }
    
    /**
     * 获取设备信息
     */
    fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            brand = Build.BRAND,
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            device = Build.DEVICE,
            isOppoDevice = isOppoDevice(),
            isOppoFindX8Ultra = isOppoFindX8Ultra()
        )
    }
    
    /**
     * 检测设备支持的专有格式
     */
    fun getSupportedProprietaryFormats(context: Context): List<ProprietaryFormat> {
        val formats = mutableListOf<ProprietaryFormat>()
        
        if (isOppoFindX8Ultra()) {
            // OPPO Find X8 Ultra专有格式
            formats.add(
                ProprietaryFormat(
                    name = "JPG MAX",
                    description = "OPPO增强JPEG格式",
                    isSupported = checkJpgMaxSupport(context),
                    vendor = "OPPO"
                )
            )
            
            formats.add(
                ProprietaryFormat(
                    name = "RAW MAX", 
                    description = "OPPO 16-bit RAW格式",
                    isSupported = checkRawMaxSupport(context),
                    vendor = "OPPO"
                )
            )
        }
        
        return formats
    }
    
    /**
     * 检测JPG MAX支持
     */
    private fun checkJpgMaxSupport(context: Context): Boolean {
        return try {
            // 尝试检测OPPO相机特性
            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            val cameraIds = cameraManager.cameraIdList
            
            for (cameraId in cameraIds) {
                val characteristics = cameraManager.getCameraCharacteristics(cameraId)
                
                // 检查是否支持高质量JPEG
                val jpegSizes = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                    ?.getOutputSizes(android.graphics.ImageFormat.JPEG)
                
                if (jpegSizes != null && jpegSizes.isNotEmpty()) {
                    val maxSize = jpegSizes.maxByOrNull { it.width * it.height }
                    // 如果支持超高分辨率JPEG，可能支持JPG MAX
                    if (maxSize != null && maxSize.width * maxSize.height > 50_000_000) { // 50MP+
                        Log.d(TAG, "High resolution JPEG detected, JPG MAX might be supported")
                        return true
                    }
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking JPG MAX support", e)
            false
        }
    }
    
    /**
     * 检测RAW MAX支持
     */
    private fun checkRawMaxSupport(context: Context): Boolean {
        return try {
            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            val cameraIds = cameraManager.cameraIdList
            
            for (cameraId in cameraIds) {
                val characteristics = cameraManager.getCameraCharacteristics(cameraId)
                
                // 检查RAW支持
                val capabilities = characteristics.get(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES)
                val supportsRaw = capabilities?.contains(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_RAW) == true
                
                if (supportsRaw) {
                    // 检查RAW尺寸
                    val rawSizes = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
                        ?.getOutputSizes(android.graphics.ImageFormat.RAW_SENSOR)
                    
                    if (rawSizes != null && rawSizes.isNotEmpty()) {
                        val maxRawSize = rawSizes.maxByOrNull { it.width * it.height }
                        // 如果支持超高分辨率RAW，可能支持RAW MAX
                        if (maxRawSize != null && maxRawSize.width * maxRawSize.height > 50_000_000) {
                            Log.d(TAG, "High resolution RAW detected, RAW MAX might be supported")
                            return true
                        }
                    }
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking RAW MAX support", e)
            false
        }
    }
    
    /**
     * 获取OPPO专有相机设置
     */
    fun getOppoSpecificSettings(): OppoSettings? {
        return if (isOppoFindX8Ultra()) {
            OppoSettings(
                supportsJpgMax = true,
                supportsRawMax = true,
                maxJpegQuality = 100,
                maxRawBitDepth = 16,
                hasHyperlapseMode = true,
                hasProMode = true
            )
        } else null
    }
}

/**
 * 设备信息数据类
 */
data class DeviceInfo(
    val brand: String,
    val manufacturer: String,
    val model: String,
    val device: String,
    val isOppoDevice: Boolean,
    val isOppoFindX8Ultra: Boolean
)

/**
 * 专有格式数据类
 */
data class ProprietaryFormat(
    val name: String,
    val description: String,
    val isSupported: Boolean,
    val vendor: String
)

/**
 * OPPO专有设置
 */
data class OppoSettings(
    val supportsJpgMax: Boolean,
    val supportsRawMax: Boolean,
    val maxJpegQuality: Int,
    val maxRawBitDepth: Int,
    val hasHyperlapseMode: Boolean,
    val hasProMode: Boolean
)
