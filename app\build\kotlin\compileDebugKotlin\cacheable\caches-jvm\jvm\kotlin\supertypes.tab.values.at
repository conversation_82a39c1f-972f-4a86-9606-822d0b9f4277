/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.app.Application, +androidx.lifecycle.DefaultLifecycleObserver kotlin.Enum androidx.room.RoomDatabase3 2com.qxyu.yucram.domain.repository.CameraRepository2 1com.qxyu.yucram.domain.repository.PhotoRepository5 4com.qxyu.yucram.domain.repository.SettingsRepository kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel$ #androidx.lifecycle.AndroidViewModel kotlin.Enum kotlin.Enum kotlin.Enum dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory, +androidx.lifecycle.DefaultLifecycleObserver kotlin.Enum dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel2 1com.qxyu.yucram.domain.repository.AlbumRepository3 2com.qxyu.yucram.domain.repository.CameraRepository2 1com.qxyu.yucram.domain.repository.PhotoRepository kotlin.Enum kotlin.Enum2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult, +com.qxyu.yucram.domain.model.AlbumLoadState, +com.qxyu.yucram.domain.model.AlbumLoadState, +com.qxyu.yucram.domain.model.AlbumLoadState, +com.qxyu.yucram.domain.model.AlbumLoadState kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum2 1com.qxyu.yucram.domain.model.PhotoOperationResult2 1com.qxyu.yucram.domain.model.PhotoOperationResult2 1com.qxyu.yucram.domain.model.PhotoOperationResult, +com.qxyu.yucram.domain.model.PhotoLoadState, +com.qxyu.yucram.domain.model.PhotoLoadState, +com.qxyu.yucram.domain.model.PhotoLoadState, +com.qxyu.yucram.domain.model.PhotoLoadState androidx.lifecycle.ViewModel dagger.internal.Factory( 'com.qxyu.yucram.data.local.dao.PhotoDao dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory kotlin.Enum kotlin.Enum2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult2 1com.qxyu.yucram.domain.model.AlbumOperationResult, +com.qxyu.yucram.domain.model.AlbumLoadState, +com.qxyu.yucram.domain.model.AlbumLoadState, +com.qxyu.yucram.domain.model.AlbumLoadState, +com.qxyu.yucram.domain.model.AlbumLoadState kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum, +com.qxyu.yucram.domain.model.AdjustmentArea, +com.qxyu.yucram.domain.model.AdjustmentArea, +com.qxyu.yucram.domain.model.AdjustmentArea kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.EnumB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteractionB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteractionB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteractionB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteraction dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory dagger.internal.Factory2 1com.qxyu.yucram.domain.repository.AlbumRepository kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum2 1com.qxyu.yucram.domain.model.PhotoOperationResult2 1com.qxyu.yucram.domain.model.PhotoOperationResult2 1com.qxyu.yucram.domain.model.PhotoOperationResult, +com.qxyu.yucram.domain.model.PhotoLoadState, +com.qxyu.yucram.domain.model.PhotoLoadState, +com.qxyu.yucram.domain.model.PhotoLoadState, +com.qxyu.yucram.domain.model.PhotoLoadState kotlin.Enum kotlin.Enum, +com.qxyu.yucram.domain.model.AdjustmentArea, +com.qxyu.yucram.domain.model.AdjustmentArea, +com.qxyu.yucram.domain.model.AdjustmentArea kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum dagger.internal.Factory dagger.internal.Factory androidx.lifecycle.ViewModelB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteractionB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteractionB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteractionB Acom.qxyu.yucram.presentation.photoedit.components.ToolInteraction