package com.qxyu.yucram.filter

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import com.qxyu.yucram.domain.model.FilterPreset
import com.qxyu.yucram.domain.model.LutFormat
import com.qxyu.yucram.domain.model.PostProcessingParams
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * LUT文件管理器
 * 管理LUT/HNCS/CUBE文件的导入、导出、存储
 */
@Singleton
class LutFileManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "LutFileManager"
        private const val LUT_DIR = "luts"
        private const val BUILTIN_DIR = "builtin"
        private const val USER_DIR = "user"
        private const val ICONS_DIR = "icons"
        private const val FILTERS_CONFIG_FILE = "filters.json"
    }
    
    private val json = Json { ignoreUnknownKeys = true }
    
    // LUT根目录
    private val lutRootDir: File by lazy {
        File(context.filesDir, LUT_DIR).apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 内置LUT目录
    private val builtinDir: File by lazy {
        File(lutRootDir, BUILTIN_DIR).apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 用户LUT目录
    private val userDir: File by lazy {
        File(lutRootDir, USER_DIR).apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 图标目录
    private val iconsDir: File by lazy {
        File(lutRootDir, ICONS_DIR).apply {
            if (!exists()) mkdirs()
        }
    }
    
    // 滤镜配置文件
    private val configFile: File by lazy {
        File(lutRootDir, FILTERS_CONFIG_FILE)
    }
    
    /**
     * 初始化LUT文件管理器
     */
    suspend fun initialize(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            // 创建必要的目录
            createDirectories()
            
            // 复制内置LUT文件（如果不存在）
            copyBuiltinLutsFromAssets()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "初始化LUT文件管理器失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取所有可用的滤镜
     */
    suspend fun getAvailableFilters(): Result<List<FilterPreset>> = withContext(Dispatchers.IO) {
        try {
            val filters = mutableListOf<FilterPreset>()
            
            // 加载内置滤镜
            filters.addAll(loadBuiltinFilters())
            
            // 加载用户滤镜
            filters.addAll(loadUserFilters())
            
            Result.success(filters)
        } catch (e: Exception) {
            Log.e(TAG, "获取滤镜列表失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 导入LUT文件
     */
    suspend fun importLut(
        lutFileUri: Uri,
        iconUri: Uri?,
        filterName: String
    ): Result<FilterPreset> = withContext(Dispatchers.IO) {
        try {
            // 验证LUT文件格式
            val lutFormat = detectLutFormat(lutFileUri)
            if (lutFormat == null) {
                return@withContext Result.failure(Exception("不支持的LUT文件格式"))
            }
            
            // 生成唯一ID
            val filterId = "user_${System.currentTimeMillis()}"
            
            // 复制LUT文件
            val lutFileName = "${filterId}.${lutFormat.extension.removePrefix(".")}"
            val lutFile = File(userDir, lutFileName)
            copyFile(lutFileUri, lutFile)
            
            // 复制图标文件
            val iconFileName = "${filterId}_icon.png"
            val iconFile = File(iconsDir, iconFileName)
            if (iconUri != null) {
                copyAndResizeIcon(iconUri, iconFile)
            } else {
                createDefaultIcon(iconFile)
            }
            
            // 创建滤镜预设
            val filterPreset = FilterPreset(
                id = filterId,
                name = filterName,
                lutFilePath = lutFile.absolutePath,
                iconPath = iconFile.absolutePath,
                isBuiltIn = false,
                postProcessingParams = PostProcessingParams()
            )
            
            // 保存到配置
            saveFilterToConfig(filterPreset)
            
            Log.d(TAG, "成功导入LUT: $filterName")
            Result.success(filterPreset)
            
        } catch (e: Exception) {
            Log.e(TAG, "导入LUT失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 导出LUT文件
     */
    suspend fun exportLut(
        filterId: String,
        outputDir: File
    ): Result<File> = withContext(Dispatchers.IO) {
        try {
            val filter = getFilterById(filterId)
                ?: return@withContext Result.failure(Exception("滤镜不存在"))
            
            val lutFile = File(filter.lutFilePath)
            if (!lutFile.exists()) {
                return@withContext Result.failure(Exception("LUT文件不存在"))
            }
            
            val outputFile = File(outputDir, "${filter.name}.${lutFile.extension}")
            lutFile.copyTo(outputFile, overwrite = true)
            
            Result.success(outputFile)
        } catch (e: Exception) {
            Log.e(TAG, "导出LUT失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 删除用户LUT
     */
    suspend fun deleteUserLut(filterId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val filter = getFilterById(filterId)
                ?: return@withContext Result.failure(Exception("滤镜不存在"))
            
            if (filter.isBuiltIn) {
                return@withContext Result.failure(Exception("无法删除内置滤镜"))
            }
            
            // 删除LUT文件
            val lutFile = File(filter.lutFilePath)
            if (lutFile.exists()) {
                lutFile.delete()
            }
            
            // 删除图标文件
            val iconFile = File(filter.iconPath)
            if (iconFile.exists()) {
                iconFile.delete()
            }
            
            // 从配置中移除
            removeFilterFromConfig(filterId)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "删除LUT失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新滤镜后处理参数
     */
    suspend fun updateFilterParams(
        filterId: String,
        params: PostProcessingParams
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val filter = getFilterById(filterId)
                ?: return@withContext Result.failure(Exception("滤镜不存在"))
            
            val updatedFilter = filter.copy(
                postProcessingParams = params,
                updatedAt = System.currentTimeMillis()
            )
            
            saveFilterToConfig(updatedFilter)
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "更新滤镜参数失败", e)
            Result.failure(e)
        }
    }
    
    // 私有方法
    
    private fun createDirectories() {
        lutRootDir.mkdirs()
        builtinDir.mkdirs()
        userDir.mkdirs()
        iconsDir.mkdirs()
    }
    
    private suspend fun copyBuiltinLutsFromAssets() {
        try {
            val assetManager = context.assets
            val lutAssets = assetManager.list("luts") ?: return
            
            for (assetFile in lutAssets) {
                if (assetFile.endsWith(".cube")) {
                    val targetFile = File(builtinDir, assetFile)
                    if (!targetFile.exists()) {
                        assetManager.open("luts/$assetFile").use { input ->
                            targetFile.outputStream().use { output ->
                                input.copyTo(output)
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "复制内置LUT文件失败", e)
        }
    }
    
    private fun loadBuiltinFilters(): List<FilterPreset> {
        val filters = mutableListOf<FilterPreset>()
        
        builtinDir.listFiles { file ->
            file.extension.lowercase() == "cube"
        }?.forEach { lutFile ->
            val filterId = "builtin_${lutFile.nameWithoutExtension}"
            val iconFile = File(iconsDir, "${filterId}_icon.png")
            
            // 如果图标不存在，创建默认图标
            if (!iconFile.exists()) {
                createDefaultIcon(iconFile)
            }
            
            filters.add(
                FilterPreset(
                    id = filterId,
                    name = lutFile.nameWithoutExtension, // 保持原始名称，支持中文
                    lutFilePath = lutFile.absolutePath,
                    iconPath = iconFile.absolutePath,
                    isBuiltIn = true
                )
            )
        }
        
        return filters
    }
    
    private fun loadUserFilters(): List<FilterPreset> {
        return try {
            if (!configFile.exists()) {
                emptyList()
            } else {
                val configJson = configFile.readText()
                json.decodeFromString<List<FilterPreset>>(configJson)
                    .filter { !it.isBuiltIn }
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载用户滤镜配置失败", e)
            emptyList()
        }
    }
    
    private fun detectLutFormat(uri: Uri): LutFormat? {
        val fileName = uri.lastPathSegment ?: return null
        return when (fileName.substringAfterLast('.').lowercase()) {
            "cube" -> LutFormat.CUBE
            "3dl" -> LutFormat.THREE_DL
            "lut" -> LutFormat.LUT
            else -> null
        }
    }
    
    private fun copyFile(sourceUri: Uri, targetFile: File) {
        context.contentResolver.openInputStream(sourceUri)?.use { input ->
            targetFile.outputStream().use { output ->
                input.copyTo(output)
            }
        }
    }
    
    private fun copyAndResizeIcon(iconUri: Uri, targetFile: File) {
        context.contentResolver.openInputStream(iconUri)?.use { input ->
            val bitmap = BitmapFactory.decodeStream(input)
            val resizedBitmap = Bitmap.createScaledBitmap(bitmap, 128, 128, true)
            
            targetFile.outputStream().use { output ->
                resizedBitmap.compress(Bitmap.CompressFormat.PNG, 100, output)
            }
            
            bitmap.recycle()
            resizedBitmap.recycle()
        }
    }
    
    private fun createDefaultIcon(iconFile: File) {
        // 根据滤镜名称创建不同颜色的图标
        val filterName = iconFile.nameWithoutExtension.replace("_icon", "").replace("builtin_", "")
        val color = when {
            // 中文滤镜
            filterName.contains("夏日") -> 0xFFFFB347.toInt() // 温暖的橙色
            filterName.contains("清新美食") -> 0xFF90EE90.toInt() // 清新绿色
            filterName.contains("温暖乡村") -> 0xFFDEB887.toInt() // 温暖米色
            filterName.contains("灰度电影") -> 0xFF708090.toInt() // 灰蓝色
            filterName.contains("白色负片") -> 0xFFF5F5F5.toInt() // 白色
            filterName.contains("稻田胶片") -> 0xFFDAA520.toInt() // 金黄色
            filterName.contains("胶片印象") -> 0xFFCD853F.toInt() // 秘鲁色
            filterName.contains("胶片复古") -> 0xFFBC8F8F.toInt() // 玫瑰棕
            filterName.contains("金色稻田") -> 0xFFFFD700.toInt() // 金色
            filterName.contains("露营氛围") -> 0xFF8FBC8F.toInt() // 深海绿
            filterName.contains("黄昏氛围") -> 0xFFFF6347.toInt() // 番茄红

            // 英文滤镜
            filterName.contains("Candy") -> 0xFFFF69B4.toInt() // 糖果粉
            filterName.contains("Hasselblad") -> 0xFF4169E1.toInt() // 皇家蓝
            filterName.contains("TXD") -> 0xFF2F4F4F.toInt() // 深灰绿
            filterName.contains("fuji") -> 0xFF00CED1.toInt() // 深绿松石

            // 默认
            else -> 0xFF666666.toInt() // 默认灰色
        }

        val bitmap = Bitmap.createBitmap(128, 128, Bitmap.Config.ARGB_8888)
        bitmap.eraseColor(color)

        iconFile.outputStream().use { output ->
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, output)
        }

        bitmap.recycle()
    }
    
    private fun getFilterById(filterId: String): FilterPreset? {
        val allFilters = loadBuiltinFilters() + loadUserFilters()
        return allFilters.find { it.id == filterId }
    }
    
    private fun saveFilterToConfig(filter: FilterPreset) {
        val existingFilters = loadUserFilters().toMutableList()
        val index = existingFilters.indexOfFirst { it.id == filter.id }
        
        if (index >= 0) {
            existingFilters[index] = filter
        } else {
            existingFilters.add(filter)
        }
        
        val configJson = json.encodeToString(existingFilters)
        configFile.writeText(configJson)
    }
    
    private fun removeFilterFromConfig(filterId: String) {
        val existingFilters = loadUserFilters().toMutableList()
        existingFilters.removeIf { it.id == filterId }
        
        val configJson = json.encodeToString(existingFilters)
        configFile.writeText(configJson)
    }
}
