package com.qxyu.yucram.domain.model;

/**
 * 边框类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/qxyu/yucram/domain/model/BorderType;", "", "(Ljava/lang/String;I)V", "SOLID", "GRADIENT", "PATTERN", "DECORATIVE", "app_debug"})
public enum BorderType {
    /*public static final*/ SOLID /* = new SOLID() */,
    /*public static final*/ GRADIENT /* = new GRADIENT() */,
    /*public static final*/ PATTERN /* = new PATTERN() */,
    /*public static final*/ DECORATIVE /* = new DECORATIVE() */;
    
    BorderType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.BorderType> getEntries() {
        return null;
    }
}