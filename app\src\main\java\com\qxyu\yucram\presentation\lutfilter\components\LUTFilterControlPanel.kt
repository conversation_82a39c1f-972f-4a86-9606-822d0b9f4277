package com.qxyu.yucram.presentation.lutfilter.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.*

/**
 * LUT滤镜控制面板
 */
@Composable
fun LUTFilterControlPanel(
    filter: LUTFilter,
    settings: LUTFilterSettings,
    onSettingsChanged: (LUTFilterSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier
            .fillMaxWidth()
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 滤镜强度控制
        item {
            FilterIntensityControl(
                intensity = settings.intensity,
                onIntensityChanged = { intensity ->
                    onSettingsChanged(settings.copy(intensity = intensity))
                }
            )
        }
        
        // 混合模式选择
        item {
            BlendModeSelector(
                selectedMode = settings.blendMode,
                onModeSelected = { mode ->
                    onSettingsChanged(settings.copy(blendMode = mode))
                }
            )
        }
        
        // 高级设置
        item {
            AdvancedSettings(
                settings = settings,
                onSettingsChanged = onSettingsChanged
            )
        }
        
        // 快速预设
        item {
            QuickPresets(
                filter = filter,
                currentSettings = settings,
                onPresetSelected = { presetSettings ->
                    onSettingsChanged(presetSettings)
                }
            )
        }
    }
}

/**
 * 滤镜强度控制
 */
@Composable
fun FilterIntensityControl(
    intensity: Float,
    onIntensityChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "滤镜强度",
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.SemiBold,
                color = Color.White
            )
            
            Text(
                text = "${(intensity * 100).toInt()}%",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Slider(
            value = intensity,
            onValueChange = onIntensityChanged,
            valueRange = 0f..1f,
            colors = SliderDefaults.colors(
                thumbColor = MaterialTheme.colorScheme.primary,
                activeTrackColor = MaterialTheme.colorScheme.primary,
                inactiveTrackColor = Color.White.copy(alpha = 0.3f)
            )
        )
        
        // 快速强度选择
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val quickIntensities = listOf(0f, 0.25f, 0.5f, 0.75f, 1f)
            val quickLabels = listOf("0%", "25%", "50%", "75%", "100%")
            
            quickIntensities.zip(quickLabels).forEach { (value, label) ->
                TextButton(
                    onClick = { onIntensityChanged(value) },
                    colors = ButtonDefaults.textButtonColors(
                        contentColor = if (kotlin.math.abs(intensity - value) < 0.01f) 
                            MaterialTheme.colorScheme.primary 
                        else Color.White.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
}

/**
 * 混合模式选择器
 */
@Composable
fun BlendModeSelector(
    selectedMode: LUTBlendMode,
    onModeSelected: (LUTBlendMode) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "混合模式",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(LUTBlendMode.values()) { mode ->
                BlendModeChip(
                    mode = mode,
                    isSelected = selectedMode == mode,
                    onClick = { onModeSelected(mode) }
                )
            }
        }
    }
}

/**
 * 混合模式芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BlendModeChip(
    mode: LUTBlendMode,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = {
            Text(
                text = mode.displayName,
                style = MaterialTheme.typography.bodySmall
            )
        },
        selected = isSelected,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = Color.White,
            containerColor = Color.White.copy(alpha = 0.1f),
            labelColor = Color.White
        ),
        modifier = modifier
    )
}

/**
 * 高级设置
 */
@Composable
fun AdvancedSettings(
    settings: LUTFilterSettings,
    onSettingsChanged: (LUTFilterSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "高级设置",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White
        )
        
        // 实时预览开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "实时预览",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White
            )
            
            Switch(
                checked = settings.realTimePreview,
                onCheckedChange = { enabled ->
                    onSettingsChanged(settings.copy(realTimePreview = enabled))
                },
                colors = SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colorScheme.primary,
                    checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                )
            )
        }
        
        // 高质量模式开关
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "高质量模式",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White
                )
                Text(
                    text = "更高质量，处理较慢",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
            
            Switch(
                checked = settings.highQualityMode,
                onCheckedChange = { enabled ->
                    onSettingsChanged(settings.copy(highQualityMode = enabled))
                },
                colors = SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colorScheme.primary,
                    checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                )
            )
        }
        
        // 遮罩功能
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "局部遮罩",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.White
                )
                Text(
                    text = "仅对选定区域应用滤镜",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
            
            Switch(
                checked = settings.maskEnabled,
                onCheckedChange = { enabled ->
                    onSettingsChanged(settings.copy(maskEnabled = enabled))
                },
                colors = SwitchDefaults.colors(
                    checkedThumbColor = MaterialTheme.colorScheme.primary,
                    checkedTrackColor = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f)
                )
            )
        }
    }
}

/**
 * 快速预设
 */
@Composable
fun QuickPresets(
    filter: LUTFilter,
    currentSettings: LUTFilterSettings,
    onPresetSelected: (LUTFilterSettings) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = "快速预设",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(getQuickPresets(filter)) { preset ->
                QuickPresetChip(
                    preset = preset,
                    isSelected = isPresetSelected(preset, currentSettings),
                    onClick = { onPresetSelected(preset.settings) }
                )
            }
        }
    }
}

/**
 * 快速预设芯片
 */
@Composable
fun QuickPresetChip(
    preset: LUTQuickPreset,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) MaterialTheme.colorScheme.primary
            else Color.White.copy(alpha = 0.1f),
            contentColor = if (isSelected) Color.White
            else Color.White.copy(alpha = 0.7f)
        ),
        modifier = modifier
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = preset.name,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
            )
            Text(
                text = "${(preset.settings.intensity * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

/**
 * 滤镜信息对话框
 */
@Composable
fun LUTFilterInfoDialog(
    filter: LUTFilter,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(filter.displayName)
        },
        text = {
            Column {
                Text("分类: ${filter.category.displayName}")
                Spacer(modifier = Modifier.height(8.dp))
                Text("描述: ${filter.description}")
                Spacer(modifier = Modifier.height(8.dp))
                Text("格式: ${filter.lutFormat.description}")
                Text("大小: ${filter.lutSize.description}")
                if (filter.author != null) {
                    Text("作者: ${filter.author}")
                }
                if (filter.isPremium) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Star,
                            contentDescription = "付费",
                            tint = Color(0xFFFFD700),
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("付费滤镜")
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 快速预设数据类
 */
data class LUTQuickPreset(
    val name: String,
    val settings: LUTFilterSettings
)

/**
 * 获取快速预设
 */
private fun getQuickPresets(filter: LUTFilter): List<LUTQuickPreset> {
    return listOf(
        LUTQuickPreset(
            name = "轻微",
            settings = LUTFilterSettings(
                selectedFilter = filter,
                intensity = 0.3f,
                blendMode = LUTBlendMode.NORMAL
            )
        ),
        LUTQuickPreset(
            name = "适中",
            settings = LUTFilterSettings(
                selectedFilter = filter,
                intensity = 0.6f,
                blendMode = LUTBlendMode.NORMAL
            )
        ),
        LUTQuickPreset(
            name = "强烈",
            settings = LUTFilterSettings(
                selectedFilter = filter,
                intensity = 1.0f,
                blendMode = LUTBlendMode.NORMAL
            )
        ),
        LUTQuickPreset(
            name = "柔和",
            settings = LUTFilterSettings(
                selectedFilter = filter,
                intensity = 0.8f,
                blendMode = LUTBlendMode.SOFT_LIGHT
            )
        ),
        LUTQuickPreset(
            name = "叠加",
            settings = LUTFilterSettings(
                selectedFilter = filter,
                intensity = 0.7f,
                blendMode = LUTBlendMode.OVERLAY
            )
        )
    )
}

/**
 * 检查预设是否被选中
 */
private fun isPresetSelected(preset: LUTQuickPreset, currentSettings: LUTFilterSettings): Boolean {
    return kotlin.math.abs(preset.settings.intensity - currentSettings.intensity) < 0.01f &&
            preset.settings.blendMode == currentSettings.blendMode
}
