package com.qxyu.yucram.domain.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.time.LocalDateTime
import java.util.Date

/**
 * 照片数据模型
 */
@Entity(tableName = "photos")
data class Photo(
    @PrimaryKey
    val id: String,
    val fileName: String,
    val filePath: String,
    val thumbnailPath: String? = null,
    val mimeType: String,
    val fileSize: Long,
    val width: Int,
    val height: Int,
    val dateAdded: LocalDateTime,
    val dateTaken: LocalDateTime? = null,
    val dateModified: LocalDateTime,
    val orientation: Int = 0,
    val location: PhotoLocation? = null,
    val exifData: ExifData? = null,
    val isYucramPhoto: Boolean = false, // 是否由Yucram拍摄
    val filterUsed: String? = null, // 使用的滤镜名称
    val processingInfo: ProcessingInfo? = null, // 处理信息
    val isFavorite: Boolean = false,
    val isDeleted: Boolean = false,
    val albumIds: List<String> = emptyList(), // 所属相册ID列表
    val tags: List<String> = emptyList() // 标签列表
)

/**
 * 照片位置信息
 */
data class PhotoLocation(
    val latitude: Double,
    val longitude: Double,
    val altitude: Double? = null,
    val address: String? = null,
    val city: String? = null,
    val country: String? = null
)

/**
 * EXIF数据
 */
data class ExifData(
    val cameraMake: String? = null,
    val cameraModel: String? = null,
    val lensModel: String? = null,
    val focalLength: Float? = null,
    val aperture: Float? = null,
    val shutterSpeed: String? = null,
    val iso: Int? = null,
    val flash: Boolean? = null,
    val whiteBalance: String? = null,
    val exposureMode: String? = null,
    val meteringMode: String? = null,
    val colorSpace: String? = null,
    val software: String? = null
)

/**
 * 处理信息（Yucram专用）
 */
data class ProcessingInfo(
    val sourceFormat: ImageSourceFormat,
    val isRawProcessed: Boolean = false,
    val isLogProcessed: Boolean = false,
    val lutApplied: String? = null,
    val processingTime: Long? = null, // 处理时间（毫秒）
    val processingVersion: String? = null, // 处理算法版本
    val adjustments: PhotoAdjustments? = null,
    val hasBorder: Boolean = false, // 是否有边框
    val hasWatermark: Boolean = false // 是否有水印
)

/**
 * 照片调整参数
 */
data class PhotoAdjustments(
    val exposure: Float = 0f,
    val highlights: Float = 0f,
    val shadows: Float = 0f,
    val contrast: Float = 0f,
    val brightness: Float = 0f,
    val saturation: Float = 0f,
    val vibrance: Float = 0f,
    val temperature: Float = 0f,
    val tint: Float = 0f,
    val sharpness: Float = 0f,
    val noise: Float = 0f,
    val vignette: Float = 0f,
    val grain: Float = 0f
)

/**
 * 照片显示模式
 */
enum class PhotoDisplayMode {
    GRID_SMALL,    // 小网格
    GRID_MEDIUM,   // 中等网格
    GRID_LARGE,    // 大网格
    LIST           // 列表模式
}

/**
 * 照片排序方式
 */
enum class PhotoSortOrder {
    DATE_TAKEN_DESC,    // 拍摄时间降序
    DATE_TAKEN_ASC,     // 拍摄时间升序
    DATE_ADDED_DESC,    // 添加时间降序
    DATE_ADDED_ASC,     // 添加时间升序
    NAME_ASC,           // 文件名升序
    NAME_DESC,          // 文件名降序
    SIZE_DESC,          // 文件大小降序
    SIZE_ASC            // 文件大小升序
}

/**
 * 照片筛选条件
 */
data class PhotoFilter(
    val dateRange: DateRange? = null,
    val location: String? = null,
    val cameraMake: String? = null,
    val cameraModel: String? = null,
    val filterUsed: String? = null,
    val isYucramPhoto: Boolean? = null,
    val isFavorite: Boolean? = null,
    val tags: List<String> = emptyList(),
    val albumIds: List<String> = emptyList(),
    val minFileSize: Long? = null,
    val maxFileSize: Long? = null,
    val orientation: PhotoOrientation? = null
)

/**
 * 日期范围
 */
data class DateRange(
    val startDate: LocalDateTime,
    val endDate: LocalDateTime
)

/**
 * 照片方向
 */
enum class PhotoOrientation {
    PORTRAIT,   // 竖向
    LANDSCAPE,  // 横向
    SQUARE      // 正方形
}

/**
 * 照片操作结果
 */
sealed class PhotoOperationResult {
    object Success : PhotoOperationResult()
    data class Error(val message: String, val exception: Throwable? = null) : PhotoOperationResult()
    data class PartialSuccess(val successCount: Int, val failureCount: Int, val errors: List<String>) : PhotoOperationResult()
}

/**
 * 照片加载状态
 */
sealed class PhotoLoadState {
    object Loading : PhotoLoadState()
    data class Success(val photos: List<Photo>) : PhotoLoadState()
    data class Error(val message: String, val exception: Throwable? = null) : PhotoLoadState()
    object Empty : PhotoLoadState()
}

/**
 * 照片选择状态
 */
data class PhotoSelectionState(
    val selectedPhotos: Set<String> = emptySet(),
    val isSelectionMode: Boolean = false
) {
    val selectedCount: Int get() = selectedPhotos.size

    fun isSelected(photoId: String): Boolean = selectedPhotos.contains(photoId)

    fun toggleSelection(photoId: String): PhotoSelectionState {
        return if (selectedPhotos.contains(photoId)) {
            copy(selectedPhotos = selectedPhotos - photoId)
        } else {
            copy(selectedPhotos = selectedPhotos + photoId)
        }
    }

    fun selectAll(photoIds: List<String>): PhotoSelectionState {
        return copy(selectedPhotos = photoIds.toSet())
    }

    fun clearSelection(): PhotoSelectionState {
        return copy(selectedPhotos = emptySet(), isSelectionMode = false)
    }

    fun enterSelectionMode(photoId: String? = null): PhotoSelectionState {
        return copy(
            isSelectionMode = true,
            selectedPhotos = if (photoId != null) setOf(photoId) else selectedPhotos
        )
    }
}

/**
 * 照片统计信息
 */
data class PhotoStats(
    val totalCount: Int,
    val yucramPhotoCount: Int,
    val favoriteCount: Int,
    val totalSize: Long,
    val oldestPhoto: LocalDateTime?,
    val newestPhoto: LocalDateTime?,
    val mostUsedFilter: String?,
    val averageFileSize: Long,
    val photosByMonth: Map<String, Int> = emptyMap(), // "2024-01" -> count
    val photosByCamera: Map<String, Int> = emptyMap() // "iPhone 15 Pro" -> count
)
