package com.qxyu.yucram.di

import android.content.Context
import androidx.room.Room
import com.qxyu.yucram.data.database.dao.CropHistoryDao
import com.qxyu.yucram.data.database.dao.CropPresetDao
import com.qxyu.yucram.data.local.YucramDatabase
import com.qxyu.yucram.data.local.dao.PhotoDao
import com.qxyu.yucram.data.local.dao.SettingsDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt模块 - 数据库相关依赖注入
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideYucramDatabase(
        @ApplicationContext context: Context
    ): YucramDatabase {
        return Room.databaseBuilder(
            context,
            YucramDatabase::class.java,
            "yucram_database"
        ).build()
    }

    @Provides
    fun providePhotoDao(database: YucramDatabase): PhotoDao {
        return database.photoDao()
    }

    @Provides
    fun provideSettingsDao(database: YucramDatabase): SettingsDao {
        return database.settingsDao()
    }

    @Provides
    fun provideCropHistoryDao(database: YucramDatabase): CropHistoryDao {
        return database.cropHistoryDao()
    }

    @Provides
    fun provideCropPresetDao(database: YucramDatabase): CropPresetDao {
        return database.cropPresetDao()
    }
}
