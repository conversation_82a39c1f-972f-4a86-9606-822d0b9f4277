package com.qxyu.yucram.presentation.photoedit;

/**
 * 照片编辑ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000e\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J \u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\f2\u0006\u0010!\u001a\u00020\f2\u0006\u0010\"\u001a\u00020\u0007H\u0002J\u001e\u0010#\u001a\u00020\u00102\u0006\u0010\u001a\u001a\u00020\u00102\u0006\u0010$\u001a\u00020\fH\u0082@\u00a2\u0006\u0002\u0010%J\u000e\u0010&\u001a\u00020\u001f2\u0006\u0010\'\u001a\u00020(J\u0006\u0010)\u001a\u00020*J\u0006\u0010+\u001a\u00020*J\u0006\u0010,\u001a\u00020\u001fJ\u0006\u0010-\u001a\u00020\u001fJ \u0010.\u001a\u00020/2\u0006\u0010\"\u001a\u00020\u00072\u0006\u0010 \u001a\u00020\f2\u0006\u0010!\u001a\u00020\fH\u0002J\b\u00100\u001a\u00020/H\u0002J\b\u00101\u001a\u00020/H\u0002J\u000e\u00102\u001a\u00020\u001f2\u0006\u00103\u001a\u000204J\u0006\u00105\u001a\u00020*J\u0010\u00106\u001a\u00020\f2\u0006\u0010\u001a\u001a\u00020\u0010H\u0002J\u000e\u00107\u001a\u00020\u001f2\u0006\u00108\u001a\u00020/J\u0006\u00109\u001a\u00020\u001fJ\u0006\u0010:\u001a\u00020\u001fJ\u0016\u0010;\u001a\u00020(2\u0006\u0010<\u001a\u00020/2\u0006\u0010=\u001a\u00020/J\u0006\u0010>\u001a\u00020\u001fJ\u000e\u0010?\u001a\u00020\u001f2\u0006\u0010\"\u001a\u00020\u0007J\u0006\u0010@\u001a\u00020\u001fJ\u000e\u0010A\u001a\u00020\u001f2\u0006\u0010!\u001a\u00020\fR\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\f0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0016R\u000e\u0010\u0019\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0016R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00120\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0016\u00a8\u0006B"}, d2 = {"Lcom/qxyu/yucram/presentation/photoedit/PhotoEditViewModel;", "Landroidx/lifecycle/ViewModel;", "photoRepository", "Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "(Lcom/qxyu/yucram/domain/repository/PhotoRepository;)V", "_currentTool", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/domain/model/EditTool;", "_editHistory", "", "Lcom/qxyu/yucram/domain/model/EditHistory;", "_editParams", "Lcom/qxyu/yucram/domain/model/PhotoEditParams;", "_historyIndex", "", "_photo", "Lcom/qxyu/yucram/domain/model/Photo;", "_uiState", "Lcom/qxyu/yucram/presentation/photoedit/PhotoEditUiState;", "currentTool", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentTool", "()Lkotlinx/coroutines/flow/StateFlow;", "editParams", "getEditParams", "originalParams", "photo", "getPhoto", "uiState", "getUiState", "addToHistory", "", "oldParams", "newParams", "tool", "applyEditParams", "params", "(Lcom/qxyu/yucram/domain/model/Photo;Lcom/qxyu/yucram/domain/model/PhotoEditParams;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyPreset", "preset", "Lcom/qxyu/yucram/domain/model/EditPreset;", "canRedo", "", "canUndo", "clearError", "clearMessage", "generateHistoryDescription", "", "generateHistoryId", "generatePresetId", "handleToolInteraction", "interaction", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction;", "hasChanges", "loadExistingEditParams", "loadPhoto", "photoId", "redo", "resetEdit", "saveAsPreset", "name", "description", "saveEdit", "selectTool", "undo", "updateEditParams", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class PhotoEditViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.repository.PhotoRepository photoRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.photoedit.PhotoEditUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.photoedit.PhotoEditUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.Photo> _photo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> photo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.PhotoEditParams> _editParams = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.PhotoEditParams> editParams = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.EditTool> _currentTool = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.EditTool> currentTool = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.qxyu.yucram.domain.model.EditHistory>> _editHistory = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _historyIndex = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.PhotoEditParams originalParams = null;
    
    @javax.inject.Inject()
    public PhotoEditViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.repository.PhotoRepository photoRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.photoedit.PhotoEditUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.Photo> getPhoto() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.PhotoEditParams> getEditParams() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.EditTool> getCurrentTool() {
        return null;
    }
    
    public final void loadPhoto(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId) {
    }
    
    public final void selectTool(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.EditTool tool) {
    }
    
    public final void updateEditParams(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams newParams) {
    }
    
    public final void handleToolInteraction(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.ToolInteraction interaction) {
    }
    
    private final void addToHistory(com.qxyu.yucram.domain.model.PhotoEditParams oldParams, com.qxyu.yucram.domain.model.PhotoEditParams newParams, com.qxyu.yucram.domain.model.EditTool tool) {
    }
    
    public final void undo() {
    }
    
    public final void redo() {
    }
    
    public final boolean canUndo() {
        return false;
    }
    
    public final boolean canRedo() {
        return false;
    }
    
    public final void resetEdit() {
    }
    
    public final boolean hasChanges() {
        return false;
    }
    
    public final void saveEdit() {
    }
    
    public final void applyPreset(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.EditPreset preset) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.EditPreset saveAsPreset(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String description) {
        return null;
    }
    
    private final com.qxyu.yucram.domain.model.PhotoEditParams loadExistingEditParams(com.qxyu.yucram.domain.model.Photo photo) {
        return null;
    }
    
    private final java.lang.Object applyEditParams(com.qxyu.yucram.domain.model.Photo photo, com.qxyu.yucram.domain.model.PhotoEditParams params, kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.Photo> $completion) {
        return null;
    }
    
    private final java.lang.String generateHistoryId() {
        return null;
    }
    
    private final java.lang.String generatePresetId() {
        return null;
    }
    
    private final java.lang.String generateHistoryDescription(com.qxyu.yucram.domain.model.EditTool tool, com.qxyu.yucram.domain.model.PhotoEditParams oldParams, com.qxyu.yucram.domain.model.PhotoEditParams newParams) {
        return null;
    }
    
    public final void clearError() {
    }
    
    public final void clearMessage() {
    }
}