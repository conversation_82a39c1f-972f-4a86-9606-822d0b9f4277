package com.qxyu.yucram.data.database.dao

import androidx.room.*
import com.qxyu.yucram.data.database.entity.CropHistoryEntity
import kotlinx.coroutines.flow.Flow

/**
 * 裁剪历史记录DAO
 */
@Dao
interface CropHistoryDao {
    
    /**
     * 获取指定照片的所有裁剪历史记录
     */
    @Query("SELECT * FROM crop_history WHERE photoId = :photoId ORDER BY timestamp DESC")
    fun getHistoryByPhotoId(photoId: String): Flow<List<CropHistoryEntity>>
    
    /**
     * 获取所有裁剪历史记录
     */
    @Query("SELECT * FROM crop_history ORDER BY timestamp DESC")
    fun getAllHistory(): Flow<List<CropHistoryEntity>>
    
    /**
     * 根据ID获取裁剪历史记录
     */
    @Query("SELECT * FROM crop_history WHERE id = :historyId")
    suspend fun getHistoryById(historyId: String): CropHistoryEntity?
    
    /**
     * 插入裁剪历史记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHistory(history: CropHistoryEntity)
    
    /**
     * 批量插入裁剪历史记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHistories(histories: List<CropHistoryEntity>)
    
    /**
     * 更新裁剪历史记录
     */
    @Update
    suspend fun updateHistory(history: CropHistoryEntity)
    
    /**
     * 删除指定的裁剪历史记录
     */
    @Query("DELETE FROM crop_history WHERE id = :historyId")
    suspend fun deleteHistory(historyId: String)
    
    /**
     * 删除指定照片的所有裁剪历史记录
     */
    @Query("DELETE FROM crop_history WHERE photoId = :photoId")
    suspend fun deleteHistoryByPhotoId(photoId: String)
    
    /**
     * 清空所有裁剪历史记录
     */
    @Query("DELETE FROM crop_history")
    suspend fun clearAllHistory()
    
    /**
     * 获取历史记录数量
     */
    @Query("SELECT COUNT(*) FROM crop_history")
    suspend fun getHistoryCount(): Int
    
    /**
     * 获取指定照片的历史记录数量
     */
    @Query("SELECT COUNT(*) FROM crop_history WHERE photoId = :photoId")
    suspend fun getHistoryCountByPhotoId(photoId: String): Int
    
    /**
     * 删除最旧的历史记录（保持历史记录数量在限制内）
     */
    @Query("DELETE FROM crop_history WHERE id IN (SELECT id FROM crop_history ORDER BY timestamp ASC LIMIT :count)")
    suspend fun deleteOldestHistory(count: Int)
    
    /**
     * 获取最近的历史记录
     */
    @Query("SELECT * FROM crop_history ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentHistory(limit: Int): List<CropHistoryEntity>
    
    /**
     * 根据时间范围获取历史记录
     */
    @Query("SELECT * FROM crop_history WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getHistoryByTimeRange(startTime: Long, endTime: Long): Flow<List<CropHistoryEntity>>
    
    /**
     * 搜索历史记录（根据描述）
     */
    @Query("SELECT * FROM crop_history WHERE description LIKE '%' || :query || '%' ORDER BY timestamp DESC")
    fun searchHistory(query: String): Flow<List<CropHistoryEntity>>
}
