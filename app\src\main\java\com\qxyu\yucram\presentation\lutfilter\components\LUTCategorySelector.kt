package com.qxyu.yucram.presentation.lutfilter.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.LUTCategory

/**
 * LUT滤镜分类选择器
 */
@Composable
fun LUTCategorySelector(
    currentCategory: LUTCategory?,
    onCategorySelected: (LUTCategory?) -> Unit,
    modifier: Modifier = Modifier
) {
    val categories = listOf(
        null to ("全部" to Icons.Filled.GridView),
        LUTCategory.CINEMATIC to ("电影" to Icons.Filled.Movie),
        LUTCategory.VINTAGE to ("复古" to Icons.Filled.CameraRoll),
        LUTCategory.PORTRAIT to ("人像" to Icons.Filled.Person),
        LUTCategory.LANDSCAPE to ("风景" to Icons.Filled.Landscape),
        LUTCategory.STREET to ("街拍" to Icons.Filled.LocationCity),
        LUTCategory.FASHION to ("时尚" to Icons.Filled.Style),
        LUTCategory.MOODY to ("情绪" to Icons.Filled.Mood),
        LUTCategory.BRIGHT to ("明亮" to Icons.Filled.WbSunny),
        LUTCategory.DARK to ("暗黑" to Icons.Filled.Brightness2),
        LUTCategory.WARM to ("暖色" to Icons.Filled.Whatshot),
        LUTCategory.COOL to ("冷色" to Icons.Filled.AcUnit),
        LUTCategory.MONOCHROME to ("黑白" to Icons.Filled.Contrast),
        LUTCategory.CREATIVE to ("创意" to Icons.Filled.AutoAwesome),
        LUTCategory.PROFESSIONAL to ("专业" to Icons.Filled.WorkspacePremium)
    )
    
    LazyRow(
        modifier = modifier
            .background(Color.Black.copy(alpha = 0.8f))
            .padding(vertical = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(categories) { (category, nameAndIcon) ->
            val (name, icon) = nameAndIcon
            CategoryChip(
                category = category,
                name = name,
                icon = icon,
                isSelected = currentCategory == category,
                onClick = { onCategorySelected(category) }
            )
        }
    }
}

/**
 * 分类芯片
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoryChip(
    category: LUTCategory?,
    name: String,
    icon: ImageVector,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    FilterChip(
        onClick = onClick,
        label = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Text(
                    text = name,
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                )
            }
        },
        selected = isSelected,
        colors = FilterChipDefaults.filterChipColors(
            selectedContainerColor = MaterialTheme.colorScheme.primary,
            selectedLabelColor = Color.White,
            selectedLeadingIconColor = Color.White,
            containerColor = Color.White.copy(alpha = 0.1f),
            labelColor = Color.White,
            iconColor = Color.White
        ),
        border = FilterChipDefaults.filterChipBorder(
            enabled = true,
            selected = isSelected,
            borderColor = if (isSelected) MaterialTheme.colorScheme.primary 
                         else Color.White.copy(alpha = 0.3f),
            selectedBorderColor = MaterialTheme.colorScheme.primary
        ),
        modifier = modifier
    )
}

/**
 * 分类统计信息
 */
@Composable
fun CategoryStats(
    category: LUTCategory?,
    filterCount: Int,
    modifier: Modifier = Modifier
) {
    if (filterCount > 0) {
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.7f)
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            Row(
                modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Icon(
                    imageVector = Icons.Filled.FilterAlt,
                    contentDescription = null,
                    modifier = Modifier.size(14.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = "$filterCount",
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
        }
    }
}

/**
 * 分类描述
 */
@Composable
fun CategoryDescription(
    category: LUTCategory?,
    modifier: Modifier = Modifier
) {
    if (category != null) {
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.7f)
            ),
            shape = RoundedCornerShape(8.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = category.displayName,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = category.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * 快速分类切换
 */
@Composable
fun QuickCategorySwitch(
    currentCategory: LUTCategory?,
    onCategorySelected: (LUTCategory?) -> Unit,
    modifier: Modifier = Modifier
) {
    val quickCategories = listOf(
        null to "全部",
        LUTCategory.CINEMATIC to "电影",
        LUTCategory.VINTAGE to "复古",
        LUTCategory.PORTRAIT to "人像",
        LUTCategory.WARM to "暖色",
        LUTCategory.COOL to "冷色"
    )
    
    Row(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.8f),
                RoundedCornerShape(20.dp)
            )
            .padding(4.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        quickCategories.forEach { (category, name) ->
            val isSelected = currentCategory == category
            
            Button(
                onClick = { onCategorySelected(category) },
                colors = ButtonDefaults.buttonColors(
                    containerColor = if (isSelected) MaterialTheme.colorScheme.primary
                                   else Color.Transparent,
                    contentColor = if (isSelected) Color.White
                                 else Color.White.copy(alpha = 0.7f)
                ),
                contentPadding = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
                modifier = Modifier.height(32.dp)
            ) {
                Text(
                    text = name,
                    style = MaterialTheme.typography.bodySmall,
                    fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
                )
            }
        }
    }
}

/**
 * 分类搜索
 */
@Composable
fun CategorySearch(
    searchQuery: String,
    onSearchQueryChanged: (String) -> Unit,
    onClearSearch: () -> Unit,
    modifier: Modifier = Modifier
) {
    OutlinedTextField(
        value = searchQuery,
        onValueChange = onSearchQueryChanged,
        placeholder = {
            Text(
                text = "搜索滤镜...",
                color = Color.White.copy(alpha = 0.5f)
            )
        },
        leadingIcon = {
            Icon(
                imageVector = Icons.Filled.Search,
                contentDescription = "搜索",
                tint = Color.White.copy(alpha = 0.7f)
            )
        },
        trailingIcon = {
            if (searchQuery.isNotEmpty()) {
                IconButton(onClick = onClearSearch) {
                    Icon(
                        imageVector = Icons.Filled.Clear,
                        contentDescription = "清除",
                        tint = Color.White.copy(alpha = 0.7f)
                    )
                }
            }
        },
        colors = OutlinedTextFieldDefaults.colors(
            focusedTextColor = Color.White,
            unfocusedTextColor = Color.White,
            focusedBorderColor = MaterialTheme.colorScheme.primary,
            unfocusedBorderColor = Color.White.copy(alpha = 0.3f),
            cursorColor = MaterialTheme.colorScheme.primary
        ),
        singleLine = true,
        modifier = modifier
    )
}

/**
 * 分类过滤器
 */
@Composable
fun CategoryFilter(
    selectedCategories: List<LUTCategory>,
    onCategoriesChanged: (List<LUTCategory>) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
    ) {
        Text(
            text = "筛选分类",
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(LUTCategory.values()) { category ->
                val isSelected = category in selectedCategories
                
                FilterChip(
                    onClick = {
                        val newCategories = if (isSelected) {
                            selectedCategories - category
                        } else {
                            selectedCategories + category
                        }
                        onCategoriesChanged(newCategories)
                    },
                    label = {
                        Text(
                            text = category.displayName,
                            style = MaterialTheme.typography.bodySmall
                        )
                    },
                    selected = isSelected,
                    colors = FilterChipDefaults.filterChipColors(
                        selectedContainerColor = MaterialTheme.colorScheme.primary,
                        selectedLabelColor = Color.White
                    )
                )
            }
        }
        
        if (selectedCategories.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            
            TextButton(
                onClick = { onCategoriesChanged(emptyList()) }
            ) {
                Text(
                    text = "清除筛选",
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}
