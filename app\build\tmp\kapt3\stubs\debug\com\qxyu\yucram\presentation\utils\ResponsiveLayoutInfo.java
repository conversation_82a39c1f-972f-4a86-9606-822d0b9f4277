package com.qxyu.yucram.presentation.utils;

/**
 * 响应式布局信息
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b&\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001Be\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000e\u001a\u00020\f\u0012\u0006\u0010\u000f\u001a\u00020\f\u0012\u0006\u0010\u0010\u001a\u00020\f\u0012\u0006\u0010\u0011\u001a\u00020\f\u0012\u0006\u0010\u0012\u001a\u00020\f\u00a2\u0006\u0002\u0010\u0013J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\fH\u00c6\u0003J\t\u0010!\u001a\u00020\fH\u00c6\u0003J\t\u0010\"\u001a\u00020\fH\u00c6\u0003J\t\u0010#\u001a\u00020\u0005H\u00c6\u0003J\t\u0010$\u001a\u00020\u0007H\u00c6\u0003J\u0016\u0010%\u001a\u00020\tH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b&\u0010\u001aJ\u0016\u0010\'\u001a\u00020\tH\u00c6\u0003\u00f8\u0001\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b(\u0010\u001aJ\t\u0010)\u001a\u00020\fH\u00c6\u0003J\t\u0010*\u001a\u00020\fH\u00c6\u0003J\t\u0010+\u001a\u00020\fH\u00c6\u0003J\t\u0010,\u001a\u00020\fH\u00c6\u0003J\u008b\u0001\u0010-\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\f2\b\b\u0002\u0010\u0010\u001a\u00020\f2\b\b\u0002\u0010\u0011\u001a\u00020\f2\b\b\u0002\u0010\u0012\u001a\u00020\fH\u00c6\u0001\u00f8\u0001\u0000\u00a2\u0006\u0004\b.\u0010/J\u0013\u00100\u001a\u00020\f2\b\u00101\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00102\u001a\u000203H\u00d6\u0001J\t\u00104\u001a\u000205H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0016R\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0016R\u0011\u0010\u0010\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0016R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u0016R\u0011\u0010\u0011\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0016R\u0011\u0010\u000f\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0016R\u0011\u0010\u0012\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0019\u0010\n\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u001b\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0019\u0010\b\u001a\u00020\t\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\n\n\u0002\u0010\u001b\u001a\u0004\b\u001e\u0010\u001a\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u00066"}, d2 = {"Lcom/qxyu/yucram/presentation/utils/ResponsiveLayoutInfo;", "", "screenSize", "Lcom/qxyu/yucram/presentation/utils/ScreenSize;", "orientation", "Lcom/qxyu/yucram/presentation/utils/ScreenOrientation;", "deviceType", "Lcom/qxyu/yucram/presentation/utils/DeviceType;", "screenWidth", "Landroidx/compose/ui/unit/Dp;", "screenHeight", "isCompact", "", "isMedium", "isExpanded", "isPortrait", "isLandscape", "isPhone", "isTablet", "(Lcom/qxyu/yucram/presentation/utils/ScreenSize;Lcom/qxyu/yucram/presentation/utils/ScreenOrientation;Lcom/qxyu/yucram/presentation/utils/DeviceType;FFZZZZZZZLkotlin/jvm/internal/DefaultConstructorMarker;)V", "getDeviceType", "()Lcom/qxyu/yucram/presentation/utils/DeviceType;", "()Z", "getOrientation", "()Lcom/qxyu/yucram/presentation/utils/ScreenOrientation;", "getScreenHeight-D9Ej5fM", "()F", "F", "getScreenSize", "()Lcom/qxyu/yucram/presentation/utils/ScreenSize;", "getScreenWidth-D9Ej5fM", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component4-D9Ej5fM", "component5", "component5-D9Ej5fM", "component6", "component7", "component8", "component9", "copy", "copy-ikN4xI4", "(Lcom/qxyu/yucram/presentation/utils/ScreenSize;Lcom/qxyu/yucram/presentation/utils/ScreenOrientation;Lcom/qxyu/yucram/presentation/utils/DeviceType;FFZZZZZZZ)Lcom/qxyu/yucram/presentation/utils/ResponsiveLayoutInfo;", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class ResponsiveLayoutInfo {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.presentation.utils.ScreenSize screenSize = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.presentation.utils.ScreenOrientation orientation = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.presentation.utils.DeviceType deviceType = null;
    private final float screenWidth = 0.0F;
    private final float screenHeight = 0.0F;
    private final boolean isCompact = false;
    private final boolean isMedium = false;
    private final boolean isExpanded = false;
    private final boolean isPortrait = false;
    private final boolean isLandscape = false;
    private final boolean isPhone = false;
    private final boolean isTablet = false;
    
    private ResponsiveLayoutInfo(com.qxyu.yucram.presentation.utils.ScreenSize screenSize, com.qxyu.yucram.presentation.utils.ScreenOrientation orientation, com.qxyu.yucram.presentation.utils.DeviceType deviceType, float screenWidth, float screenHeight, boolean isCompact, boolean isMedium, boolean isExpanded, boolean isPortrait, boolean isLandscape, boolean isPhone, boolean isTablet) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.utils.ScreenSize getScreenSize() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.utils.ScreenOrientation getOrientation() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.utils.DeviceType getDeviceType() {
        return null;
    }
    
    public final boolean isCompact() {
        return false;
    }
    
    public final boolean isMedium() {
        return false;
    }
    
    public final boolean isExpanded() {
        return false;
    }
    
    public final boolean isPortrait() {
        return false;
    }
    
    public final boolean isLandscape() {
        return false;
    }
    
    public final boolean isPhone() {
        return false;
    }
    
    public final boolean isTablet() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.utils.ScreenSize component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.utils.ScreenOrientation component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.utils.DeviceType component3() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}