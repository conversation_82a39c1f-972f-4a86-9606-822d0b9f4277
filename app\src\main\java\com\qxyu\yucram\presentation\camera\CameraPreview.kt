package com.qxyu.yucram.presentation.camera

import android.view.SurfaceHolder
import android.view.SurfaceView
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.qxyu.yucram.camera.CameraManager

/**
 * 相机预览组件
 * 使用SurfaceView显示相机预览
 */
@Composable
fun CameraPreview(
    cameraManager: CameraManager,
    modifier: Modifier = Modifier
) {
    val lifecycleOwner = LocalLifecycleOwner.current
    
    // 监听生命周期
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_CREATE -> {
                    cameraManager.initialize()
                }
                Lifecycle.Event.ON_RESUME -> {
                    cameraManager.openCamera()
                }
                Lifecycle.Event.ON_PAUSE -> {
                    cameraManager.closeCamera()
                }
                else -> {}
            }
        }
        
        lifecycleOwner.lifecycle.addObserver(observer)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    
    AndroidView(
        factory = { ctx ->
            SurfaceView(ctx).apply {
                holder.addCallback(object : SurfaceHolder.Callback {
                    override fun surfaceCreated(holder: SurfaceHolder) {
                        cameraManager.setPreviewSurface(holder.surface)
                    }
                    
                    override fun surfaceChanged(
                        holder: SurfaceHolder,
                        format: Int,
                        width: Int,
                        height: Int
                    ) {
                        // Surface尺寸改变时的处理
                    }
                    
                    override fun surfaceDestroyed(holder: SurfaceHolder) {
                        // Surface销毁时的处理
                    }
                })
            }
        },
        modifier = modifier.fillMaxSize()
    )
}
