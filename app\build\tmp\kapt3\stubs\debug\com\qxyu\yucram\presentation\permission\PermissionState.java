package com.qxyu.yucram.presentation.permission;

/**
 * 权限状态数据类
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0016\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0005\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003JE\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00032\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u001cH\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000bR\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u000bR\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u000b\u00a8\u0006\u001d"}, d2 = {"Lcom/qxyu/yucram/presentation/permission/PermissionState;", "", "cameraPermissionGranted", "", "storagePermissionGranted", "locationPermissionGranted", "recordAudioPermissionGranted", "allCorePermissionsGranted", "isLoading", "(ZZZZZZ)V", "getAllCorePermissionsGranted", "()Z", "getCameraPermissionGranted", "getLocationPermissionGranted", "getRecordAudioPermissionGranted", "getStoragePermissionGranted", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class PermissionState {
    private final boolean cameraPermissionGranted = false;
    private final boolean storagePermissionGranted = false;
    private final boolean locationPermissionGranted = false;
    private final boolean recordAudioPermissionGranted = false;
    private final boolean allCorePermissionsGranted = false;
    private final boolean isLoading = false;
    
    public PermissionState(boolean cameraPermissionGranted, boolean storagePermissionGranted, boolean locationPermissionGranted, boolean recordAudioPermissionGranted, boolean allCorePermissionsGranted, boolean isLoading) {
        super();
    }
    
    public final boolean getCameraPermissionGranted() {
        return false;
    }
    
    public final boolean getStoragePermissionGranted() {
        return false;
    }
    
    public final boolean getLocationPermissionGranted() {
        return false;
    }
    
    public final boolean getRecordAudioPermissionGranted() {
        return false;
    }
    
    public final boolean getAllCorePermissionsGranted() {
        return false;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public PermissionState() {
        super();
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.presentation.permission.PermissionState copy(boolean cameraPermissionGranted, boolean storagePermissionGranted, boolean locationPermissionGranted, boolean recordAudioPermissionGranted, boolean allCorePermissionsGranted, boolean isLoading) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}