package com.qxyu.yucram.presentation.borderwater.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*
import kotlin.math.*

/**
 * 边框和水印预览组件
 */
@Composable
fun BorderWatermarkPreview(
    photo: Photo,
    settings: BorderWatermarkSettings,
    modifier: Modifier = Modifier
) {
    var scale by remember { mutableStateOf(1f) }
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }
    var showControls by remember { mutableStateOf(true) }
    
    val density = LocalDensity.current
    val textMeasurer = rememberTextMeasurer()
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // 主要预览区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(Color.Black)
        ) {
            // 原始照片
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(photo.filePath)
                    .crossfade(true)
                    .build(),
                contentDescription = photo.fileName,
                contentScale = ContentScale.Fit,
                modifier = Modifier
                    .fillMaxSize()
                    .graphicsLayer(
                        scaleX = scale,
                        scaleY = scale,
                        translationX = offsetX,
                        translationY = offsetY
                    )
                    .pointerInput(Unit) {
                        detectTransformGestures(
                            onGesture = { _, pan, zoom, _ ->
                                scale = (scale * zoom).coerceIn(0.5f, 3f)
                                if (scale > 1f) {
                                    offsetX += pan.x
                                    offsetY += pan.y
                                } else {
                                    offsetX = 0f
                                    offsetY = 0f
                                }
                            }
                        )
                    }
            )
            
            // 边框和水印覆盖层
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                // 绘制边框
                if (settings.borderSettings.isEnabled) {
                    drawBorder(settings.borderSettings, size)
                }
                
                // 绘制水印
                if (settings.watermarkSettings.isEnabled) {
                    drawWatermark(
                        settings.watermarkSettings,
                        size,
                        density,
                        textMeasurer
                    )
                }
            }
        }
        
        // 预览控制栏
        if (showControls) {
            PreviewControls(
                onResetZoom = {
                    scale = 1f
                    offsetX = 0f
                    offsetY = 0f
                },
                onToggleControls = { showControls = !showControls },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(24.dp)
            )
        }
        
        // 设置信息显示
        if (showControls && (settings.borderSettings.isEnabled || settings.watermarkSettings.isEnabled)) {
            SettingsInfo(
                settings = settings,
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(24.dp)
            )
        }
        
        // 点击切换控制显示
        Box(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTransformGestures { _, _, _, _ ->
                        showControls = !showControls
                    }
                }
        )
    }
}

/**
 * 绘制边框
 */
private fun DrawScope.drawBorder(
    borderSettings: BorderSettings,
    canvasSize: Size
) {
    val borderWidth = borderSettings.width
    val borderColor = borderSettings.color
    val cornerRadius = borderSettings.cornerRadius
    
    when (borderSettings.borderType) {
        BorderType.SOLID -> {
            drawSolidBorder(borderSettings, canvasSize)
        }
        
        BorderType.GRADIENT -> {
            drawGradientBorder(borderSettings, canvasSize)
        }
        
        BorderType.PATTERN -> {
            drawPatternBorder(borderSettings, canvasSize)
        }
        
        BorderType.DECORATIVE -> {
            drawDecorativeBorder(borderSettings, canvasSize)
        }
    }
    
    // 绘制阴影
    if (borderSettings.shadowEnabled) {
        drawBorderShadow(borderSettings, canvasSize)
    }
}

/**
 * 绘制纯色边框
 */
private fun DrawScope.drawSolidBorder(
    borderSettings: BorderSettings,
    canvasSize: Size
) {
    val borderWidth = borderSettings.width
    val borderColor = borderSettings.color
    val cornerRadius = borderSettings.cornerRadius
    
    // 外边框
    drawRoundRect(
        color = borderColor,
        size = canvasSize,
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(cornerRadius, cornerRadius)
    )
    
    // 内部透明区域
    drawRoundRect(
        color = Color.Transparent,
        topLeft = Offset(borderWidth, borderWidth),
        size = Size(
            canvasSize.width - borderWidth * 2,
            canvasSize.height - borderWidth * 2
        ),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(
            maxOf(0f, cornerRadius - borderWidth),
            maxOf(0f, cornerRadius - borderWidth)
        )
    )
}

/**
 * 绘制渐变边框
 */
private fun DrawScope.drawGradientBorder(
    borderSettings: BorderSettings,
    canvasSize: Size
) {
    val borderWidth = borderSettings.width
    val gradientColors = borderSettings.gradientColors
    val gradientAngle = borderSettings.gradientAngle
    
    val gradient = Brush.linearGradient(
        colors = gradientColors,
        start = Offset(0f, 0f),
        end = Offset(
            cos(Math.toRadians(gradientAngle.toDouble())).toFloat() * canvasSize.width,
            sin(Math.toRadians(gradientAngle.toDouble())).toFloat() * canvasSize.height
        )
    )
    
    drawRoundRect(
        brush = gradient,
        size = canvasSize,
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(
            borderSettings.cornerRadius,
            borderSettings.cornerRadius
        )
    )
    
    // 内部透明区域
    drawRoundRect(
        color = Color.Transparent,
        topLeft = Offset(borderWidth, borderWidth),
        size = Size(
            canvasSize.width - borderWidth * 2,
            canvasSize.height - borderWidth * 2
        ),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(
            maxOf(0f, borderSettings.cornerRadius - borderWidth),
            maxOf(0f, borderSettings.cornerRadius - borderWidth)
        )
    )
}

/**
 * 绘制图案边框
 */
private fun DrawScope.drawPatternBorder(
    borderSettings: BorderSettings,
    canvasSize: Size
) {
    val borderWidth = borderSettings.width
    val patternType = borderSettings.patternType
    val patternColor = borderSettings.patternColor
    val patternSize = borderSettings.patternSize
    
    // 先绘制基础边框
    drawSolidBorder(borderSettings, canvasSize)
    
    // 绘制图案
    when (patternType) {
        BorderPattern.DOTS -> drawDotPattern(borderWidth, patternColor, patternSize, canvasSize)
        BorderPattern.LINES -> drawLinePattern(borderWidth, patternColor, patternSize, canvasSize)
        BorderPattern.DASHES -> drawDashPattern(borderWidth, patternColor, patternSize, canvasSize)
        BorderPattern.WAVES -> drawWavePattern(borderWidth, patternColor, patternSize, canvasSize)
        BorderPattern.ZIGZAG -> drawZigzagPattern(borderWidth, patternColor, patternSize, canvasSize)
        else -> {} // 其他图案待实现
    }
}

/**
 * 绘制装饰边框
 */
private fun DrawScope.drawDecorativeBorder(
    borderSettings: BorderSettings,
    canvasSize: Size
) {
    val decorativeStyle = borderSettings.decorativeStyle
    val decorativeColor = borderSettings.decorativeColor
    val borderWidth = borderSettings.width
    
    // 先绘制基础边框
    drawSolidBorder(borderSettings, canvasSize)
    
    // 绘制装饰
    when (decorativeStyle) {
        DecorativeStyle.CORNER_ORNAMENTS -> {
            drawCornerOrnaments(decorativeColor, borderWidth, canvasSize)
        }
        DecorativeStyle.EDGE_ORNAMENTS -> {
            drawEdgeOrnaments(decorativeColor, borderWidth, canvasSize)
        }
        DecorativeStyle.VINTAGE_CORNERS -> {
            drawVintageCorners(decorativeColor, borderWidth, canvasSize)
        }
        else -> {} // 其他装饰待实现
    }
}

/**
 * 绘制边框阴影
 */
private fun DrawScope.drawBorderShadow(
    borderSettings: BorderSettings,
    canvasSize: Size
) {
    val shadowColor = borderSettings.shadowColor
    val shadowBlur = borderSettings.shadowBlur
    val shadowOffset = borderSettings.shadowOffset
    
    // TODO: 实现阴影绘制
    // 这里需要使用Paint的setShadowLayer方法
}

/**
 * 绘制水印
 */
private fun DrawScope.drawWatermark(
    watermarkSettings: WatermarkSettings,
    canvasSize: Size,
    density: androidx.compose.ui.unit.Density,
    textMeasurer: androidx.compose.ui.text.TextMeasurer
) {
    when (watermarkSettings.watermarkType) {
        WatermarkType.TEXT -> {
            drawTextWatermark(watermarkSettings, canvasSize, textMeasurer)
        }
        
        WatermarkType.LOGO -> {
            drawLogoWatermark(watermarkSettings, canvasSize)
        }
        
        WatermarkType.TIMESTAMP -> {
            drawTimestampWatermark(watermarkSettings, canvasSize, textMeasurer)
        }
        
        WatermarkType.CAMERA_INFO -> {
            drawCameraInfoWatermark(watermarkSettings, canvasSize, textMeasurer)
        }
        
        else -> {} // 其他类型待实现
    }
}

/**
 * 绘制文字水印
 */
private fun DrawScope.drawTextWatermark(
    watermarkSettings: WatermarkSettings,
    canvasSize: Size,
    textMeasurer: androidx.compose.ui.text.TextMeasurer
) {
    if (watermarkSettings.text.isBlank()) return
    
    val textStyle = TextStyle(
        fontSize = watermarkSettings.fontSize,
        fontWeight = watermarkSettings.fontWeight,
        color = watermarkSettings.textColor.copy(alpha = watermarkSettings.opacity)
    )
    
    val textLayoutResult = textMeasurer.measure(
        text = watermarkSettings.text,
        style = textStyle
    )
    
    val position = calculateWatermarkPosition(
        watermarkSettings.position,
        watermarkSettings.customX,
        watermarkSettings.customY,
        watermarkSettings.margin,
        textLayoutResult.size.width.toFloat(),
        textLayoutResult.size.height.toFloat(),
        canvasSize
    )
    
    // 绘制文字阴影
    if (watermarkSettings.textShadowEnabled) {
        val shadowOffset = watermarkSettings.textShadowOffset
        drawText(
            textLayoutResult = textMeasurer.measure(
                text = watermarkSettings.text,
                style = textStyle.copy(
                    color = watermarkSettings.textShadowColor.copy(alpha = watermarkSettings.opacity * 0.5f)
                )
            ),
            topLeft = Offset(
                position.x + shadowOffset.first,
                position.y + shadowOffset.second
            )
        )
    }
    
    // 绘制文字
    drawText(
        textLayoutResult = textLayoutResult,
        topLeft = position
    )
}

/**
 * 绘制Logo水印
 */
private fun DrawScope.drawLogoWatermark(
    watermarkSettings: WatermarkSettings,
    canvasSize: Size
) {
    val logoSize = watermarkSettings.logoSize
    val logoColor = watermarkSettings.logoColor.copy(alpha = watermarkSettings.opacity)
    
    val position = calculateWatermarkPosition(
        watermarkSettings.position,
        watermarkSettings.customX,
        watermarkSettings.customY,
        watermarkSettings.margin,
        logoSize,
        logoSize,
        canvasSize
    )
    
    // 绘制Logo（这里以Yucram Logo为例）
    when (watermarkSettings.logoType) {
        LogoType.YUCRAM -> {
            drawYucramLogo(position, logoSize, logoColor)
        }
        LogoType.CAMERA -> {
            drawCameraIcon(position, logoSize, logoColor)
        }
        LogoType.APERTURE -> {
            drawApertureIcon(position, logoSize, logoColor)
        }
        else -> {}
    }
}

/**
 * 计算水印位置
 */
private fun calculateWatermarkPosition(
    position: WatermarkPosition,
    customX: Float,
    customY: Float,
    margin: Float,
    watermarkWidth: Float,
    watermarkHeight: Float,
    canvasSize: Size
): Offset {
    return when (position) {
        WatermarkPosition.TOP_LEFT -> Offset(margin, margin)
        WatermarkPosition.TOP_CENTER -> Offset(
            (canvasSize.width - watermarkWidth) / 2,
            margin
        )
        WatermarkPosition.TOP_RIGHT -> Offset(
            canvasSize.width - watermarkWidth - margin,
            margin
        )
        WatermarkPosition.CENTER_LEFT -> Offset(
            margin,
            (canvasSize.height - watermarkHeight) / 2
        )
        WatermarkPosition.CENTER -> Offset(
            (canvasSize.width - watermarkWidth) / 2,
            (canvasSize.height - watermarkHeight) / 2
        )
        WatermarkPosition.CENTER_RIGHT -> Offset(
            canvasSize.width - watermarkWidth - margin,
            (canvasSize.height - watermarkHeight) / 2
        )
        WatermarkPosition.BOTTOM_LEFT -> Offset(
            margin,
            canvasSize.height - watermarkHeight - margin
        )
        WatermarkPosition.BOTTOM_CENTER -> Offset(
            (canvasSize.width - watermarkWidth) / 2,
            canvasSize.height - watermarkHeight - margin
        )
        WatermarkPosition.BOTTOM_RIGHT -> Offset(
            canvasSize.width - watermarkWidth - margin,
            canvasSize.height - watermarkHeight - margin
        )
        WatermarkPosition.CUSTOM -> Offset(
            customX * canvasSize.width,
            customY * canvasSize.height
        )
    }
}

// TODO: 实现具体的图案绘制方法
private fun DrawScope.drawDotPattern(borderWidth: Float, color: Color, size: Float, canvasSize: Size) {}
private fun DrawScope.drawLinePattern(borderWidth: Float, color: Color, size: Float, canvasSize: Size) {}
private fun DrawScope.drawDashPattern(borderWidth: Float, color: Color, size: Float, canvasSize: Size) {}
private fun DrawScope.drawWavePattern(borderWidth: Float, color: Color, size: Float, canvasSize: Size) {}
private fun DrawScope.drawZigzagPattern(borderWidth: Float, color: Color, size: Float, canvasSize: Size) {}
private fun DrawScope.drawCornerOrnaments(color: Color, borderWidth: Float, canvasSize: Size) {}
private fun DrawScope.drawEdgeOrnaments(color: Color, borderWidth: Float, canvasSize: Size) {}
private fun DrawScope.drawVintageCorners(color: Color, borderWidth: Float, canvasSize: Size) {}
private fun DrawScope.drawYucramLogo(position: Offset, size: Float, color: Color) {}
private fun DrawScope.drawCameraIcon(position: Offset, size: Float, color: Color) {}
private fun DrawScope.drawApertureIcon(position: Offset, size: Float, color: Color) {}
private fun DrawScope.drawTimestampWatermark(watermarkSettings: WatermarkSettings, canvasSize: Size, textMeasurer: androidx.compose.ui.text.TextMeasurer) {}
private fun DrawScope.drawCameraInfoWatermark(watermarkSettings: WatermarkSettings, canvasSize: Size, textMeasurer: androidx.compose.ui.text.TextMeasurer) {}

/**
 * 预览控制栏
 */
@Composable
fun PreviewControls(
    onResetZoom: () -> Unit,
    onToggleControls: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.7f),
                RoundedCornerShape(8.dp)
            )
            .padding(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        IconButton(
            onClick = onResetZoom,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.CenterFocusWeak,
                contentDescription = "重置缩放",
                tint = Color.White
            )
        }
        
        IconButton(
            onClick = onToggleControls,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.VisibilityOff,
                contentDescription = "隐藏控制",
                tint = Color.White
            )
        }
    }
}

/**
 * 设置信息显示
 */
@Composable
fun SettingsInfo(
    settings: BorderWatermarkSettings,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = "当前设置",
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            if (settings.borderSettings.isEnabled) {
                Text(
                    text = "边框: ${settings.borderSettings.borderType.name}",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White
                )
            }
            
            if (settings.watermarkSettings.isEnabled) {
                Text(
                    text = "水印: ${settings.watermarkSettings.watermarkType.name}",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White
                )
            }
        }
    }
}
