package com.qxyu.yucram.camera

import android.content.Context
import android.graphics.ImageFormat
import android.hardware.camera2.*
import android.media.Image
import android.media.ImageReader
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.util.Size
import android.view.Surface
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * RAW图像捕获管理器
 * 负责RAW格式图像的捕获和基础处理
 */
@Singleton
class RawCaptureManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "RawCaptureManager"
        private const val MAX_IMAGES = 2
    }
    
    private var cameraManager: CameraManager? = null
    private var cameraDevice: CameraDevice? = null
    private var captureSession: CameraCaptureSession? = null
    private var characteristics: CameraCharacteristics? = null
    
    // 图像读取器
    private var rawImageReader: ImageReader? = null
    private var jpegImageReader: ImageReader? = null
    
    // 后台处理线程
    private var backgroundThread: HandlerThread? = null
    private var backgroundHandler: Handler? = null
    
    // RAW支持检测
    private var isRawSupported: Boolean = false
    private var rawSize: Size? = null
    
    /**
     * 初始化RAW捕获系统
     */
    suspend fun initialize(cameraId: String): Result<Unit> {
        return try {
            cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            characteristics = cameraManager!!.getCameraCharacteristics(cameraId)
            
            // 检查RAW支持
            checkRawSupport()
            
            if (!isRawSupported) {
                return Result.failure(Exception("设备不支持RAW格式"))
            }
            
            // 启动后台线程
            startBackgroundThread()
            
            // 设置图像读取器
            setupImageReaders()
            
            Log.d(TAG, "RAW捕获系统初始化成功")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "RAW捕获系统初始化失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查设备RAW支持能力
     */
    private fun checkRawSupport() {
        val capabilities = characteristics?.get(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES)
        isRawSupported = capabilities?.contains(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_RAW) == true
        
        if (isRawSupported) {
            // 获取RAW尺寸
            val streamConfigMap = characteristics?.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
            val rawSizes = streamConfigMap?.getOutputSizes(ImageFormat.RAW_SENSOR)
            rawSize = rawSizes?.maxByOrNull { it.width * it.height }
            
            Log.d(TAG, "设备支持RAW格式，尺寸: ${rawSize?.width}x${rawSize?.height}")
        } else {
            Log.w(TAG, "设备不支持RAW格式")
        }
    }
    
    /**
     * 设置图像读取器
     */
    private fun setupImageReaders() {
        rawSize?.let { size ->
            // RAW图像读取器
            rawImageReader = ImageReader.newInstance(
                size.width, size.height,
                ImageFormat.RAW_SENSOR, MAX_IMAGES
            ).apply {
                setOnImageAvailableListener({ reader ->
                    val image = reader.acquireLatestImage()
                    image?.let { processRawImage(it) }
                }, backgroundHandler)
            }
            
            // JPEG图像读取器（用于预览和对比）
            jpegImageReader = ImageReader.newInstance(
                size.width, size.height,
                ImageFormat.JPEG, MAX_IMAGES
            ).apply {
                setOnImageAvailableListener({ reader ->
                    val image = reader.acquireLatestImage()
                    image?.let { processJpegImage(it) }
                }, backgroundHandler)
            }
        }
    }
    
    /**
     * 捕获RAW图像
     */
    suspend fun captureRawImage(): Result<RawCaptureResult> = suspendCancellableCoroutine { continuation ->
        try {
            if (!isRawSupported) {
                continuation.resumeWithException(Exception("设备不支持RAW格式"))
                return@suspendCancellableCoroutine
            }
            
            val surfaces = mutableListOf<Surface>()
            rawImageReader?.surface?.let { surfaces.add(it) }
            jpegImageReader?.surface?.let { surfaces.add(it) }
            
            // 创建捕获请求
            val captureRequestBuilder = cameraDevice?.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE)
            surfaces.forEach { surface ->
                captureRequestBuilder?.addTarget(surface)
            }
            
            // 设置RAW捕获参数
            captureRequestBuilder?.apply {
                // 禁用自动处理，保持RAW数据的原始性
                set(CaptureRequest.CONTROL_MODE, CaptureRequest.CONTROL_MODE_OFF)
                set(CaptureRequest.NOISE_REDUCTION_MODE, CaptureRequest.NOISE_REDUCTION_MODE_OFF)
                set(CaptureRequest.EDGE_MODE, CaptureRequest.EDGE_MODE_OFF)
                set(CaptureRequest.COLOR_CORRECTION_MODE, CaptureRequest.COLOR_CORRECTION_MODE_TRANSFORM_MATRIX)
            }
            
            val captureRequest = captureRequestBuilder?.build()
            
            // 执行捕获
            captureSession?.capture(captureRequest!!, object : CameraCaptureSession.CaptureCallback() {
                override fun onCaptureCompleted(
                    session: CameraCaptureSession,
                    request: CaptureRequest,
                    result: TotalCaptureResult
                ) {
                    Log.d(TAG, "RAW图像捕获完成")
                    // 捕获结果将在ImageReader回调中处理
                }
                
                override fun onCaptureFailed(
                    session: CameraCaptureSession,
                    request: CaptureRequest,
                    failure: CaptureFailure
                ) {
                    continuation.resumeWithException(Exception("RAW捕获失败: ${failure.reason}"))
                }
            }, backgroundHandler)
            
        } catch (e: Exception) {
            continuation.resumeWithException(e)
        }
    }
    
    /**
     * 处理RAW图像
     */
    private fun processRawImage(image: Image) {
        try {
            Log.d(TAG, "开始处理RAW图像，格式: ${image.format}, 尺寸: ${image.width}x${image.height}")
            
            // 获取RAW数据
            val buffer = image.planes[0].buffer
            val rawData = ByteArray(buffer.remaining())
            buffer.get(rawData)
            
            // 保存RAW数据到临时文件（用于后续处理）
            val rawFile = File(context.cacheDir, "temp_raw_${System.currentTimeMillis()}.raw")
            FileOutputStream(rawFile).use { it.write(rawData) }
            
            Log.d(TAG, "RAW图像处理完成，数据大小: ${rawData.size} bytes")
            
        } catch (e: Exception) {
            Log.e(TAG, "处理RAW图像失败", e)
        } finally {
            image.close()
        }
    }
    
    /**
     * 处理JPEG图像
     */
    private fun processJpegImage(image: Image) {
        try {
            Log.d(TAG, "处理JPEG图像用于对比")
            
            val buffer = image.planes[0].buffer
            val jpegData = ByteArray(buffer.remaining())
            buffer.get(jpegData)
            
            // 保存JPEG用于对比
            val jpegFile = File(context.cacheDir, "temp_jpeg_${System.currentTimeMillis()}.jpg")
            FileOutputStream(jpegFile).use { it.write(jpegData) }
            
        } catch (e: Exception) {
            Log.e(TAG, "处理JPEG图像失败", e)
        } finally {
            image.close()
        }
    }
    
    /**
     * 创建DNG文件
     */
    fun createDngFile(rawImage: Image, captureResult: CaptureResult): ByteArray {
        val dngCreator = DngCreator(characteristics!!, captureResult)
        val outputStream = ByteArrayOutputStream()
        
        try {
            dngCreator.writeImage(outputStream, rawImage)
            Log.d(TAG, "DNG文件创建成功，大小: ${outputStream.size()} bytes")
            return outputStream.toByteArray()
        } catch (e: Exception) {
            Log.e(TAG, "创建DNG文件失败", e)
            throw e
        } finally {
            dngCreator.close()
            outputStream.close()
        }
    }
    
    /**
     * 获取RAW支持状态
     */
    fun isRawSupported(): Boolean = isRawSupported
    
    /**
     * 获取RAW图像尺寸
     */
    fun getRawSize(): Size? = rawSize
    
    /**
     * 启动后台线程
     */
    private fun startBackgroundThread() {
        backgroundThread = HandlerThread("RawCaptureBackground").apply {
            start()
            backgroundHandler = Handler(looper)
        }
    }
    
    /**
     * 停止后台线程
     */
    private fun stopBackgroundThread() {
        backgroundThread?.quitSafely()
        try {
            backgroundThread?.join()
            backgroundThread = null
            backgroundHandler = null
        } catch (e: InterruptedException) {
            Log.e(TAG, "停止后台线程时发生错误", e)
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        rawImageReader?.close()
        jpegImageReader?.close()
        captureSession?.close()
        cameraDevice?.close()
        stopBackgroundThread()
        
        Log.d(TAG, "RAW捕获系统资源清理完成")
    }
}

/**
 * RAW捕获结果数据类
 */
data class RawCaptureResult(
    val rawData: ByteArray,
    val dngData: ByteArray,
    val jpegData: ByteArray,
    val captureResult: CaptureResult,
    val timestamp: Long = System.currentTimeMillis()
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as RawCaptureResult
        
        if (!rawData.contentEquals(other.rawData)) return false
        if (!dngData.contentEquals(other.dngData)) return false
        if (!jpegData.contentEquals(other.jpegData)) return false
        if (timestamp != other.timestamp) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = rawData.contentHashCode()
        result = 31 * result + dngData.contentHashCode()
        result = 31 * result + jpegData.contentHashCode()
        result = 31 * result + timestamp.hashCode()
        return result
    }
}
