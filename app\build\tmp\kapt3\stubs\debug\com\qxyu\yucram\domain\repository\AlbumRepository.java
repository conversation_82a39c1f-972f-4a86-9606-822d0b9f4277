package com.qxyu.yucram.domain.repository;

/**
 * 相册仓库接口
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0005\bf\u0018\u00002\u00020\u0001J,\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\b\u0010\tJ2\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\r\u0010\u000eJ$\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0011\u001a\u00020\u0012H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0013\u0010\u0014J2\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0016\u001a\u00020\u00102\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0019\u0010\u001aJ$\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u001dJ\u0018\u0010\u001e\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001dJ\u000e\u0010\u001f\u001a\u00020 H\u00a6@\u00a2\u0006\u0002\u0010!J\u0016\u0010\"\u001a\u00020#2\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001dJ\u001c\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\f0%2\u0006\u0010&\u001a\u00020\u0006H&J\u001c\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00100\f2\u0006\u0010\u0007\u001a\u00020\u0006H\u00a6@\u00a2\u0006\u0002\u0010\u001dJ\u0014\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\f0%H&J\u001c\u0010)\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020*0\f0%2\u0006\u0010\u0005\u001a\u00020\u0006H&J\u0014\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\f0%H&J\u0014\u0010,\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\f0%H&J\u001c\u0010-\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b.\u0010!J$\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b0\u0010\u001dJ,\u00101\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u0010\tJ2\u00103\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b4\u0010\u000eJ\u001c\u00105\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\f0%2\u0006\u00106\u001a\u00020\u0006H&J,\u00107\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u000208H\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b9\u0010:J2\u0010;\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\fH\u00a6@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b<\u0010\u000e\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006="}, d2 = {"Lcom/qxyu/yucram/domain/repository/AlbumRepository;", "", "addPhotoToAlbum", "Lkotlin/Result;", "", "albumId", "", "photoId", "addPhotoToAlbum-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addPhotosToAlbum", "photoIds", "", "addPhotosToAlbum-0E7RQCE", "(Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createAlbum", "Lcom/qxyu/yucram/domain/model/Album;", "request", "Lcom/qxyu/yucram/domain/model/CreateAlbumRequest;", "createAlbum-gIAlu-s", "(Lcom/qxyu/yucram/domain/model/CreateAlbumRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSmartAlbum", "album", "rules", "Lcom/qxyu/yucram/domain/model/SmartAlbumRule;", "createSmartAlbum-0E7RQCE", "(Lcom/qxyu/yucram/domain/model/Album;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAlbum", "deleteAlbum-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAlbumById", "getAlbumCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAlbumStats", "Lcom/qxyu/yucram/domain/model/AlbumStats;", "getAlbumsByTag", "Lkotlinx/coroutines/flow/Flow;", "tag", "getAlbumsForPhoto", "getAllAlbums", "getPhotosInAlbum", "Lcom/qxyu/yucram/domain/model/Photo;", "getSystemAlbums", "getUserAlbums", "initializeSystemAlbums", "initializeSystemAlbums-IoAF18A", "refreshSmartAlbum", "refreshSmartAlbum-gIAlu-s", "removePhotoFromAlbum", "removePhotoFromAlbum-0E7RQCE", "removePhotosFromAlbum", "removePhotosFromAlbum-0E7RQCE", "searchAlbums", "query", "updateAlbum", "Lcom/qxyu/yucram/domain/model/UpdateAlbumRequest;", "updateAlbum-0E7RQCE", "(Ljava/lang/String;Lcom/qxyu/yucram/domain/model/UpdateAlbumRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSmartAlbumRules", "updateSmartAlbumRules-0E7RQCE", "app_debug"})
public abstract interface AlbumRepository {
    
    /**
     * 获取所有相册
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getAllAlbums();
    
    /**
     * 根据ID获取相册
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlbumById(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.Album> $completion);
    
    /**
     * 获取用户创建的相册
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getUserAlbums();
    
    /**
     * 获取系统相册
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getSystemAlbums();
    
    /**
     * 获取相册中的照片
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Photo>> getPhotosInAlbum(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId);
    
    /**
     * 获取照片所属的相册
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlbumsForPhoto(@org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.qxyu.yucram.domain.model.Album>> $completion);
    
    /**
     * 搜索相册
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> searchAlbums(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    /**
     * 根据标签获取相册
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.domain.model.Album>> getAlbumsByTag(@org.jetbrains.annotations.NotNull()
    java.lang.String tag);
    
    /**
     * 获取相册统计信息
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlbumStats(@org.jetbrains.annotations.NotNull()
    java.lang.String albumId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.domain.model.AlbumStats> $completion);
    
    /**
     * 获取相册数量
     */
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAlbumCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
}