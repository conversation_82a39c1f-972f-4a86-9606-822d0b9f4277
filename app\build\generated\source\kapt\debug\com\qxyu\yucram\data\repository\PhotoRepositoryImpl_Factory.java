// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.data.repository;

import com.qxyu.yucram.data.local.dao.PhotoDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PhotoRepositoryImpl_Factory implements Factory<PhotoRepositoryImpl> {
  private final Provider<PhotoDao> photoDaoProvider;

  public PhotoRepositoryImpl_Factory(Provider<PhotoDao> photoDaoProvider) {
    this.photoDaoProvider = photoDaoProvider;
  }

  @Override
  public PhotoRepositoryImpl get() {
    return newInstance(photoDaoProvider.get());
  }

  public static PhotoRepositoryImpl_Factory create(Provider<PhotoDao> photoDaoProvider) {
    return new PhotoRepositoryImpl_Factory(photoDaoProvider);
  }

  public static PhotoRepositoryImpl newInstance(PhotoDao photoDao) {
    return new PhotoRepositoryImpl(photoDao);
  }
}
