package com.qxyu.yucram.domain.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/qxyu/yucram/domain/model/AspectRatio;", "", "ratio", "", "value", "", "(Ljava/lang/String;ILjava/lang/String;F)V", "getRatio", "()Ljava/lang/String;", "getValue", "()F", "RATIO_1_1", "RATIO_4_3", "RATIO_3_2", "RATIO_16_9", "app_debug"})
public enum AspectRatio {
    /*public static final*/ RATIO_1_1 /* = new RATIO_1_1(null, 0.0F) */,
    /*public static final*/ RATIO_4_3 /* = new RATIO_4_3(null, 0.0F) */,
    /*public static final*/ RATIO_3_2 /* = new RATIO_3_2(null, 0.0F) */,
    /*public static final*/ RATIO_16_9 /* = new RATIO_16_9(null, 0.0F) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ratio = null;
    private final float value = 0.0F;
    
    AspectRatio(java.lang.String ratio, float value) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRatio() {
        return null;
    }
    
    public final float getValue() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.AspectRatio> getEntries() {
        return null;
    }
}