// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.data.repository;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AlbumRepositoryImpl_Factory implements Factory<AlbumRepositoryImpl> {
  @Override
  public AlbumRepositoryImpl get() {
    return newInstance();
  }

  public static AlbumRepositoryImpl_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static AlbumRepositoryImpl newInstance() {
    return new AlbumRepositoryImpl();
  }

  private static final class InstanceHolder {
    private static final AlbumRepositoryImpl_Factory INSTANCE = new AlbumRepositoryImpl_Factory();
  }
}
