package com.qxyu.yucram.camera;

/**
 * 相机状态枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/camera/CameraState;", "", "(Ljava/lang/String;I)V", "CLOSED", "INITIALIZING", "OPENING", "OPENED", "PREVIEW", "CAPTURING", "DISCONNECTED", "ERROR", "app_debug"})
public enum CameraState {
    /*public static final*/ CLOSED /* = new CLOSED() */,
    /*public static final*/ INITIALIZING /* = new INITIALIZING() */,
    /*public static final*/ OPENING /* = new OPENING() */,
    /*public static final*/ OPENED /* = new OPENED() */,
    /*public static final*/ PREVIEW /* = new PREVIEW() */,
    /*public static final*/ CAPTURING /* = new CAPTURING() */,
    /*public static final*/ DISCONNECTED /* = new DISCONNECTED() */,
    /*public static final*/ ERROR /* = new ERROR() */;
    
    CameraState() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.camera.CameraState> getEntries() {
        return null;
    }
}