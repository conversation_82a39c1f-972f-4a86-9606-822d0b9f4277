package com.qxyu.yucram.presentation.lutfilter.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000L\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001aF\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a\"\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a6\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\u0010\u0010\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0011\u001a\u00020\u00072\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a,\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\u0010\u0004\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a6\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\u00132\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00010\u00172\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00010\u00172\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a.\u0010\u0019\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\u00010\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u0007\u001a.\u0010\u001a\u001a\u00020\u0001*\u00020\u001b2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\u001c\u001a\u00020\u001dH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001e\u0010\u001f\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006 "}, d2 = {"BeforeAfterPreview", "", "photo", "Lcom/qxyu/yucram/domain/model/Photo;", "filter", "Lcom/qxyu/yucram/domain/model/LUTFilter;", "settings", "Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "splitPosition", "", "onSplitPositionChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "FilterInfo", "LUTFilterPreview", "selectedFilter", "filterSettings", "showBeforeAfter", "", "NormalPreview", "PreviewControls", "onResetZoom", "Lkotlin/Function0;", "onToggleControls", "SplitLineControl", "drawLUTFilterEffect", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "canvasSize", "Landroidx/compose/ui/geometry/Size;", "drawLUTFilterEffect-Ug5Nnss", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Lcom/qxyu/yucram/domain/model/LUTFilter;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;J)V", "app_debug"})
public final class LUTFilterPreviewKt {
    
    /**
     * LUT滤镜预览组件
     */
    @androidx.compose.runtime.Composable()
    public static final void LUTFilterPreview(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Photo photo, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter selectedFilter, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings filterSettings, boolean showBeforeAfter, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 普通预览模式
     */
    @androidx.compose.runtime.Composable()
    public static final void NormalPreview(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Photo photo, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.LUTFilter filter, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings settings, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 前后对比预览模式
     */
    @androidx.compose.runtime.Composable()
    public static final void BeforeAfterPreview(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Photo photo, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings settings, float splitPosition, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onSplitPositionChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 预览控制栏
     */
    @androidx.compose.runtime.Composable()
    public static final void PreviewControls(boolean showBeforeAfter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResetZoom, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onToggleControls, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 滤镜信息显示
     */
    @androidx.compose.runtime.Composable()
    public static final void FilterInfo(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilter filter, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings settings, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 分割线控制
     */
    @androidx.compose.runtime.Composable()
    public static final void SplitLineControl(float splitPosition, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onSplitPositionChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
}