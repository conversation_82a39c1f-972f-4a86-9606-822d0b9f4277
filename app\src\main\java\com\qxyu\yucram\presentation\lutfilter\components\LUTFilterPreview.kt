package com.qxyu.yucram.presentation.lutfilter.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*
import kotlin.math.*

/**
 * LUT滤镜预览组件
 */
@Composable
fun LUTFilterPreview(
    photo: Photo,
    selectedFilter: LUTFilter?,
    filterSettings: LUTFilterSettings,
    showBeforeAfter: Boolean = false,
    modifier: Modifier = Modifier
) {
    var scale by remember { mutableStateOf(1f) }
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }
    var showControls by remember { mutableStateOf(true) }
    var splitPosition by remember { mutableStateOf(0.5f) }
    
    val density = LocalDensity.current
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        // 主要预览区域
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(Color.Black)
        ) {
            if (showBeforeAfter && selectedFilter != null) {
                // 前后对比模式
                BeforeAfterPreview(
                    photo = photo,
                    filter = selectedFilter,
                    settings = filterSettings,
                    splitPosition = splitPosition,
                    onSplitPositionChanged = { splitPosition = it },
                    modifier = Modifier
                        .fillMaxSize()
                        .graphicsLayer(
                            scaleX = scale,
                            scaleY = scale,
                            translationX = offsetX,
                            translationY = offsetY
                        )
                        .pointerInput(Unit) {
                            detectTransformGestures(
                                onGesture = { _, pan, zoom, _ ->
                                    scale = (scale * zoom).coerceIn(0.5f, 3f)
                                    if (scale > 1f) {
                                        offsetX += pan.x
                                        offsetY += pan.y
                                    } else {
                                        offsetX = 0f
                                        offsetY = 0f
                                    }
                                }
                            )
                        }
                )
            } else {
                // 普通预览模式
                NormalPreview(
                    photo = photo,
                    filter = selectedFilter,
                    settings = filterSettings,
                    modifier = Modifier
                        .fillMaxSize()
                        .graphicsLayer(
                            scaleX = scale,
                            scaleY = scale,
                            translationX = offsetX,
                            translationY = offsetY
                        )
                        .pointerInput(Unit) {
                            detectTransformGestures(
                                onGesture = { _, pan, zoom, _ ->
                                    scale = (scale * zoom).coerceIn(0.5f, 3f)
                                    if (scale > 1f) {
                                        offsetX += pan.x
                                        offsetY += pan.y
                                    } else {
                                        offsetX = 0f
                                        offsetY = 0f
                                    }
                                }
                            )
                        }
                )
            }
        }
        
        // 预览控制栏
        if (showControls) {
            PreviewControls(
                showBeforeAfter = showBeforeAfter,
                onResetZoom = {
                    scale = 1f
                    offsetX = 0f
                    offsetY = 0f
                },
                onToggleControls = { showControls = !showControls },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(24.dp)
            )
        }
        
        // 滤镜信息显示
        if (showControls && selectedFilter != null) {
            FilterInfo(
                filter = selectedFilter,
                settings = filterSettings,
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(24.dp)
            )
        }
        
        // 前后对比分割线控制
        if (showBeforeAfter && showControls) {
            SplitLineControl(
                splitPosition = splitPosition,
                onSplitPositionChanged = { splitPosition = it },
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(24.dp)
            )
        }
        
        // 点击切换控制显示
        Box(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTransformGestures { _, _, _, _ ->
                        showControls = !showControls
                    }
                }
        )
    }
}

/**
 * 普通预览模式
 */
@Composable
fun NormalPreview(
    photo: Photo,
    filter: LUTFilter?,
    settings: LUTFilterSettings,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        // 原始照片
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(photo.filePath)
                .crossfade(true)
                .build(),
            contentDescription = photo.fileName,
            contentScale = ContentScale.Fit,
            modifier = Modifier.fillMaxSize()
        )
        
        // LUT滤镜效果覆盖层
        if (filter != null && settings.previewEnabled) {
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                drawLUTFilterEffect(
                    filter = filter,
                    settings = settings,
                    canvasSize = size
                )
            }
        }
    }
}

/**
 * 前后对比预览模式
 */
@Composable
fun BeforeAfterPreview(
    photo: Photo,
    filter: LUTFilter,
    settings: LUTFilterSettings,
    splitPosition: Float,
    onSplitPositionChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        // 原始照片（左侧）
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(photo.filePath)
                .crossfade(true)
                .build(),
            contentDescription = "${photo.fileName} - 原图",
            contentScale = ContentScale.Fit,
            modifier = Modifier.fillMaxSize()
        )
        
        // 滤镜效果照片（右侧）
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(
                    RectangleShape // TODO: 实现自定义裁剪形状
                )
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(photo.filePath)
                    .crossfade(true)
                    .build(),
                contentDescription = "${photo.fileName} - 滤镜效果",
                contentScale = ContentScale.Fit,
                modifier = Modifier.fillMaxSize()
            )
            
            // LUT滤镜效果覆盖层
            Canvas(
                modifier = Modifier.fillMaxSize()
            ) {
                drawLUTFilterEffect(
                    filter = filter,
                    settings = settings,
                    canvasSize = size
                )
            }
        }
        
        // 分割线
        Canvas(
            modifier = Modifier.fillMaxSize()
        ) {
            val splitX = size.width * splitPosition
            
            // 绘制分割线
            drawLine(
                color = Color.White,
                start = Offset(splitX, 0f),
                end = Offset(splitX, size.height),
                strokeWidth = 4.dp.toPx()
            )
            
            // 绘制分割线控制点
            drawCircle(
                color = Color.White,
                radius = 12.dp.toPx(),
                center = Offset(splitX, size.height / 2)
            )
            
            drawCircle(
                color = Color.Black,
                radius = 8.dp.toPx(),
                center = Offset(splitX, size.height / 2)
            )
        }
        
        // 标签
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                )
            ) {
                Text(
                    text = "原图",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White,
                    modifier = Modifier.padding(8.dp)
                )
            }
            
            Card(
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                )
            ) {
                Text(
                    text = filter.displayName,
                    style = MaterialTheme.typography.bodySmall,
                    color = Color.White,
                    modifier = Modifier.padding(8.dp)
                )
            }
        }
    }
}

/**
 * 绘制LUT滤镜效果
 */
private fun DrawScope.drawLUTFilterEffect(
    filter: LUTFilter,
    settings: LUTFilterSettings,
    canvasSize: Size
) {
    // TODO: 实现实际的LUT滤镜绘制
    // 这里应该调用LUT处理引擎应用滤镜效果
    
    // 临时实现：绘制一个半透明的颜色覆盖层来模拟滤镜效果
    val overlayColor = when (filter.category) {
        LUTCategory.WARM -> Color.Yellow.copy(alpha = settings.intensity * 0.2f)
        LUTCategory.COOL -> Color.Blue.copy(alpha = settings.intensity * 0.2f)
        LUTCategory.VINTAGE -> Color(0xFFD2691E).copy(alpha = settings.intensity * 0.3f)
        LUTCategory.CINEMATIC -> Color(0xFF1E90FF).copy(alpha = settings.intensity * 0.15f)
        LUTCategory.MONOCHROME -> Color.Gray.copy(alpha = settings.intensity * 0.4f)
        else -> Color.Transparent
    }
    
    if (overlayColor != Color.Transparent) {
        drawRect(
            color = overlayColor,
            size = canvasSize
        )
    }
}

/**
 * 预览控制栏
 */
@Composable
fun PreviewControls(
    showBeforeAfter: Boolean,
    onResetZoom: () -> Unit,
    onToggleControls: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .background(
                Color.Black.copy(alpha = 0.7f),
                RoundedCornerShape(8.dp)
            )
            .padding(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        IconButton(
            onClick = onResetZoom,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.CenterFocusWeak,
                contentDescription = "重置缩放",
                tint = Color.White
            )
        }
        
        IconButton(
            onClick = onToggleControls,
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.VisibilityOff,
                contentDescription = "隐藏控制",
                tint = Color.White
            )
        }
        
        if (showBeforeAfter) {
            Icon(
                imageVector = Icons.Filled.Compare,
                contentDescription = "对比模式",
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

/**
 * 滤镜信息显示
 */
@Composable
fun FilterInfo(
    filter: LUTFilter,
    settings: LUTFilterSettings,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = filter.displayName,
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                color = Color.White
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = filter.category.displayName,
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.7f)
            )
            
            if (settings.intensity != 1.0f) {
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = "强度: ${(settings.intensity * 100).toInt()}%",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

/**
 * 分割线控制
 */
@Composable
fun SplitLineControl(
    splitPosition: Float,
    onSplitPositionChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.7f)
        ),
        shape = RoundedCornerShape(8.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "对比位置",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Slider(
                value = splitPosition,
                onValueChange = onSplitPositionChanged,
                valueRange = 0f..1f,
                colors = SliderDefaults.colors(
                    thumbColor = MaterialTheme.colorScheme.primary,
                    activeTrackColor = MaterialTheme.colorScheme.primary,
                    inactiveTrackColor = Color.White.copy(alpha = 0.3f)
                ),
                modifier = Modifier.width(200.dp)
            )
            
            Text(
                text = "${(splitPosition * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                color = Color.White.copy(alpha = 0.7f)
            )
        }
    }
}
