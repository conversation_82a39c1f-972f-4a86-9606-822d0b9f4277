package com.qxyu.yucram.presentation.animation;

/**
 * 动画类型枚举
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/qxyu/yucram/presentation/animation/AnimationType;", "", "(<PERSON>ja<PERSON>/lang/String;I)V", "SLIDE", "FADE", "SCALE", "SLIDE_AND_FADE", "SCALE_AND_FADE", "SPRING", "app_debug"})
public enum AnimationType {
    /*public static final*/ SLIDE /* = new SLIDE() */,
    /*public static final*/ FADE /* = new FADE() */,
    /*public static final*/ SCALE /* = new SCALE() */,
    /*public static final*/ SLIDE_AND_FADE /* = new SLIDE_AND_FADE() */,
    /*public static final*/ SCALE_AND_FADE /* = new SCALE_AND_FADE() */,
    /*public static final*/ SPRING /* = new SPRING() */;
    
    AnimationType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.presentation.animation.AnimationType> getEntries() {
        return null;
    }
}