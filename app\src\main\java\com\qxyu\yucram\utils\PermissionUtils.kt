package com.qxyu.yucram.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat

/**
 * 权限管理工具类
 * 支持Android 14+的权限管理
 */
object PermissionUtils {
    
    /**
     * 相机权限
     */
    const val CAMERA_PERMISSION = Manifest.permission.CAMERA
    
    /**
     * 录音权限（用于视频录制）
     */
    const val RECORD_AUDIO_PERMISSION = Manifest.permission.RECORD_AUDIO
    
    /**
     * 位置权限（用于GPS水印）
     */
    const val LOCATION_FINE_PERMISSION = Manifest.permission.ACCESS_FINE_LOCATION
    const val LOCATION_COARSE_PERMISSION = Manifest.permission.ACCESS_COARSE_LOCATION
    
    /**
     * 存储权限（Android 14+）
     */
    val STORAGE_PERMISSIONS = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
        arrayOf(
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_VIDEO
        )
    } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        arrayOf(
            Manifest.permission.READ_MEDIA_IMAGES,
            Manifest.permission.READ_MEDIA_VIDEO
        )
    } else {
        arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )
    }
    
    /**
     * 震动权限
     */
    const val VIBRATE_PERMISSION = Manifest.permission.VIBRATE
    
    /**
     * 检查单个权限是否已授予
     */
    fun isPermissionGranted(context: Context, permission: String): Boolean {
        return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * 检查多个权限是否已授予
     */
    fun arePermissionsGranted(context: Context, permissions: Array<String>): Boolean {
        return permissions.all { isPermissionGranted(context, it) }
    }
    
    /**
     * 检查相机权限
     */
    fun isCameraPermissionGranted(context: Context): Boolean {
        return isPermissionGranted(context, CAMERA_PERMISSION)
    }
    
    /**
     * 检查存储权限
     */
    fun isStoragePermissionGranted(context: Context): Boolean {
        return arePermissionsGranted(context, STORAGE_PERMISSIONS)
    }
    
    /**
     * 检查位置权限
     */
    fun isLocationPermissionGranted(context: Context): Boolean {
        return isPermissionGranted(context, LOCATION_FINE_PERMISSION) ||
                isPermissionGranted(context, LOCATION_COARSE_PERMISSION)
    }
    
    /**
     * 检查录音权限
     */
    fun isRecordAudioPermissionGranted(context: Context): Boolean {
        return isPermissionGranted(context, RECORD_AUDIO_PERMISSION)
    }
    
    /**
     * 获取所有必需的权限
     */
    fun getRequiredPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        permissions.add(CAMERA_PERMISSION)
        permissions.add(RECORD_AUDIO_PERMISSION)
        permissions.addAll(STORAGE_PERMISSIONS)
        permissions.add(LOCATION_FINE_PERMISSION)
        permissions.add(LOCATION_COARSE_PERMISSION)
        permissions.add(VIBRATE_PERMISSION)
        return permissions.toTypedArray()
    }
    
    /**
     * 获取相机相关的核心权限
     */
    fun getCameraPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        permissions.add(CAMERA_PERMISSION)
        permissions.addAll(STORAGE_PERMISSIONS)
        return permissions.toTypedArray()
    }
}
