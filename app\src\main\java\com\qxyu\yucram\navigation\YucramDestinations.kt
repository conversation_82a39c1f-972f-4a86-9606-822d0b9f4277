package com.qxyu.yucram.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CameraAlt
import androidx.compose.material.icons.filled.PhotoLibrary
import androidx.compose.material.icons.filled.Settings

/**
 * Yucram应用导航目的地定义
 */
object YucramDestinations {
    
    // 主要页面
    const val CAMERA_ROUTE = "camera"
    const val GALLERY_ROUTE = "gallery"
    const val SETTINGS_ROUTE = "settings"
    
    // 权限页面
    const val PERMISSION_ROUTE = "permission"
    
    // 相机相关页面
    const val CAMERA_SETTINGS_ROUTE = "camera_settings"
    const val FILTER_SELECTION_ROUTE = "filter_selection"
    
    // 图库相关页面
    const val PHOTO_DETAIL_ROUTE = "photo_detail"
    const val PHOTO_EDIT_ROUTE = "photo_edit"
    const val ALBUM_ROUTE = "album"
    
    // 设置相关页面
    const val THEME_SETTINGS_ROUTE = "theme_settings"
    const val ABOUT_ROUTE = "about"
    
    // 带参数的路由
    const val PHOTO_DETAIL_WITH_ID = "photo_detail/{photoId}"
    const val PHOTO_EDIT_WITH_ID = "photo_edit/{photoId}"
    const val ALBUM_WITH_ID = "album/{albumId}"
    
    // 参数键
    const val PHOTO_ID_KEY = "photoId"
    const val ALBUM_ID_KEY = "albumId"
}

/**
 * 导航路由数据类
 */
data class YucramRoute(
    val route: String,
    val title: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    val isMainDestination: Boolean = false
)

/**
 * 主要导航目的地
 */
enum class MainDestination(
    val route: String,
    val title: String,
    val icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    CAMERA(
        route = YucramDestinations.CAMERA_ROUTE,
        title = "相机",
        icon = Icons.Filled.CameraAlt
    ),
    GALLERY(
        route = YucramDestinations.GALLERY_ROUTE,
        title = "图库",
        icon = Icons.Filled.PhotoLibrary
    ),
    SETTINGS(
        route = YucramDestinations.SETTINGS_ROUTE,
        title = "设置",
        icon = Icons.Filled.Settings
    )
}
