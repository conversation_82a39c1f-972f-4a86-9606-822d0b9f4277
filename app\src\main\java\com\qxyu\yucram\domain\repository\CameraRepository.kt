package com.qxyu.yucram.domain.repository

import com.qxyu.yucram.domain.model.CameraSettings
import com.qxyu.yucram.domain.model.Photo
import kotlinx.coroutines.flow.Flow

/**
 * 相机Repository接口
 */
interface CameraRepository {
    
    /**
     * 获取相机设置
     */
    fun getCameraSettings(): Flow<CameraSettings>
    
    /**
     * 更新相机设置
     */
    suspend fun updateCameraSettings(settings: CameraSettings)
    
    /**
     * 拍摄照片
     */
    suspend fun capturePhoto(settings: CameraSettings): Result<Photo>
    
    /**
     * 检查相机权限
     */
    suspend fun checkCameraPermission(): Boolean
    
    /**
     * 检查存储权限
     */
    suspend fun checkStoragePermission(): Boolean
    
    /**
     * 获取可用的相机列表
     */
    suspend fun getAvailableCameras(): List<String>
    
    /**
     * 切换相机
     */
    suspend fun switchCamera(cameraId: String): Result<Unit>
}
