package com.qxyu.yucram.presentation.borderwater.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000T\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\b\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a8\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\r\u0010\u000e\u001a.\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a$\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a.\u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a$\u0010\u0012\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a$\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001a$\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0007\u001aZ\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0018\u001a\u00020\u00192\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00190\u001b2\u0012\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00010\u00052\u0014\b\u0002\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0019\u0012\u0004\u0012\u00020\u00170\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a0\u0010\u001e\u001a\u00020\u00012\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"2\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010$2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010%\u001a\u00020\u00012\u0006\u0010&\u001a\u00020 2\u0012\u0010\'\u001a\u000e\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010(\u001a\u00020\u00012\u0006\u0010)\u001a\u00020\"2\u0012\u0010*\u001a\u000e\u0012\u0004\u0012\u00020\"\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a0\u0010+\u001a\u00020\u00012\u0006\u0010,\u001a\u00020-2\u0006\u0010!\u001a\u00020\"2\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00010$2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010.\u001a\u00020\u00012\u0006\u0010/\u001a\u00020-2\u0012\u00100\u001a\u000e\u0012\u0004\u0012\u00020-\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00061"}, d2 = {"BorderAdvancedSettings", "", "borderSettings", "Lcom/qxyu/yucram/domain/model/BorderSettings;", "onSettingsChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "BorderBasicSettings", "BorderColorPicker", "selectedColor", "Landroidx/compose/ui/graphics/Color;", "onColorSelected", "BorderColorPicker-ek8zF_U", "(JLkotlin/jvm/functions/Function1;Landroidx/compose/ui/Modifier;)V", "BorderColorSettings", "BorderDecorativeSettings", "BorderEditPanel", "BorderGradientSettings", "BorderPatternSettings", "BorderShadowSettings", "BorderSliderSetting", "label", "", "value", "", "valueRange", "Lkotlin/ranges/ClosedFloatingPointRange;", "onValueChanged", "formatValue", "BorderStyleChip", "style", "Lcom/qxyu/yucram/domain/model/BorderStyle;", "isSelected", "", "onClick", "Lkotlin/Function0;", "BorderStyleSection", "selectedStyle", "onStyleSelected", "BorderToggleSection", "isEnabled", "onToggle", "BorderTypeItem", "type", "Lcom/qxyu/yucram/domain/model/BorderType;", "BorderTypeSection", "selectedType", "onTypeSelected", "app_debug"})
public final class BorderEditPanelKt {
    
    /**
     * 边框编辑面板
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderEditPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框开关区域
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderToggleSection(boolean isEnabled, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onToggle, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框类型选择
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderTypeSection(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderType selectedType, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderType, kotlin.Unit> onTypeSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框类型项
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderTypeItem(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderType type, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框样式选择
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderStyleSection(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderStyle selectedStyle, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderStyle, kotlin.Unit> onStyleSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框样式芯片
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void BorderStyleChip(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderStyle style, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框基础设置
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderBasicSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框颜色设置
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderColorSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框高级设置
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderAdvancedSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 边框滑块设置
     */
    @androidx.compose.runtime.Composable()
    public static final void BorderSliderSetting(@org.jetbrains.annotations.NotNull()
    java.lang.String label, float value, @org.jetbrains.annotations.NotNull()
    kotlin.ranges.ClosedFloatingPointRange<java.lang.Float> valueRange, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onValueChanged, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Float, java.lang.String> formatValue, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BorderGradientSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BorderPatternSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BorderDecorativeSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void BorderShadowSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.BorderSettings, kotlin.Unit> onSettingsChanged) {
    }
}