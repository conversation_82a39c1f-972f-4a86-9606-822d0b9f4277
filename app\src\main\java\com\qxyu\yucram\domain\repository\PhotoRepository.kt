package com.qxyu.yucram.domain.repository

import androidx.paging.PagingData
import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * 照片仓库接口
 */
interface PhotoRepository {

    // ========== 基础操作 ==========

    /**
     * 获取所有照片（分页）
     */
    fun getAllPhotosPaged(): Flow<PagingData<Photo>>

    /**
     * 获取所有照片
     */
    fun getAllPhotos(): Flow<List<Photo>>

    /**
     * 根据ID获取照片
     */
    suspend fun getPhotoById(photoId: String): Photo?

    /**
     * 根据ID列表获取照片
     */
    suspend fun getPhotosByIds(photoIds: List<String>): List<Photo>

    /**
     * 保存照片
     */
    suspend fun savePhoto(photo: Photo): Result<Photo>

    /**
     * 批量保存照片
     */
    suspend fun savePhotos(photos: List<Photo>): Result<List<Photo>>

    /**
     * 更新照片
     */
    suspend fun updatePhoto(photo: Photo): Result<Photo>

    /**
     * 删除照片
     */
    suspend fun deletePhoto(photoId: String): Result<Unit>

    /**
     * 批量删除照片
     */
    suspend fun deletePhotos(photoIds: List<String>): Result<Unit>

    // ========== 收藏操作 ==========

    /**
     * 设置收藏状态
     */
    suspend fun setFavorite(photoId: String, isFavorite: Boolean): Result<Unit>

    /**
     * 获取收藏照片
     */
    fun getFavoritePhotos(): Flow<List<Photo>>

    // ========== 筛选和搜索 ==========

    /**
     * 根据筛选条件获取照片
     */
    fun getPhotosWithFilter(filter: PhotoFilter): Flow<List<Photo>>

    /**
     * 搜索照片
     */
    fun searchPhotos(query: String): Flow<List<Photo>>

    /**
     * 根据日期范围获取照片
     */
    fun getPhotosByDateRange(startDate: java.time.LocalDateTime, endDate: java.time.LocalDateTime): Flow<List<Photo>>

    /**
     * 获取Yucram拍摄的照片
     */
    fun getYucramPhotos(): Flow<List<Photo>>

    /**
     * 根据滤镜获取照片
     */
    fun getPhotosByFilter(filterName: String): Flow<List<Photo>>

    /**
     * 根据标签获取照片
     */
    fun getPhotosByTag(tag: String): Flow<List<Photo>>

    // ========== 统计信息 ==========

    /**
     * 获取照片统计信息
     */
    suspend fun getPhotoStats(): PhotoStats

    /**
     * 获取照片数量
     */
    suspend fun getPhotoCount(): Int

    /**
     * 获取最近照片
     */
    suspend fun getRecentPhotos(limit: Int = 20): List<Photo>

    // ========== 文件操作 ==========

    /**
     * 扫描设备照片
     */
    suspend fun scanDevicePhotos(): Result<List<Photo>>

    /**
     * 检查照片文件是否存在
     */
    suspend fun isPhotoFileExists(photoId: String): Boolean

    /**
     * 生成缩略图
     */
    suspend fun generateThumbnail(photoId: String): Result<String>

    /**
     * 清理已删除的照片
     */
    suspend fun cleanupDeletedPhotos(): Result<Int>

    // ========== 导入导出 ==========

    /**
     * 导入照片
     */
    suspend fun importPhoto(filePath: String): Result<Photo>

    /**
     * 批量导入照片
     */
    suspend fun importPhotos(filePaths: List<String>): Result<List<Photo>>

    /**
     * 导出照片
     */
    suspend fun exportPhoto(photoId: String, destinationPath: String): Result<String>

    /**
     * 批量导出照片
     */
    suspend fun exportPhotos(photoIds: List<String>, destinationPath: String): Result<List<String>>
}
