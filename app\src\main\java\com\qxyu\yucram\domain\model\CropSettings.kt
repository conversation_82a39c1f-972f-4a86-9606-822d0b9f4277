package com.qxyu.yucram.domain.model

import android.graphics.Matrix
import android.graphics.RectF
import androidx.compose.runtime.Immutable

/**
 * 裁剪设置数据模型
 */
@Immutable
data class CropSettings(
    val cropRect: CropRect = CropRect(),
    val aspectRatio: AspectRatio = AspectRatio.FREE,
    val customAspectRatio: Float? = null,
    val showGrid: Boolean = true,
    val gridType: GridType = GridType.RULE_OF_THIRDS,
    val snapToGrid: Boolean = false,
    val cropMode: CropMode = CropMode.NORMAL,
    val isLocked: Boolean = false,
    val minCropSize: Float = 0.1f,
    val maxCropSize: Float = 1.0f
) {
    /**
     * 获取实际的宽高比
     */
    fun getActualAspectRatio(): Float? {
        return when (aspectRatio) {
            AspectRatio.FREE -> null
            AspectRatio.CUSTOM -> customAspectRatio
            else -> aspectRatio.ratio
        }
    }
    
    /**
     * 检查裁剪设置是否有效
     */
    fun isValid(): Boolean {
        return cropRect.isValid() && 
               minCropSize > 0 && 
               maxCropSize <= 1.0f && 
               minCropSize <= maxCropSize &&
               (aspectRatio != AspectRatio.CUSTOM || customAspectRatio != null)
    }
}

/**
 * 裁剪矩形
 */
@Immutable
data class CropRect(
    val left: Float = 0f,
    val top: Float = 0f,
    val right: Float = 1f,
    val bottom: Float = 1f
) {
    val width: Float get() = right - left
    val height: Float get() = bottom - top
    val centerX: Float get() = (left + right) / 2f
    val centerY: Float get() = (top + bottom) / 2f
    
    /**
     * 转换为Android RectF
     */
    fun toRectF(): RectF = RectF(left, top, right, bottom)
    
    /**
     * 检查矩形是否有效
     */
    fun isValid(): Boolean {
        return left >= 0f && top >= 0f && right <= 1f && bottom <= 1f &&
               left < right && top < bottom
    }
    
    /**
     * 获取宽高比
     */
    fun getAspectRatio(): Float = if (height > 0) width / height else 1f
    
    /**
     * 缩放裁剪矩形
     */
    fun scale(scaleX: Float, scaleY: Float): CropRect {
        return copy(
            left = left * scaleX,
            top = top * scaleY,
            right = right * scaleX,
            bottom = bottom * scaleY
        )
    }
    
    /**
     * 平移裁剪矩形
     */
    fun translate(dx: Float, dy: Float): CropRect {
        return copy(
            left = (left + dx).coerceIn(0f, 1f - width),
            top = (top + dy).coerceIn(0f, 1f - height),
            right = (right + dx).coerceIn(width, 1f),
            bottom = (bottom + dy).coerceIn(height, 1f)
        )
    }
    
    companion object {
        /**
         * 创建居中的裁剪矩形
         */
        fun centered(width: Float, height: Float): CropRect {
            val w = width.coerceIn(0.1f, 1f)
            val h = height.coerceIn(0.1f, 1f)
            val left = (1f - w) / 2f
            val top = (1f - h) / 2f
            return CropRect(left, top, left + w, top + h)
        }
        
        /**
         * 从宽高比创建裁剪矩形
         */
        fun fromAspectRatio(aspectRatio: Float, maxSize: Float = 0.8f): CropRect {
            return if (aspectRatio >= 1f) {
                // 横向
                val width = maxSize
                val height = width / aspectRatio
                centered(width, height)
            } else {
                // 纵向
                val height = maxSize
                val width = height * aspectRatio
                centered(width, height)
            }
        }
    }
}

/**
 * 宽高比枚举
 */
enum class AspectRatio(
    val displayName: String,
    val ratio: Float?,
    val description: String
) {
    FREE("自由", null, "自由裁剪，不限制宽高比"),
    SQUARE("1:1", 1f, "正方形，适合社交媒体"),
    PHOTO_4_3("4:3", 4f/3f, "传统照片比例"),
    PHOTO_3_2("3:2", 3f/2f, "35mm胶片比例"),
    WIDESCREEN_16_9("16:9", 16f/9f, "宽屏比例，适合视频"),
    WIDESCREEN_16_10("16:10", 16f/10f, "宽屏比例"),
    CINEMA_21_9("21:9", 21f/9f, "电影宽屏比例"),
    PORTRAIT_9_16("9:16", 9f/16f, "竖屏比例，适合手机"),
    PORTRAIT_4_5("4:5", 4f/5f, "Instagram竖屏比例"),
    GOLDEN_RATIO("黄金比例", 1.618f, "黄金分割比例"),
    CUSTOM("自定义", null, "用户自定义比例");
    
    /**
     * 是否为横向比例
     */
    val isLandscape: Boolean get() = ratio != null && ratio!! > 1f
    
    /**
     * 是否为纵向比例
     */
    val isPortrait: Boolean get() = ratio != null && ratio!! < 1f
    
    /**
     * 是否为正方形
     */
    val isSquare: Boolean get() = ratio == 1f
}

/**
 * 网格类型
 */
enum class GridType(
    val displayName: String,
    val description: String
) {
    NONE("无网格", "不显示辅助网格"),
    RULE_OF_THIRDS("三分法", "九宫格辅助线，经典构图法则"),
    GOLDEN_RATIO("黄金分割", "黄金分割辅助线"),
    DIAGONAL("对角线", "对角线辅助构图"),
    TRIANGLE("三角形", "三角形构图辅助线"),
    SPIRAL("螺旋线", "黄金螺旋构图线"),
    CENTER("中心线", "水平垂直中心线"),
    CUSTOM("自定义", "用户自定义网格")
}

/**
 * 裁剪模式
 */
enum class CropMode(
    val displayName: String,
    val description: String
) {
    NORMAL("普通裁剪", "标准的矩形裁剪"),
    CIRCLE("圆形裁剪", "圆形裁剪，适合头像"),
    ROUNDED("圆角裁剪", "圆角矩形裁剪"),
    POLYGON("多边形", "多边形自由裁剪"),
    FREEFORM("自由形状", "完全自由的形状裁剪")
}

/**
 * 裁剪操作历史
 */
@Immutable
data class CropHistory(
    val id: String,
    val timestamp: Long,
    val cropSettings: CropSettings,
    val description: String,
    val canUndo: Boolean = true
)

/**
 * 裁剪预设
 */
@Immutable
data class CropPreset(
    val id: String,
    val name: String,
    val description: String,
    val aspectRatio: AspectRatio,
    val customRatio: Float? = null,
    val gridType: GridType = GridType.RULE_OF_THIRDS,
    val isBuiltIn: Boolean = true,
    val category: CropPresetCategory = CropPresetCategory.GENERAL,
    val icon: String? = null,
    val usageCount: Int = 0,
    val isFavorite: Boolean = false
)

/**
 * 裁剪预设分类
 */
enum class CropPresetCategory(
    val displayName: String,
    val description: String
) {
    GENERAL("通用", "通用裁剪预设"),
    SOCIAL_MEDIA("社交媒体", "适合各种社交平台"),
    PHOTOGRAPHY("摄影", "专业摄影构图"),
    PRINT("打印", "适合打印的尺寸"),
    VIDEO("视频", "视频相关比例"),
    CUSTOM("自定义", "用户自定义预设")
}

/**
 * 智能裁剪建议
 */
@Immutable
data class SmartCropSuggestion(
    val id: String,
    val cropRect: CropRect,
    val aspectRatio: AspectRatio,
    val confidence: Float, // 0-1，建议的置信度
    val reason: String, // 建议理由
    val category: SmartCropCategory,
    val score: Float // 构图评分
)

/**
 * 智能裁剪分类
 */
enum class SmartCropCategory(
    val displayName: String,
    val description: String
) {
    FACE_CENTERED("人脸居中", "以人脸为中心的裁剪"),
    RULE_OF_THIRDS("三分法构图", "符合三分法则的裁剪"),
    GOLDEN_RATIO("黄金分割", "黄金分割构图"),
    SYMMETRY("对称构图", "对称性构图"),
    LEADING_LINES("引导线", "利用引导线的构图"),
    SUBJECT_ISOLATION("主体突出", "突出主要拍摄对象"),
    HORIZON_CORRECTION("水平校正", "校正水平线的裁剪"),
    CONTENT_AWARE("内容感知", "基于图像内容的智能裁剪")
}
