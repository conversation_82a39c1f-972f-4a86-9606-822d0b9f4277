package com.qxyu.yucram.presentation.photoedit.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000^\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000e\n\u0002\b\u0002\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a8\u0010\b\u001a\u00020\u00012\b\u0010\t\u001a\u0004\u0018\u00010\n2\u0006\u0010\u000b\u001a\u00020\f2\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u001a\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a4\u0010\u0011\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\"\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a@\u0010\u0016\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001aL\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u001c2\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u001e2\u0006\u0010\u001f\u001a\u00020\u001c2\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u001e2\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00010\u001e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a4\u0010\"\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00140\u00132\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a6\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020\u00182\u0006\u0010\u0004\u001a\u00020\u00052\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u000f\u0012\u0004\u0012\u00020\u00010\u000e2\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u0010\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\fH\u0002\u00a8\u0006("}, d2 = {"BeforeAfterComparison", "", "photo", "Lcom/qxyu/yucram/domain/model/Photo;", "editParams", "Lcom/qxyu/yucram/domain/model/PhotoEditParams;", "modifier", "Landroidx/compose/ui/Modifier;", "CropOverlay", "cropRatio", "Lcom/qxyu/yucram/domain/model/CropRatio;", "rotation", "", "onInteraction", "Lkotlin/Function1;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction;", "EditInfoDisplay", "GraduatedFilterOverlay", "localAdjustments", "", "Lcom/qxyu/yucram/domain/model/LocalAdjustment;", "HistogramDisplay", "PhotoEditPreview", "currentTool", "Lcom/qxyu/yucram/domain/model/EditTool;", "onToolInteraction", "PreviewControls", "showBeforeAfter", "", "onToggleBeforeAfter", "Lkotlin/Function0;", "showHistogram", "onToggleHistogram", "onResetZoom", "RadialFilterOverlay", "ToolOverlay", "tool", "formatValue", "", "value", "app_debug"})
public final class PhotoEditPreviewKt {
    
    /**
     * 照片编辑预览组件
     */
    @androidx.compose.runtime.Composable()
    public static final void PhotoEditPreview(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Photo photo, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.EditTool currentTool, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.ToolInteraction, kotlin.Unit> onToolInteraction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 预览控制栏
     */
    @androidx.compose.runtime.Composable()
    public static final void PreviewControls(boolean showBeforeAfter, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onToggleBeforeAfter, boolean showHistogram, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onToggleHistogram, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onResetZoom, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 编辑信息显示
     */
    @androidx.compose.runtime.Composable()
    public static final void EditInfoDisplay(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 工具覆盖层
     */
    @androidx.compose.runtime.Composable()
    public static final void ToolOverlay(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.EditTool tool, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.ToolInteraction, kotlin.Unit> onInteraction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 直方图显示
     */
    @androidx.compose.runtime.Composable()
    public static final void HistogramDisplay(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Photo photo, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 对比显示
     */
    @androidx.compose.runtime.Composable()
    public static final void BeforeAfterComparison(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.Photo photo, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams editParams, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 裁剪覆盖层
     */
    @androidx.compose.runtime.Composable()
    public static final void CropOverlay(@org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.CropRatio cropRatio, float rotation, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.ToolInteraction, kotlin.Unit> onInteraction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 径向滤镜覆盖层
     */
    @androidx.compose.runtime.Composable()
    public static final void RadialFilterOverlay(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LocalAdjustment> localAdjustments, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.ToolInteraction, kotlin.Unit> onInteraction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 渐变滤镜覆盖层
     */
    @androidx.compose.runtime.Composable()
    public static final void GraduatedFilterOverlay(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.LocalAdjustment> localAdjustments, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.ToolInteraction, kotlin.Unit> onInteraction, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 格式化数值
     */
    private static final java.lang.String formatValue(float value) {
        return null;
    }
}