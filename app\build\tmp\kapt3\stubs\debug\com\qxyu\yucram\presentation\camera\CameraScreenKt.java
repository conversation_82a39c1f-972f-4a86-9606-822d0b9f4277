package com.qxyu.yucram.presentation.camera;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\n\u001aD\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\f\u0010\r\u001aD\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a(\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a\u001c\u0010\u0015\u001a\u00020\u00012\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u001a(\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a8\u0010\u0019\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u001a\u001a\u00020\u00102\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001aX\u0010\u001b\u001a\u00020\u00012\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a0\u0010!\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\u0006\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001a(\u0010\"\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001aj\u0010#\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\u0006\u0010&\u001a\u00020\u00102\u0006\u0010\'\u001a\u00020\u00102\u0006\u0010(\u001a\u00020\u00102\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u001ab\u0010-\u001a\u00020\u00012\u0006\u0010$\u001a\u00020%2\u0006\u0010(\u001a\u00020\u00102\u0006\u0010\'\u001a\u00020\u00102\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010,\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\n\u001a\u00020\u000bH\u0003\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006/"}, d2 = {"ActionButton", "", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "onClick", "Lkotlin/Function0;", "contentDescription", "", "size", "Landroidx/compose/ui/unit/Dp;", "modifier", "Landroidx/compose/ui/Modifier;", "ActionButton-2lqI77k", "(Landroidx/compose/ui/graphics/vector/ImageVector;Lkotlin/jvm/functions/Function0;Ljava/lang/String;FLandroidx/compose/ui/Modifier;)V", "BottomActionBar", "isCapturing", "", "onFilterClick", "onCaptureClick", "onGalleryClick", "BottomControlBar", "CameraScreen", "viewModel", "Lcom/qxyu/yucram/presentation/camera/CameraViewModel;", "CaptureButton", "FunctionButton", "isActive", "RightSideControls", "onCameraSwitchClick", "onZoomClick", "onExposureClick", "onWhiteBalanceClick", "onIsoClick", "SideControlButton", "SuperCaptureButton", "TopControlBar", "flashMode", "Lcom/qxyu/yucram/domain/model/FlashMode;", "isGridEnabled", "isRawEnabled", "isHdrEnabled", "onFlashToggle", "onGridToggle", "onRawToggle", "onHdrToggle", "TopFunctionBar", "onSettingsClick", "app_debug"})
public final class CameraScreenKt {
    
    /**
     * 相机主界面
     */
    @androidx.compose.runtime.Composable()
    public static final void CameraScreen(@org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.camera.CameraViewModel viewModel) {
    }
    
    /**
     * 顶部功能栏 (闪光灯、HDR、RAW、设置)
     */
    @androidx.compose.runtime.Composable()
    private static final void TopFunctionBar(com.qxyu.yucram.domain.model.FlashMode flashMode, boolean isHdrEnabled, boolean isRawEnabled, kotlin.jvm.functions.Function0<kotlin.Unit> onFlashToggle, kotlin.jvm.functions.Function0<kotlin.Unit> onHdrToggle, kotlin.jvm.functions.Function0<kotlin.Unit> onRawToggle, kotlin.jvm.functions.Function0<kotlin.Unit> onSettingsClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 功能按钮组件
     */
    @androidx.compose.runtime.Composable()
    private static final void FunctionButton(androidx.compose.ui.graphics.vector.ImageVector icon, boolean isActive, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, java.lang.String contentDescription, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 右侧参数控制栏 (半透明悬浮)
     */
    @androidx.compose.runtime.Composable()
    private static final void RightSideControls(kotlin.jvm.functions.Function0<kotlin.Unit> onCameraSwitchClick, kotlin.jvm.functions.Function0<kotlin.Unit> onZoomClick, kotlin.jvm.functions.Function0<kotlin.Unit> onExposureClick, kotlin.jvm.functions.Function0<kotlin.Unit> onWhiteBalanceClick, kotlin.jvm.functions.Function0<kotlin.Unit> onIsoClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 侧边控制按钮
     */
    @androidx.compose.runtime.Composable()
    private static final void SideControlButton(androidx.compose.ui.graphics.vector.ImageVector icon, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, java.lang.String contentDescription, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 底部操作栏 (滤镜、拍摄、图库)
     */
    @androidx.compose.runtime.Composable()
    private static final void BottomActionBar(boolean isCapturing, kotlin.jvm.functions.Function0<kotlin.Unit> onFilterClick, kotlin.jvm.functions.Function0<kotlin.Unit> onCaptureClick, kotlin.jvm.functions.Function0<kotlin.Unit> onGalleryClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 超大拍摄按钮
     */
    @androidx.compose.runtime.Composable()
    private static final void SuperCaptureButton(boolean isCapturing, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 顶部控制栏
     */
    @androidx.compose.runtime.Composable()
    private static final void TopControlBar(com.qxyu.yucram.domain.model.FlashMode flashMode, boolean isGridEnabled, boolean isRawEnabled, boolean isHdrEnabled, kotlin.jvm.functions.Function0<kotlin.Unit> onFlashToggle, kotlin.jvm.functions.Function0<kotlin.Unit> onGridToggle, kotlin.jvm.functions.Function0<kotlin.Unit> onRawToggle, kotlin.jvm.functions.Function0<kotlin.Unit> onHdrToggle, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 底部控制栏
     */
    @androidx.compose.runtime.Composable()
    private static final void BottomControlBar(boolean isCapturing, kotlin.jvm.functions.Function0<kotlin.Unit> onCaptureClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 拍摄按钮组件
     * 带有动画效果和视觉反馈
     */
    @androidx.compose.runtime.Composable()
    private static final void CaptureButton(boolean isCapturing, kotlin.jvm.functions.Function0<kotlin.Unit> onCaptureClick, androidx.compose.ui.Modifier modifier) {
    }
}