package com.qxyu.yucram.presentation.filter;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000X\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0007\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a0\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001aF\u0010\n\u001a\u00020\u00012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\b\u0010\r\u001a\u0004\u0018\u00010\u00032\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000f2\u0006\u0010\u0010\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001a.\u0010\u0011\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001a>\u0010\u0014\u001a\u00020\u00012\u0012\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000f2\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\u0016\u001a\u00020\u0017H\u0007\u001a4\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u001a2\u0018\u0010\u001b\u001a\u0014\u0012\u0004\u0012\u00020\u001d\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u00010\u001c2\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u001aD\u0010\u001f\u001a\u00020\u00012\u0006\u0010 \u001a\u00020\u001d2\u0006\u0010!\u001a\u00020\u001e2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u001e0#2\u0012\u0010$\u001a\u000e\u0012\u0004\u0012\u00020\u001e\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\b\u001a\u00020\tH\u0003\u00a8\u0006%"}, d2 = {"FilterPresetCard", "", "filter", "Lcom/qxyu/yucram/domain/model/FilterPreset;", "isSelected", "", "onClick", "Lkotlin/Function0;", "modifier", "Landroidx/compose/ui/Modifier;", "FilterPresetGrid", "filters", "", "selectedFilter", "onFilterClick", "Lkotlin/Function1;", "isLoading", "FilterSelectionHeader", "onDismiss", "onImportClick", "FilterSelectionScreen", "onFilterSelected", "viewModel", "Lcom/qxyu/yucram/presentation/filter/FilterSelectionViewModel;", "PostProcessingControls", "params", "Lcom/qxyu/yucram/domain/model/PostProcessingParams;", "onParameterChanged", "Lkotlin/Function2;", "", "", "PostProcessingSlider", "label", "value", "valueRange", "Lkotlin/ranges/ClosedFloatingPointRange;", "onValueChange", "app_debug"})
public final class FilterSelectionScreenKt {
    
    /**
     * 滤镜选择界面
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void FilterSelectionScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.FilterPreset, kotlin.Unit> onFilterSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.filter.FilterSelectionViewModel viewModel) {
    }
    
    /**
     * 滤镜选择标题栏
     */
    @androidx.compose.runtime.Composable()
    private static final void FilterSelectionHeader(kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, kotlin.jvm.functions.Function0<kotlin.Unit> onImportClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 滤镜预设网格
     */
    @androidx.compose.runtime.Composable()
    private static final void FilterPresetGrid(java.util.List<com.qxyu.yucram.domain.model.FilterPreset> filters, com.qxyu.yucram.domain.model.FilterPreset selectedFilter, kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.FilterPreset, kotlin.Unit> onFilterClick, boolean isLoading, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 滤镜预设卡片
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void FilterPresetCard(com.qxyu.yucram.domain.model.FilterPreset filter, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 后处理参数控制
     */
    @androidx.compose.runtime.Composable()
    private static final void PostProcessingControls(com.qxyu.yucram.domain.model.PostProcessingParams params, kotlin.jvm.functions.Function2<? super java.lang.String, ? super java.lang.Float, kotlin.Unit> onParameterChanged, androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 后处理参数滑块
     */
    @androidx.compose.runtime.Composable()
    private static final void PostProcessingSlider(java.lang.String label, float value, kotlin.ranges.ClosedFloatingPointRange<java.lang.Float> valueRange, kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onValueChange, androidx.compose.ui.Modifier modifier) {
    }
}