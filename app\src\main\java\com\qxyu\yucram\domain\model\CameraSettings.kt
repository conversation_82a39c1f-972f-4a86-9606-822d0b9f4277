package com.qxyu.yucram.domain.model

/**
 * 相机设置数据模型
 */
data class CameraSettings(
    val aspectRatio: AspectRatio = AspectRatio.RATIO_4_3,
    val imageQuality: ImageQuality = ImageQuality.HIGH,
    val imageFormat: ImageFormat = ImageFormat.JPEG,
    val isRawEnabled: Boolean = false,
    val isLogEnabled: Boolean = false,
    val isHdrEnabled: Boolean = false,
    val flashMode: FlashMode = FlashMode.AUTO,
    val isGridEnabled: Boolean = false,
    val isLevelEnabled: Boolean = false,
    val timerDuration: Int = 0, // 0 means no timer
    val zoomLevel: Float = 1.0f,
    val exposureCompensation: Int = 0,
    val focusMode: FocusMode = FocusMode.AUTO,
    val whiteBalance: WhiteBalance = WhiteBalance.AUTO
)

enum class AspectRatio(val ratio: String, val value: Float) {
    RATIO_1_1("1:1", 1.0f),
    RATIO_4_3("4:3", 4.0f / 3.0f),
    RATIO_3_2("3:2", 3.0f / 2.0f),
    RATIO_16_9("16:9", 16.0f / 9.0f)
}

enum class ImageQuality {
    LOW, MEDIUM, HIGH
}

enum class ImageFormat {
    JPEG, PNG, RAW, HEIF
}

enum class FlashMode {
    AUTO, ON, OFF, TORCH
}

enum class FocusMode {
    AUTO, MANUAL, CONTINUOUS
}

enum class WhiteBalance {
    AUTO, DAYLIGHT, CLOUDY, TUNGSTEN, FLUORESCENT, MANUAL
}
