package com.qxyu.yucram.filter

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.util.Log
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.film.LutProcessor
import com.qxyu.yucram.processing.RawToLogProcessor
import com.qxyu.yucram.camera.RawCaptureManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 图像处理管道
 * 实现基于RAW/LOG的专业色彩处理流程
 */
@Singleton
class ImageProcessingPipeline @Inject constructor(
    @ApplicationContext private val context: Context,
    private val lutProcessor: LutProcessor,
    private val deviceCapabilityDetector: DeviceCapabilityDetector,
    private val rawToLogProcessor: RawToLogProcessor,
    private val rawCaptureManager: RawCaptureManager
) {
    
    companion object {
        private const val TAG = "ImageProcessingPipeline"
    }
    
    /**
     * 处理图像的主要入口点 - 专业RAW到LOG处理管道
     */
    suspend fun processImage(
        imageData: ByteArray,
        sourceFormat: ImageSourceFormat,
        filterPreset: FilterPreset,
        captureResult: android.hardware.camera2.CaptureResult? = null
    ): Result<ProcessedImage> = withContext(Dispatchers.Default) {
        try {
            Log.d(TAG, "开始处理图像，源格式: ${sourceFormat.displayName}, 滤镜: ${filterPreset.name}")
            
            // 1. 解码原始图像
            val originalBitmap = decodeImage(imageData, sourceFormat)
                ?: return@withContext Result.failure(Exception("无法解码图像"))
            
            // 2. 专业RAW到LOG处理管道
            val preprocessedBitmap = when (sourceFormat) {
                ImageSourceFormat.RAW -> {
                    // 专业RAW处理：RAW解码 → 线性RGB → LOG色彩空间
                    if (captureResult != null) {
                        processRawToLogProfessional(imageData, captureResult, originalBitmap)
                    } else {
                        // 降级到基础RAW处理
                        processRawImage(originalBitmap)
                    }
                }
                ImageSourceFormat.LOG -> {
                    // LOG图像处理（如果设备支持）
                    processLogImage(originalBitmap)
                }
                ImageSourceFormat.JPEG -> {
                    // JPEG图像处理：转换为LOG基础以获得最大可编辑性
                    convertJpegToLogBase(originalBitmap)
                }
            }
            
            // 3. 应用LUT
            val lutProcessedBitmap = applyLut(preprocessedBitmap, filterPreset.lutFilePath)
            
            // 4. 后处理效果
            val finalBitmap = applyPostProcessing(lutProcessedBitmap, filterPreset.postProcessingParams)
            
            // 5. 转换为输出格式
            val outputData = bitmapToByteArray(finalBitmap)
            
            // 清理内存
            if (originalBitmap != preprocessedBitmap) originalBitmap.recycle()
            if (preprocessedBitmap != lutProcessedBitmap) preprocessedBitmap.recycle()
            if (lutProcessedBitmap != finalBitmap) lutProcessedBitmap.recycle()
            
            val processedImage = ProcessedImage(
                data = outputData,
                width = finalBitmap.width,
                height = finalBitmap.height,
                format = ProcessedImageFormat.JPEG,
                colorSpace = ColorSpace.SRGB,
                metadata = ImageMetadata(
                    processingHistory = listOf("${sourceFormat.displayName} -> LUT -> PostProcessing"),
                    filmPresetUsed = filterPreset.name,
                    lutApplied = filterPreset.lutFilePath
                )
            )
            
            finalBitmap.recycle()
            
            Log.d(TAG, "图像处理完成")
            Result.success(processedImage)
            
        } catch (e: Exception) {
            Log.e(TAG, "图像处理失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查是否支持指定的源格式
     */
    fun isSourceFormatSupported(format: ImageSourceFormat): Boolean {
        return deviceCapabilityDetector.isFormatSupported(format)
    }
    
    /**
     * 获取推荐的源格式
     */
    fun getRecommendedSourceFormat(): ImageSourceFormat {
        // 基于实际设备能力推荐格式
        return deviceCapabilityDetector.getRecommendedFormat()
    }
    
    // 私有方法
    
    /**
     * 解码图像
     */
    private fun decodeImage(imageData: ByteArray, sourceFormat: ImageSourceFormat): Bitmap? {
        return try {
            when (sourceFormat) {
                ImageSourceFormat.RAW -> decodeRawImage(imageData)
                ImageSourceFormat.LOG -> decodeLogImage(imageData)
                ImageSourceFormat.JPEG -> BitmapFactory.decodeByteArray(imageData, 0, imageData.size)
            }
        } catch (e: Exception) {
            Log.e(TAG, "解码图像失败", e)
            null
        }
    }
    
    /**
     * 解码RAW图像
     */
    private fun decodeRawImage(rawData: ByteArray): Bitmap? {
        // 这里需要实现RAW图像解码
        // 可以使用Android的DngCreator或第三方库如LibRaw
        // 暂时使用JPEG解码作为占位符
        return BitmapFactory.decodeByteArray(rawData, 0, rawData.size)
    }
    
    /**
     * 解码LOG图像
     */
    private fun decodeLogImage(logData: ByteArray): Bitmap? {
        // 这里需要实现LOG图像解码
        // 根据设备的LOG格式进行特殊处理
        val logCapability = deviceCapabilityDetector.detectLogSupport()
        
        return when (logCapability) {
            LogCapability.OPPO_LOG -> decodeOppoLogImage(logData)
            LogCapability.SONY_SLOG -> decodeSonyLogImage(logData)
            else -> BitmapFactory.decodeByteArray(logData, 0, logData.size)
        }
    }
    
    /**
     * 解码OPPO LOG图像
     */
    private fun decodeOppoLogImage(logData: ByteArray): Bitmap? {
        // OPPO Find X8 Ultra的LOG解码
        // 这里需要根据OPPO的LOG格式规范实现
        val bitmap = BitmapFactory.decodeByteArray(logData, 0, logData.size)
        
        // 应用OPPO LOG到线性的转换
        return bitmap?.let { applyOppoLogToLinear(it) }
    }
    
    /**
     * 解码Sony LOG图像
     */
    private fun decodeSonyLogImage(logData: ByteArray): Bitmap? {
        // Sony S-Log的解码实现
        return BitmapFactory.decodeByteArray(logData, 0, logData.size)
    }
    
    /**
     * 专业RAW到LOG处理
     */
    private suspend fun processRawToLogProfessional(
        rawData: ByteArray,
        captureResult: android.hardware.camera2.CaptureResult,
        fallbackBitmap: Bitmap
    ): Bitmap {
        Log.d(TAG, "开始专业RAW到LOG处理管道")

        return try {
            // 使用专业RAW处理器
            val result = rawToLogProcessor.processRawToLog(
                rawData = rawData,
                captureResult = captureResult,
                width = fallbackBitmap.width,
                height = fallbackBitmap.height
            )

            result.fold(
                onSuccess = { logBitmap ->
                    Log.d(TAG, "专业RAW到LOG处理成功")
                    logBitmap
                },
                onFailure = { error ->
                    Log.w(TAG, "专业RAW处理失败，使用降级方案", error)
                    // 降级到基础RAW处理
                    processRawImage(fallbackBitmap)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "专业RAW处理异常，使用降级方案", e)
            processRawImage(fallbackBitmap)
        }
    }

    /**
     * 处理RAW图像（基础方案）
     */
    private suspend fun processRawImage(bitmap: Bitmap): Bitmap {
        // RAW图像的色彩空间转换和基础处理
        return applyRawProcessing(bitmap)
    }
    
    /**
     * 处理LOG图像
     */
    private suspend fun processLogImage(bitmap: Bitmap): Bitmap {
        val logCapability = deviceCapabilityDetector.detectLogSupport()

        return when (logCapability) {
            LogCapability.OPPO_LOG -> applyOppoLogProcessing(bitmap)
            LogCapability.SONY_SLOG -> applySonyLogProcessing(bitmap)
            else -> bitmap
        }
    }

    /**
     * 处理JPEG图像
     */
    private suspend fun processJpegImage(bitmap: Bitmap): Bitmap {
        // JPEG图像的色彩优化处理
        // 应用轻微的色彩增强，为LUT处理做准备
        val colorMatrix = ColorMatrix().apply {
            // 轻微增强对比度和饱和度
            val contrast = 1.05f
            val saturation = 1.02f

            // 对比度调整
            setScale(contrast, contrast, contrast, 1.0f)

            // 饱和度调整
            val satMatrix = ColorMatrix()
            satMatrix.setSaturation(saturation)
            postConcat(satMatrix)
        }

        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    /**
     * 应用LUT
     */
    private suspend fun applyLut(bitmap: Bitmap, lutFilePath: String): Bitmap {
        // 加载LUT数据
        val lutResult = lutProcessor.loadLutFile(lutFilePath)
        
        return lutResult.fold(
            onSuccess = { lutData ->
                lutProcessor.applyLutToImage(bitmap, lutData, 1.0f).getOrElse { bitmap }
            },
            onFailure = {
                Log.w(TAG, "应用LUT失败，使用原图像")
                bitmap
            }
        )
    }
    
    /**
     * 应用后处理效果
     */
    private fun applyPostProcessing(bitmap: Bitmap, params: PostProcessingParams): Bitmap {
        var processedBitmap = bitmap
        
        // 应用高光阴影调整
        if (params.highlights != 0.0f || params.shadows != 0.0f) {
            processedBitmap = applyHighlightsShadows(processedBitmap, params.highlights, params.shadows)
        }
        
        // 应用暗角效果
        if (params.vignette > 0.0f) {
            processedBitmap = applyVignette(processedBitmap, params.vignette)
        }
        
        // 应用色散效果
        if (params.chromaAberration > 0.0f) {
            processedBitmap = applyChromaticAberration(processedBitmap, params.chromaAberration)
        }
        
        // 应用颗粒效果
        if (params.grain > 0.0f) {
            processedBitmap = applyGrain(processedBitmap, params.grain)
        }
        
        // 应用锐度调整
        if (params.sharpness != 0.0f) {
            processedBitmap = applySharpness(processedBitmap, params.sharpness)
        }
        
        return processedBitmap
    }
    
    /**
     * OPPO LOG到线性空间的转换
     */
    private fun applyOppoLogToLinear(bitmap: Bitmap): Bitmap {
        // 这里需要实现OPPO LOG的具体转换曲线
        // 暂时使用简单的伽马校正作为占位符
        val colorMatrix = ColorMatrix().apply {
            // OPPO LOG的近似逆变换
            val gamma = 1.0f / 2.2f
            setScale(1.0f, 1.0f, 1.0f, 1.0f)
        }
        
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    /**
     * RAW处理
     */
    private fun applyRawProcessing(bitmap: Bitmap): Bitmap {
        // RAW图像的基础处理：白平衡、曝光、色彩空间转换
        val colorMatrix = ColorMatrix().apply {
            // 基础的RAW处理矩阵
            setScale(1.0f, 1.0f, 1.0f, 1.0f)
        }
        
        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    /**
     * OPPO LOG处理
     */
    private fun applyOppoLogProcessing(bitmap: Bitmap): Bitmap {
        // OPPO LOG的专门处理
        return applyOppoLogToLinear(bitmap)
    }
    
    /**
     * Sony LOG处理
     */
    private fun applySonyLogProcessing(bitmap: Bitmap): Bitmap {
        // Sony S-Log的处理
        return bitmap
    }

    /**
     * 将RAW转换为LOG静帧基础
     */
    private fun convertRawToLogBase(bitmap: Bitmap): Bitmap {
        // 将RAW图像转换为LOG静帧的灰片基础
        // 这里实现RAW到LOG的色彩空间转换
        val colorMatrix = ColorMatrix().apply {
            // 应用LOG曲线变换
            // 这里使用简化的LOG变换，实际应该根据具体的LOG格式调整
            val logGamma = 0.45f // LOG曲线的伽马值
            setScale(logGamma, logGamma, logGamma, 1.0f)
        }

        return applyColorMatrix(bitmap, colorMatrix)
    }

    /**
     * 将JPEG转换为LOG静帧基础
     */
    private fun convertJpegToLogBase(bitmap: Bitmap): Bitmap {
        // 将JPEG图像转换为LOG静帧的灰片基础
        // 先去除JPEG的伽马校正，然后应用LOG曲线
        val colorMatrix = ColorMatrix().apply {
            // 逆伽马校正 + LOG变换
            val invGamma = 1.0f / 2.2f // 逆伽马
            val logGamma = 0.45f // LOG曲线
            val combinedGamma = invGamma * logGamma
            setScale(combinedGamma, combinedGamma, combinedGamma, 1.0f)
        }

        return applyColorMatrix(bitmap, colorMatrix)
    }
    
    /**
     * 应用颜色矩阵
     */
    private fun applyColorMatrix(bitmap: Bitmap, colorMatrix: ColorMatrix): Bitmap {
        val result = Bitmap.createBitmap(bitmap.width, bitmap.height, bitmap.config)
        val canvas = Canvas(result)
        val paint = Paint().apply {
            colorFilter = ColorMatrixColorFilter(colorMatrix)
        }
        canvas.drawBitmap(bitmap, 0f, 0f, paint)
        return result
    }
    
    /**
     * 应用高光阴影调整
     */
    private fun applyHighlightsShadows(bitmap: Bitmap, highlights: Float, shadows: Float): Bitmap {
        // 实现高光阴影调整算法
        return bitmap // 占位符实现
    }
    
    /**
     * 应用暗角效果
     */
    private fun applyVignette(bitmap: Bitmap, intensity: Float): Bitmap {
        // 实现暗角效果
        return bitmap // 占位符实现
    }
    
    /**
     * 应用色散效果
     */
    private fun applyChromaticAberration(bitmap: Bitmap, intensity: Float): Bitmap {
        // 实现色散效果
        return bitmap // 占位符实现
    }
    
    /**
     * 应用颗粒效果
     */
    private fun applyGrain(bitmap: Bitmap, intensity: Float): Bitmap {
        // 实现颗粒效果
        return bitmap // 占位符实现
    }
    
    /**
     * 应用锐度调整
     */
    private fun applySharpness(bitmap: Bitmap, intensity: Float): Bitmap {
        // 实现锐度调整
        return bitmap // 占位符实现
    }
    
    /**
     * 将Bitmap转换为字节数组
     */
    private fun bitmapToByteArray(bitmap: Bitmap): ByteArray {
        val stream = java.io.ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 95, stream)
        return stream.toByteArray()
    }
}
