package com.qxyu.yucram.presentation.photoedit.components;

/**
 * 工具交互事件
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0004\u0003\u0004\u0005\u0006B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0004\u0007\b\t\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction;", "", "()V", "CropChanged", "LocalAdjustmentAdded", "LocalAdjustmentModified", "LocalAdjustmentRemoved", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$CropChanged;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$LocalAdjustmentAdded;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$LocalAdjustmentModified;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$LocalAdjustmentRemoved;", "app_debug"})
public abstract class ToolInteraction {
    
    private ToolInteraction() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u0017\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u000b\u0010\u000b\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001f\u0010\r\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001R\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0016"}, d2 = {"Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$CropChanged;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction;", "cropRatio", "Lcom/qxyu/yucram/domain/model/CropRatio;", "rotation", "", "(Lcom/qxyu/yucram/domain/model/CropRatio;F)V", "getCropRatio", "()Lcom/qxyu/yucram/domain/model/CropRatio;", "getRotation", "()F", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class CropChanged extends com.qxyu.yucram.presentation.photoedit.components.ToolInteraction {
        @org.jetbrains.annotations.Nullable()
        private final com.qxyu.yucram.domain.model.CropRatio cropRatio = null;
        private final float rotation = 0.0F;
        
        public CropChanged(@org.jetbrains.annotations.Nullable()
        com.qxyu.yucram.domain.model.CropRatio cropRatio, float rotation) {
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.qxyu.yucram.domain.model.CropRatio getCropRatio() {
            return null;
        }
        
        public final float getRotation() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.Nullable()
        public final com.qxyu.yucram.domain.model.CropRatio component1() {
            return null;
        }
        
        public final float component2() {
            return 0.0F;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.presentation.photoedit.components.ToolInteraction.CropChanged copy(@org.jetbrains.annotations.Nullable()
        com.qxyu.yucram.domain.model.CropRatio cropRatio, float rotation) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$LocalAdjustmentAdded;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction;", "adjustment", "Lcom/qxyu/yucram/domain/model/LocalAdjustment;", "(Lcom/qxyu/yucram/domain/model/LocalAdjustment;)V", "getAdjustment", "()Lcom/qxyu/yucram/domain/model/LocalAdjustment;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class LocalAdjustmentAdded extends com.qxyu.yucram.presentation.photoedit.components.ToolInteraction {
        @org.jetbrains.annotations.NotNull()
        private final com.qxyu.yucram.domain.model.LocalAdjustment adjustment = null;
        
        public LocalAdjustmentAdded(@org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.LocalAdjustment adjustment) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.LocalAdjustment getAdjustment() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.LocalAdjustment component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.presentation.photoedit.components.ToolInteraction.LocalAdjustmentAdded copy(@org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.LocalAdjustment adjustment) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u00d6\u0003J\t\u0010\u0012\u001a\u00020\u0013H\u00d6\u0001J\t\u0010\u0014\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0015"}, d2 = {"Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$LocalAdjustmentModified;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction;", "id", "", "adjustment", "Lcom/qxyu/yucram/domain/model/LocalAdjustment;", "(Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LocalAdjustment;)V", "getAdjustment", "()Lcom/qxyu/yucram/domain/model/LocalAdjustment;", "getId", "()Ljava/lang/String;", "component1", "component2", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class LocalAdjustmentModified extends com.qxyu.yucram.presentation.photoedit.components.ToolInteraction {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String id = null;
        @org.jetbrains.annotations.NotNull()
        private final com.qxyu.yucram.domain.model.LocalAdjustment adjustment = null;
        
        public LocalAdjustmentModified(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.LocalAdjustment adjustment) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.LocalAdjustment getAdjustment() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.domain.model.LocalAdjustment component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.presentation.photoedit.components.ToolInteraction.LocalAdjustmentModified copy(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        com.qxyu.yucram.domain.model.LocalAdjustment adjustment) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction$LocalAdjustmentRemoved;", "Lcom/qxyu/yucram/presentation/photoedit/components/ToolInteraction;", "id", "", "(Ljava/lang/String;)V", "getId", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class LocalAdjustmentRemoved extends com.qxyu.yucram.presentation.photoedit.components.ToolInteraction {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String id = null;
        
        public LocalAdjustmentRemoved(@org.jetbrains.annotations.NotNull()
        java.lang.String id) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.qxyu.yucram.presentation.photoedit.components.ToolInteraction.LocalAdjustmentRemoved copy(@org.jetbrains.annotations.NotNull()
        java.lang.String id) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
}