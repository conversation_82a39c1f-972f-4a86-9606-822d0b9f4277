// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.filter;

import android.content.Context;
import com.qxyu.yucram.camera.RawCaptureManager;
import com.qxyu.yucram.film.LutProcessor;
import com.qxyu.yucram.processing.RawToLogProcessor;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ImageProcessingPipeline_Factory implements Factory<ImageProcessingPipeline> {
  private final Provider<Context> contextProvider;

  private final Provider<LutProcessor> lutProcessorProvider;

  private final Provider<DeviceCapabilityDetector> deviceCapabilityDetectorProvider;

  private final Provider<RawToLogProcessor> rawToLogProcessorProvider;

  private final Provider<RawCaptureManager> rawCaptureManagerProvider;

  public ImageProcessingPipeline_Factory(Provider<Context> contextProvider,
      Provider<LutProcessor> lutProcessorProvider,
      Provider<DeviceCapabilityDetector> deviceCapabilityDetectorProvider,
      Provider<RawToLogProcessor> rawToLogProcessorProvider,
      Provider<RawCaptureManager> rawCaptureManagerProvider) {
    this.contextProvider = contextProvider;
    this.lutProcessorProvider = lutProcessorProvider;
    this.deviceCapabilityDetectorProvider = deviceCapabilityDetectorProvider;
    this.rawToLogProcessorProvider = rawToLogProcessorProvider;
    this.rawCaptureManagerProvider = rawCaptureManagerProvider;
  }

  @Override
  public ImageProcessingPipeline get() {
    return newInstance(contextProvider.get(), lutProcessorProvider.get(), deviceCapabilityDetectorProvider.get(), rawToLogProcessorProvider.get(), rawCaptureManagerProvider.get());
  }

  public static ImageProcessingPipeline_Factory create(Provider<Context> contextProvider,
      Provider<LutProcessor> lutProcessorProvider,
      Provider<DeviceCapabilityDetector> deviceCapabilityDetectorProvider,
      Provider<RawToLogProcessor> rawToLogProcessorProvider,
      Provider<RawCaptureManager> rawCaptureManagerProvider) {
    return new ImageProcessingPipeline_Factory(contextProvider, lutProcessorProvider, deviceCapabilityDetectorProvider, rawToLogProcessorProvider, rawCaptureManagerProvider);
  }

  public static ImageProcessingPipeline newInstance(Context context, LutProcessor lutProcessor,
      DeviceCapabilityDetector deviceCapabilityDetector, RawToLogProcessor rawToLogProcessor,
      RawCaptureManager rawCaptureManager) {
    return new ImageProcessingPipeline(context, lutProcessor, deviceCapabilityDetector, rawToLogProcessor, rawCaptureManager);
  }
}
