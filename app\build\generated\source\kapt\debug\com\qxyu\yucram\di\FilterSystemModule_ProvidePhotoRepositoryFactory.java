// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.di;

import com.qxyu.yucram.data.repository.PhotoRepositoryImpl;
import com.qxyu.yucram.domain.repository.PhotoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FilterSystemModule_ProvidePhotoRepositoryFactory implements Factory<PhotoRepository> {
  private final Provider<PhotoRepositoryImpl> photoRepositoryImplProvider;

  public FilterSystemModule_ProvidePhotoRepositoryFactory(
      Provider<PhotoRepositoryImpl> photoRepositoryImplProvider) {
    this.photoRepositoryImplProvider = photoRepositoryImplProvider;
  }

  @Override
  public PhotoRepository get() {
    return providePhotoRepository(photoRepositoryImplProvider.get());
  }

  public static FilterSystemModule_ProvidePhotoRepositoryFactory create(
      Provider<PhotoRepositoryImpl> photoRepositoryImplProvider) {
    return new FilterSystemModule_ProvidePhotoRepositoryFactory(photoRepositoryImplProvider);
  }

  public static PhotoRepository providePhotoRepository(PhotoRepositoryImpl photoRepositoryImpl) {
    return Preconditions.checkNotNullFromProvides(FilterSystemModule.INSTANCE.providePhotoRepository(photoRepositoryImpl));
  }
}
