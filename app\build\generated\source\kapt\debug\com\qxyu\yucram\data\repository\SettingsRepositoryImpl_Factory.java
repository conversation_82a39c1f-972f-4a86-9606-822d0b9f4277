// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.data.repository;

import com.qxyu.yucram.data.local.dao.SettingsDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsRepositoryImpl_Factory implements Factory<SettingsRepositoryImpl> {
  private final Provider<SettingsDao> settingsDaoProvider;

  public SettingsRepositoryImpl_Factory(Provider<SettingsDao> settingsDaoProvider) {
    this.settingsDaoProvider = settingsDaoProvider;
  }

  @Override
  public SettingsRepositoryImpl get() {
    return newInstance(settingsDaoProvider.get());
  }

  public static SettingsRepositoryImpl_Factory create(Provider<SettingsDao> settingsDaoProvider) {
    return new SettingsRepositoryImpl_Factory(settingsDaoProvider);
  }

  public static SettingsRepositoryImpl newInstance(SettingsDao settingsDao) {
    return new SettingsRepositoryImpl(settingsDao);
  }
}
