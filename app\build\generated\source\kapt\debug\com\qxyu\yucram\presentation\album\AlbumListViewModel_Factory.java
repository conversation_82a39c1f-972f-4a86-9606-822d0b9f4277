// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram.presentation.album;

import com.qxyu.yucram.domain.repository.AlbumRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AlbumListViewModel_Factory implements Factory<AlbumListViewModel> {
  private final Provider<AlbumRepository> albumRepositoryProvider;

  public AlbumListViewModel_Factory(Provider<AlbumRepository> albumRepositoryProvider) {
    this.albumRepositoryProvider = albumRepositoryProvider;
  }

  @Override
  public AlbumListViewModel get() {
    return newInstance(albumRepositoryProvider.get());
  }

  public static AlbumListViewModel_Factory create(
      Provider<AlbumRepository> albumRepositoryProvider) {
    return new AlbumListViewModel_Factory(albumRepositoryProvider);
  }

  public static AlbumListViewModel newInstance(AlbumRepository albumRepository) {
    return new AlbumListViewModel(albumRepository);
  }
}
