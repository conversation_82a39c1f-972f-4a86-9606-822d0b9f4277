package com.qxyu.yucram.presentation.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000(\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\u001a\u001a\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a*\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0003\u00a8\u0006\r"}, d2 = {"NavigationRail", "", "navController", "Landroidx/navigation/NavController;", "modifier", "Landroidx/compose/ui/Modifier;", "RailNavigationIcon", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "isSelected", "", "label", "", "app_debug"})
public final class NavigationRailKt {
    
    /**
     * 导航栏组件（用于平板横屏）
     */
    @androidx.compose.runtime.Composable()
    public static final void NavigationRail(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 导航栏图标组件
     */
    @androidx.compose.runtime.Composable()
    private static final void RailNavigationIcon(androidx.compose.ui.graphics.vector.ImageVector icon, boolean isSelected, java.lang.String label, androidx.compose.ui.Modifier modifier) {
    }
}