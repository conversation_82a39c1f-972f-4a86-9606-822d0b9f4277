package com.qxyu.yucram.domain.model;

/**
 * 混合模式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000e\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000e\u00a8\u0006\u000f"}, d2 = {"Lcom/qxyu/yucram/domain/model/BlendMode;", "", "(Ljava/lang/String;I)V", "NORMAL", "MULTIPLY", "SCREEN", "OVERLAY", "SOFT_LIGHT", "HARD_LIGHT", "COLOR_DODGE", "COLOR_BURN", "DARKEN", "LIGHTEN", "DIFFERENCE", "EXCLUSION", "app_debug"})
public enum BlendMode {
    /*public static final*/ NORMAL /* = new NORMAL() */,
    /*public static final*/ MULTIPLY /* = new MULTIPLY() */,
    /*public static final*/ SCREEN /* = new SCREEN() */,
    /*public static final*/ OVERLAY /* = new OVERLAY() */,
    /*public static final*/ SOFT_LIGHT /* = new SOFT_LIGHT() */,
    /*public static final*/ HARD_LIGHT /* = new HARD_LIGHT() */,
    /*public static final*/ COLOR_DODGE /* = new COLOR_DODGE() */,
    /*public static final*/ COLOR_BURN /* = new COLOR_BURN() */,
    /*public static final*/ DARKEN /* = new DARKEN() */,
    /*public static final*/ LIGHTEN /* = new LIGHTEN() */,
    /*public static final*/ DIFFERENCE /* = new DIFFERENCE() */,
    /*public static final*/ EXCLUSION /* = new EXCLUSION() */;
    
    BlendMode() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.BlendMode> getEntries() {
        return null;
    }
}