package com.qxyu.yucram.domain.model;

/**
 * 边框和水印组合设置
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B7\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\t\u0010\u0017\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u000bH\u00c6\u0003J;\u0010\u001c\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000bH\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u00072\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001f\u001a\u00020 H\u00d6\u0001J\t\u0010!\u001a\u00020\"H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016\u00a8\u0006#"}, d2 = {"Lcom/qxyu/yucram/domain/model/BorderWatermarkSettings;", "", "borderSettings", "Lcom/qxyu/yucram/domain/model/BorderSettings;", "watermarkSettings", "Lcom/qxyu/yucram/domain/model/WatermarkSettings;", "previewEnabled", "", "exportQuality", "", "exportFormat", "Lcom/qxyu/yucram/domain/model/ExportFormat;", "(Lcom/qxyu/yucram/domain/model/BorderSettings;Lcom/qxyu/yucram/domain/model/WatermarkSettings;ZFLcom/qxyu/yucram/domain/model/ExportFormat;)V", "getBorderSettings", "()Lcom/qxyu/yucram/domain/model/BorderSettings;", "getExportFormat", "()Lcom/qxyu/yucram/domain/model/ExportFormat;", "getExportQuality", "()F", "getPreviewEnabled", "()Z", "getWatermarkSettings", "()Lcom/qxyu/yucram/domain/model/WatermarkSettings;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class BorderWatermarkSettings {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.BorderSettings borderSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings = null;
    private final boolean previewEnabled = false;
    private final float exportQuality = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ExportFormat exportFormat = null;
    
    public BorderWatermarkSettings(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, boolean previewEnabled, float exportQuality, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ExportFormat exportFormat) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderSettings getBorderSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkSettings getWatermarkSettings() {
        return null;
    }
    
    public final boolean getPreviewEnabled() {
        return false;
    }
    
    public final float getExportQuality() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ExportFormat getExportFormat() {
        return null;
    }
    
    public BorderWatermarkSettings() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderSettings component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.WatermarkSettings component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ExportFormat component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BorderWatermarkSettings copy(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BorderSettings borderSettings, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.WatermarkSettings watermarkSettings, boolean previewEnabled, float exportQuality, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ExportFormat exportFormat) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}