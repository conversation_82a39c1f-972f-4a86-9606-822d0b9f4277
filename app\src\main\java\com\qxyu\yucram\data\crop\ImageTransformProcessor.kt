package com.qxyu.yucram.data.crop

import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import androidx.exifinterface.media.ExifInterface
import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 图像变换处理器
 * 负责裁剪、旋转、透视校正等图像变换操作
 */
@Singleton
class ImageTransformProcessor @Inject constructor() {
    
    /**
     * 应用裁剪变换
     */
    suspend fun applyCrop(
        inputPath: String,
        cropSettings: CropSettings,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            onProgress(0.1f)
            
            // 加载原始图像
            val originalBitmap = BitmapFactory.decodeFile(inputPath)
                ?: return@withContext Result.failure(Exception("Failed to load image: $inputPath"))
            
            onProgress(0.3f)
            
            // 应用裁剪
            val croppedBitmap = cropBitmap(originalBitmap, cropSettings)
            
            onProgress(0.8f)
            
            // 保存结果
            val success = saveBitmap(croppedBitmap, outputPath)
            
            // 清理资源
            if (croppedBitmap != originalBitmap) {
                croppedBitmap.recycle()
            }
            originalBitmap.recycle()
            
            onProgress(1.0f)
            
            if (success) {
                Result.success(outputPath)
            } else {
                Result.failure(Exception("Failed to save cropped image"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 应用旋转变换
     */
    suspend fun applyRotation(
        inputPath: String,
        rotationSettings: RotationSettings,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            onProgress(0.1f)
            
            // 加载原始图像
            val originalBitmap = BitmapFactory.decodeFile(inputPath)
                ?: return@withContext Result.failure(Exception("Failed to load image: $inputPath"))
            
            onProgress(0.3f)
            
            // 应用旋转
            val rotatedBitmap = rotateBitmap(originalBitmap, rotationSettings)
            
            onProgress(0.8f)
            
            // 保存结果
            val success = saveBitmap(rotatedBitmap, outputPath)
            
            // 清理资源
            if (rotatedBitmap != originalBitmap) {
                rotatedBitmap.recycle()
            }
            originalBitmap.recycle()
            
            onProgress(1.0f)
            
            if (success) {
                Result.success(outputPath)
            } else {
                Result.failure(Exception("Failed to save rotated image"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 应用透视校正
     */
    suspend fun applyPerspectiveCorrection(
        inputPath: String,
        perspectiveSettings: PerspectiveSettings,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            onProgress(0.1f)
            
            // 加载原始图像
            val originalBitmap = BitmapFactory.decodeFile(inputPath)
                ?: return@withContext Result.failure(Exception("Failed to load image: $inputPath"))
            
            onProgress(0.3f)
            
            // 应用透视校正
            val correctedBitmap = applyPerspectiveTransform(originalBitmap, perspectiveSettings)
            
            onProgress(0.8f)
            
            // 保存结果
            val success = saveBitmap(correctedBitmap, outputPath)
            
            // 清理资源
            if (correctedBitmap != originalBitmap) {
                correctedBitmap.recycle()
            }
            originalBitmap.recycle()
            
            onProgress(1.0f)
            
            if (success) {
                Result.success(outputPath)
            } else {
                Result.failure(Exception("Failed to save perspective corrected image"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 应用组合变换
     */
    suspend fun applyCombinedTransform(
        inputPath: String,
        cropSettings: CropSettings,
        rotationSettings: RotationSettings,
        perspectiveSettings: PerspectiveSettings?,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            onProgress(0.1f)
            
            // 加载原始图像
            var currentBitmap = BitmapFactory.decodeFile(inputPath)
                ?: return@withContext Result.failure(Exception("Failed to load image: $inputPath"))
            
            onProgress(0.2f)
            
            // 1. 先应用透视校正（如果需要）
            if (perspectiveSettings?.isEnabled == true && !perspectiveSettings.isRectangular()) {
                val perspectiveBitmap = applyPerspectiveTransform(currentBitmap, perspectiveSettings)
                if (perspectiveBitmap != currentBitmap) {
                    currentBitmap.recycle()
                    currentBitmap = perspectiveBitmap
                }
                onProgress(0.4f)
            }
            
            // 2. 应用旋转
            if (rotationSettings.hasTransformation()) {
                val rotatedBitmap = rotateBitmap(currentBitmap, rotationSettings)
                if (rotatedBitmap != currentBitmap) {
                    currentBitmap.recycle()
                    currentBitmap = rotatedBitmap
                }
                onProgress(0.6f)
            }
            
            // 3. 最后应用裁剪
            val finalBitmap = cropBitmap(currentBitmap, cropSettings)
            if (finalBitmap != currentBitmap) {
                currentBitmap.recycle()
            }
            
            onProgress(0.8f)
            
            // 保存结果
            val success = saveBitmap(finalBitmap, outputPath)
            finalBitmap.recycle()
            
            onProgress(1.0f)
            
            if (success) {
                Result.success(outputPath)
            } else {
                Result.failure(Exception("Failed to save transformed image"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 生成预览图像
     */
    suspend fun generatePreview(
        inputPath: String,
        cropSettings: CropSettings,
        rotationSettings: RotationSettings,
        perspectiveSettings: PerspectiveSettings?,
        previewSize: Int = 512
    ): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            // 加载并缩放原始图像
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(inputPath, options)
            
            val scaleFactor = maxOf(
                options.outWidth / previewSize,
                options.outHeight / previewSize
            ).coerceAtLeast(1)
            
            options.inJustDecodeBounds = false
            options.inSampleSize = scaleFactor
            
            var currentBitmap = BitmapFactory.decodeFile(inputPath, options)
                ?: return@withContext Result.failure(Exception("Failed to load image for preview"))
            
            // 应用变换（顺序同上）
            if (perspectiveSettings?.isEnabled == true && !perspectiveSettings.isRectangular()) {
                val perspectiveBitmap = applyPerspectiveTransform(currentBitmap, perspectiveSettings)
                if (perspectiveBitmap != currentBitmap) {
                    currentBitmap.recycle()
                    currentBitmap = perspectiveBitmap
                }
            }
            
            if (rotationSettings.hasTransformation()) {
                val rotatedBitmap = rotateBitmap(currentBitmap, rotationSettings)
                if (rotatedBitmap != currentBitmap) {
                    currentBitmap.recycle()
                    currentBitmap = rotatedBitmap
                }
            }
            
            val finalBitmap = cropBitmap(currentBitmap, cropSettings)
            if (finalBitmap != currentBitmap) {
                currentBitmap.recycle()
            }
            
            Result.success(finalBitmap)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 裁剪Bitmap
     */
    private fun cropBitmap(bitmap: Bitmap, cropSettings: CropSettings): Bitmap {
        val cropRect = cropSettings.cropRect
        
        // 计算实际裁剪区域
        val left = (cropRect.left * bitmap.width).toInt().coerceIn(0, bitmap.width)
        val top = (cropRect.top * bitmap.height).toInt().coerceIn(0, bitmap.height)
        val width = ((cropRect.right - cropRect.left) * bitmap.width).toInt()
            .coerceIn(1, bitmap.width - left)
        val height = ((cropRect.bottom - cropRect.top) * bitmap.height).toInt()
            .coerceIn(1, bitmap.height - top)
        
        return when (cropSettings.cropMode) {
            CropMode.NORMAL -> {
                Bitmap.createBitmap(bitmap, left, top, width, height)
            }
            CropMode.CIRCLE -> {
                createCircularCrop(bitmap, left, top, width, height)
            }
            CropMode.ROUNDED -> {
                createRoundedCrop(bitmap, left, top, width, height, 20f)
            }
            else -> {
                // 其他模式暂时使用普通裁剪
                Bitmap.createBitmap(bitmap, left, top, width, height)
            }
        }
    }
    
    /**
     * 旋转Bitmap
     */
    private fun rotateBitmap(bitmap: Bitmap, rotationSettings: RotationSettings): Bitmap {
        if (!rotationSettings.hasTransformation()) {
            return bitmap
        }
        
        val matrix = Matrix()
        val centerX = bitmap.width / 2f
        val centerY = bitmap.height / 2f
        
        // 应用翻转
        val scaleX = if (rotationSettings.flipHorizontal) -1f else 1f
        val scaleY = if (rotationSettings.flipVertical) -1f else 1f
        
        if (scaleX != 1f || scaleY != 1f) {
            matrix.postScale(scaleX, scaleY, centerX, centerY)
        }
        
        // 应用旋转
        if (rotationSettings.rotation != 0f) {
            matrix.postRotate(rotationSettings.rotation, centerX, centerY)
        }
        
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }
    
    /**
     * 应用透视变换
     */
    private fun applyPerspectiveTransform(
        bitmap: Bitmap,
        perspectiveSettings: PerspectiveSettings
    ): Bitmap {
        if (!perspectiveSettings.isEnabled || perspectiveSettings.isRectangular()) {
            return bitmap
        }
        
        val width = bitmap.width.toFloat()
        val height = bitmap.height.toFloat()
        
        val matrix = perspectiveSettings.getPerspectiveMatrix(width, height)
        
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    }
    
    /**
     * 创建圆形裁剪
     */
    private fun createCircularCrop(
        bitmap: Bitmap,
        left: Int,
        top: Int,
        width: Int,
        height: Int
    ): Bitmap {
        val size = minOf(width, height)
        val centerX = left + width / 2
        val centerY = top + height / 2
        val radius = size / 2f
        
        val output = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        
        // 绘制圆形遮罩
        canvas.drawCircle(size / 2f, size / 2f, radius, paint)
        
        // 应用源图像
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
        val srcRect = Rect(
            centerX - radius.toInt(),
            centerY - radius.toInt(),
            centerX + radius.toInt(),
            centerY + radius.toInt()
        )
        val dstRect = Rect(0, 0, size, size)
        canvas.drawBitmap(bitmap, srcRect, dstRect, paint)
        
        return output
    }
    
    /**
     * 创建圆角裁剪
     */
    private fun createRoundedCrop(
        bitmap: Bitmap,
        left: Int,
        top: Int,
        width: Int,
        height: Int,
        cornerRadius: Float
    ): Bitmap {
        val output = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)
        val paint = Paint(Paint.ANTI_ALIAS_FLAG)
        
        // 绘制圆角矩形遮罩
        val rect = RectF(0f, 0f, width.toFloat(), height.toFloat())
        canvas.drawRoundRect(rect, cornerRadius, cornerRadius, paint)
        
        // 应用源图像
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
        val srcRect = Rect(left, top, left + width, top + height)
        val dstRect = Rect(0, 0, width, height)
        canvas.drawBitmap(bitmap, srcRect, dstRect, paint)
        
        return output
    }
    
    /**
     * 保存Bitmap到文件
     */
    private fun saveBitmap(bitmap: Bitmap, outputPath: String): Boolean {
        return try {
            val outputFile = File(outputPath)
            outputFile.parentFile?.mkdirs()
            
            FileOutputStream(outputFile).use { outputStream ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 95, outputStream)
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取图像信息
     */
    suspend fun getImageInfo(imagePath: String): Result<ImageInfo> = withContext(Dispatchers.IO) {
        try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            
            val file = File(imagePath)
            val exif = try {
                ExifInterface(imagePath)
            } catch (e: Exception) {
                null
            }
            
            val imageInfo = ImageInfo(
                width = options.outWidth,
                height = options.outHeight,
                aspectRatio = options.outWidth.toFloat() / options.outHeight.toFloat(),
                orientation = exif?.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL) ?: 0,
                hasExif = exif != null,
                colorSpace = options.outColorSpace?.name,
                fileSize = file.length(),
                format = options.outMimeType ?: "unknown"
            )
            
            Result.success(imageInfo)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
