package com.qxyu.yucram.data.local.dao;

/**
 * 照片数据访问对象
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u0014\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\nH\'J\u0018\u0010\u000e\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00072\u0006\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u000e\u0010\u0016\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0018\u001a\u00020\u00102\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u0019\u001a\u00020\u00032\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00070\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u0016\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u001c\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010\u001f\u001a\u00020\u0014H\'J\u001c\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000b0\n2\u0006\u0010!\u001a\u00020\u0014H\'J\u0016\u0010\"\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006#"}, d2 = {"Lcom/qxyu/yucram/data/local/dao/PhotoDao;", "", "deleteAllPhotos", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deletePhoto", "photo", "Lcom/qxyu/yucram/data/local/entity/PhotoEntity;", "(Lcom/qxyu/yucram/data/local/entity/PhotoEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllPhotos", "Lkotlinx/coroutines/flow/Flow;", "", "getAllPhotosFlow", "getFavoritePhotos", "getPhotoById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPhotoByPath", "filePath", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPhotoCount", "", "insertPhoto", "insertPhotos", "photos", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isPhotoExists", "", "searchPhotos", "query", "searchPhotosByTag", "tag", "updatePhoto", "app_debug"})
@androidx.room.Dao()
public abstract interface PhotoDao {
    
    /**
     * 获取所有照片
     */
    @androidx.room.Query(value = "SELECT * FROM photos ORDER BY dateCreated DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.data.local.entity.PhotoEntity>> getAllPhotos();
    
    /**
     * 获取所有照片（Flow）
     */
    @androidx.room.Query(value = "SELECT * FROM photos ORDER BY dateCreated DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.data.local.entity.PhotoEntity>> getAllPhotosFlow();
    
    /**
     * 根据ID获取照片
     */
    @androidx.room.Query(value = "SELECT * FROM photos WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoById(long id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.data.local.entity.PhotoEntity> $completion);
    
    /**
     * 获取照片数量
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM photos")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * 插入照片
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPhoto(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.local.entity.PhotoEntity photo, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * 批量插入照片
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertPhotos(@org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.data.local.entity.PhotoEntity> photos, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 更新照片
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updatePhoto(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.local.entity.PhotoEntity photo, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 删除照片
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deletePhoto(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.local.entity.PhotoEntity photo, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * 获取收藏照片
     */
    @androidx.room.Query(value = "SELECT * FROM photos WHERE isFavorite = 1 ORDER BY dateCreated DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.data.local.entity.PhotoEntity>> getFavoritePhotos();
    
    /**
     * 根据标签搜索照片
     */
    @androidx.room.Query(value = "SELECT * FROM photos WHERE tags LIKE \'%\' || :tag || \'%\' ORDER BY dateCreated DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.data.local.entity.PhotoEntity>> searchPhotosByTag(@org.jetbrains.annotations.NotNull()
    java.lang.String tag);
    
    /**
     * 搜索照片（文件名）
     */
    @androidx.room.Query(value = "SELECT * FROM photos WHERE fileName LIKE \'%\' || :query || \'%\' ORDER BY dateCreated DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.qxyu.yucram.data.local.entity.PhotoEntity>> searchPhotos(@org.jetbrains.annotations.NotNull()
    java.lang.String query);
    
    /**
     * 根据文件路径查询照片
     */
    @androidx.room.Query(value = "SELECT * FROM photos WHERE filePath = :filePath")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPhotoByPath(@org.jetbrains.annotations.NotNull()
    java.lang.String filePath, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.qxyu.yucram.data.local.entity.PhotoEntity> $completion);
    
    /**
     * 检查文件是否已存在
     */
    @androidx.room.Query(value = "SELECT COUNT(*) > 0 FROM photos WHERE filePath = :filePath")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isPhotoExists(@org.jetbrains.annotations.NotNull()
    java.lang.String filePath, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    /**
     * 删除所有照片
     */
    @androidx.room.Query(value = "DELETE FROM photos")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllPhotos(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}