# 创建应用图标说明

由于无法直接创建PNG文件，请按以下步骤手动创建图标：

## 方法1：使用Android Studio的Image Asset Studio

1. 在Android Studio中右键点击 `app/src/main/res`
2. 选择 `New` → `Image Asset`
3. 选择 `Launcher Icons (Adaptive and Legacy)`
4. 设置图标：
   - Foreground Layer: 选择一个相机图标或文字"Y"
   - Background Layer: 设置颜色为 #FF6600 (橙色)
5. 点击 `Next` 然后 `Finish`

## 方法2：临时解决方案

暂时删除自定义图标配置，使用默认图标：

1. 删除 `app/src/main/res/mipmap-anydpi-v26/` 目录下的文件
2. 使用系统默认图标

## 方法3：下载现成图标

从以下网站下载相机图标：
- https://material.io/resources/icons/
- https://icons8.com/
- https://www.flaticon.com/

然后放置到对应的mipmap目录中。
