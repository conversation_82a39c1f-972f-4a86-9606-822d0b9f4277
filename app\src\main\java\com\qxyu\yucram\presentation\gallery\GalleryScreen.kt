package com.qxyu.yucram.presentation.gallery

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.gallery.components.*

/**
 * 图库页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GalleryScreen(
    onNavigateToPhotoDetail: (String) -> Unit = {},
    onNavigateToCamera: () -> Unit = {},
    onNavigateToAlbum: (String) -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: GalleryViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val photos by viewModel.photos.collectAsStateWithLifecycle()
    val albums by viewModel.albums.collectAsStateWithLifecycle()
    val selectionState by viewModel.selectionState.collectAsStateWithLifecycle()

    var showSortMenu by remember { mutableStateOf(false) }
    var showFilterMenu by remember { mutableStateOf(false) }
    Scaffold(
        topBar = {
            GalleryTopBar(
                title = if (selectionState.isSelectionMode) {
                    "${selectionState.selectedCount} 已选择"
                } else {
                    "图库"
                },
                isSelectionMode = selectionState.isSelectionMode,
                onSortClick = { showSortMenu = true },
                onFilterClick = { showFilterMenu = true },
                onSearchClick = { viewModel.toggleSearchMode() },
                onSelectAllClick = { viewModel.selectAllPhotos() },
                onClearSelectionClick = { viewModel.clearSelection() },
                onDeleteSelectedClick = { viewModel.deleteSelectedPhotos() }
            )
        },
        floatingActionButton = {
            if (!selectionState.isSelectionMode) {
                FloatingActionButton(
                    onClick = onNavigateToCamera,
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                ) {
                    Icon(
                        imageVector = Icons.Filled.CameraAlt,
                        contentDescription = "拍照"
                    )
                }
            }
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索栏
            if (uiState.isSearchMode) {
                GallerySearchBar(
                    query = uiState.searchQuery,
                    onQueryChange = viewModel::updateSearchQuery,
                    onClearClick = viewModel::clearSearch,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }

            // 显示模式切换
            if (!selectionState.isSelectionMode && !uiState.isSearchMode) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "${photos.size} 张照片",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    DisplayModeToggle(
                        currentMode = uiState.displayMode,
                        onModeChange = viewModel::updateDisplayMode
                    )
                }
            }

            // 主要内容
            Box(modifier = Modifier.fillMaxSize()) {
                when {
                    uiState.isLoading -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }

                    photos.isEmpty() -> {
                        EmptyGalleryState(
                            onNavigateToCamera = onNavigateToCamera
                        )
                    }

                    else -> {
                        PhotoGrid(
                            photos = photos,
                            displayMode = uiState.displayMode,
                            selectionState = selectionState,
                            onPhotoClick = onNavigateToPhotoDetail,
                            onPhotoLongClick = viewModel::enterSelectionMode,
                            onToggleSelection = viewModel::togglePhotoSelection,
                            onToggleFavorite = viewModel::toggleFavorite
                        )
                    }
                }
            }
        }

        // 排序菜单
        SortMenu(
            expanded = showSortMenu,
            onDismiss = { showSortMenu = false },
            currentSortOrder = uiState.sortOrder,
            onSortOrderChange = viewModel::updateSortOrder
        )

        // 错误提示
        uiState.error?.let { error ->
            LaunchedEffect(error) {
                // 显示错误消息
                viewModel.clearError()
            }
        }

        // 成功消息
        uiState.message?.let { message ->
            LaunchedEffect(message) {
                // 显示成功消息
                viewModel.clearMessage()
            }
        }
    }
}



/**
 * 照片网格项（未来实现）
 */
@Composable
private fun PhotoGridItem(
    photoId: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    // TODO: 实现照片网格项
}
