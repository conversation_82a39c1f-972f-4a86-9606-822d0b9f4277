package com.qxyu.yucram.domain.model;

/**
 * 边框样式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/domain/model/BorderStyle;", "", "(Ljava/lang/String;I)V", "CLASSIC", "MODERN", "VINTAGE", "ARTISTIC", "POLAROID", "FILM", "DIGITAL", "ELEGANT", "app_debug"})
public enum BorderStyle {
    /*public static final*/ CLASSIC /* = new CLASSIC() */,
    /*public static final*/ MODERN /* = new MODERN() */,
    /*public static final*/ VINTAGE /* = new VINTAGE() */,
    /*public static final*/ ARTISTIC /* = new ARTISTIC() */,
    /*public static final*/ POLAROID /* = new POLAROID() */,
    /*public static final*/ FILM /* = new FILM() */,
    /*public static final*/ DIGITAL /* = new DIGITAL() */,
    /*public static final*/ ELEGANT /* = new ELEGANT() */;
    
    BorderStyle() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.BorderStyle> getEntries() {
        return null;
    }
}