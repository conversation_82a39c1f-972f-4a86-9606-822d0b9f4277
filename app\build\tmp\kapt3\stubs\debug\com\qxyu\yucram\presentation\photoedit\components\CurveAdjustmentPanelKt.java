package com.qxyu.yucram.presentation.photoedit.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000`\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\u001a.\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001aB\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0018\u0010\u000e\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a0\u0010\u000f\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u00132\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a.\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\n2\u0012\u0010\u0016\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u001a8\u0010\u0017\u001a\u00020\u00182\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001dH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001f\u0010 \u001a\u001e\u0010!\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\"\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\nH\u0002\u001a\u0018\u0010#\u001a\u00020\u00032\u0006\u0010\"\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\nH\u0002\u001a&\u0010$\u001a\u00020\u00032\u0006\u0010\"\u001a\u00020\u00032\u0006\u0010\t\u001a\u00020\n2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020\r0\fH\u0002\u001a<\u0010&\u001a\u00020\u0001*\u00020\'2\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010(\u001a\u00020)2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001dH\u0002\u00f8\u0001\u0000\u00a2\u0006\u0004\b*\u0010+\u001a*\u0010,\u001a\u00020\u0001*\u00020\'2\u0006\u0010\t\u001a\u00020\n2\f\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010-\u001a\u00020\u0018H\u0002\u001a\u001c\u0010.\u001a\u00020\u0001*\u00020\'2\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u001dH\u0002\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006/"}, d2 = {"CurveAdjustmentPanel", "", "curveAdjustments", "Lcom/qxyu/yucram/domain/model/CurveAdjustments;", "onAdjustmentsChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "CurveEditor", "curveType", "Lcom/qxyu/yucram/presentation/photoedit/components/CurveType;", "curvePoints", "", "Lcom/qxyu/yucram/domain/model/CurvePoint;", "onCurveChanged", "CurveTypeButton", "isSelected", "", "onClick", "Lkotlin/Function0;", "CurveTypeSelector", "selectedCurve", "onCurveSelected", "findNearestPoint", "", "points", "offset", "Landroidx/compose/ui/geometry/Offset;", "canvasWidth", "", "canvasHeight", "findNearestPoint-ubNVwUQ", "(Ljava/util/List;JFF)I", "getCurrentCurve", "adjustments", "resetCurve", "updateCurve", "newPoints", "drawCurve", "Landroidx/compose/ui/graphics/drawscope/DrawScope;", "color", "Landroidx/compose/ui/graphics/Color;", "drawCurve-XO-JAsU", "(Landroidx/compose/ui/graphics/drawscope/DrawScope;Ljava/util/List;JFF)V", "drawCurveEditor", "draggedPointIndex", "drawGrid", "app_debug"})
public final class CurveAdjustmentPanelKt {
    
    /**
     * 曲线调整面板
     */
    @androidx.compose.runtime.Composable()
    public static final void CurveAdjustmentPanel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.CurveAdjustments curveAdjustments, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.domain.model.CurveAdjustments, kotlin.Unit> onAdjustmentsChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 曲线类型选择器
     */
    @androidx.compose.runtime.Composable()
    public static final void CurveTypeSelector(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.CurveType selectedCurve, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.qxyu.yucram.presentation.photoedit.components.CurveType, kotlin.Unit> onCurveSelected, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 曲线类型按钮
     */
    @androidx.compose.runtime.Composable()
    public static final void CurveTypeButton(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.CurveType curveType, boolean isSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onClick, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 曲线编辑器
     */
    @androidx.compose.runtime.Composable()
    public static final void CurveEditor(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.presentation.photoedit.components.CurveType curveType, @org.jetbrains.annotations.NotNull()
    java.util.List<com.qxyu.yucram.domain.model.CurvePoint> curvePoints, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.util.List<com.qxyu.yucram.domain.model.CurvePoint>, kotlin.Unit> onCurveChanged, @org.jetbrains.annotations.NotNull()
    androidx.compose.ui.Modifier modifier) {
    }
    
    /**
     * 绘制曲线编辑器
     */
    private static final void drawCurveEditor(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawCurveEditor, com.qxyu.yucram.presentation.photoedit.components.CurveType curveType, java.util.List<com.qxyu.yucram.domain.model.CurvePoint> points, int draggedPointIndex) {
    }
    
    /**
     * 绘制网格
     */
    private static final void drawGrid(androidx.compose.ui.graphics.drawscope.DrawScope $this$drawGrid, float canvasWidth, float canvasHeight) {
    }
    
    /**
     * 获取当前曲线
     */
    private static final java.util.List<com.qxyu.yucram.domain.model.CurvePoint> getCurrentCurve(com.qxyu.yucram.domain.model.CurveAdjustments adjustments, com.qxyu.yucram.presentation.photoedit.components.CurveType curveType) {
        return null;
    }
    
    /**
     * 更新曲线
     */
    private static final com.qxyu.yucram.domain.model.CurveAdjustments updateCurve(com.qxyu.yucram.domain.model.CurveAdjustments adjustments, com.qxyu.yucram.presentation.photoedit.components.CurveType curveType, java.util.List<com.qxyu.yucram.domain.model.CurvePoint> newPoints) {
        return null;
    }
    
    /**
     * 重置曲线
     */
    private static final com.qxyu.yucram.domain.model.CurveAdjustments resetCurve(com.qxyu.yucram.domain.model.CurveAdjustments adjustments, com.qxyu.yucram.presentation.photoedit.components.CurveType curveType) {
        return null;
    }
}