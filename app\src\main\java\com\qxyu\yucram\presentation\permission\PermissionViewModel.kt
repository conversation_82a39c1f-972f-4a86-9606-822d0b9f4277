package com.qxyu.yucram.presentation.permission

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.utils.PermissionUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 权限管理ViewModel
 */
@HiltViewModel
class PermissionViewModel @Inject constructor(
    application: Application
) : AndroidViewModel(application) {
    
    private val context = application.applicationContext
    
    private val _permissionState = MutableStateFlow(PermissionState())
    val permissionState: StateFlow<PermissionState> = _permissionState.asStateFlow()
    
    init {
        checkPermissions()
    }
    
    /**
     * 检查所有权限状态
     */
    fun checkPermissions() {
        viewModelScope.launch {
            val cameraGranted = PermissionUtils.isCameraPermissionGranted(context)
            val storageGranted = PermissionUtils.isStoragePermissionGranted(context)
            val locationGranted = PermissionUtils.isLocationPermissionGranted(context)
            val recordAudioGranted = PermissionUtils.isRecordAudioPermissionGranted(context)
            
            _permissionState.value = PermissionState(
                cameraPermissionGranted = cameraGranted,
                storagePermissionGranted = storageGranted,
                locationPermissionGranted = locationGranted,
                recordAudioPermissionGranted = recordAudioGranted,
                allCorePermissionsGranted = cameraGranted && storageGranted
            )
        }
    }
    
    /**
     * 权限请求完成后的回调
     */
    fun onPermissionResult() {
        checkPermissions()
    }
    
    /**
     * 检查是否可以使用相机
     */
    fun canUseCamera(): Boolean {
        return _permissionState.value.allCorePermissionsGranted
    }
}

/**
 * 权限状态数据类
 */
data class PermissionState(
    val cameraPermissionGranted: Boolean = false,
    val storagePermissionGranted: Boolean = false,
    val locationPermissionGranted: Boolean = false,
    val recordAudioPermissionGranted: Boolean = false,
    val allCorePermissionsGranted: Boolean = false,
    val isLoading: Boolean = false
)
