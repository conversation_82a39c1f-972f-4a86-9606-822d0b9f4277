package com.qxyu.yucram.presentation.camera

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qxyu.yucram.camera.CameraManager
import com.qxyu.yucram.camera.CameraState
import com.qxyu.yucram.domain.model.AspectRatio
import com.qxyu.yucram.domain.model.CameraSettings
import com.qxyu.yucram.domain.model.FlashMode
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 相机界面ViewModel
 */
@HiltViewModel
class CameraViewModel @Inject constructor(
    private val cameraManager: CameraManager
) : ViewModel() {
    
    // 相机状态
    val cameraState: StateFlow<CameraState> = cameraManager.cameraState
    
    // 相机设置
    private val _cameraSettings = MutableStateFlow(CameraSettings())
    val cameraSettings: StateFlow<CameraSettings> = _cameraSettings.asStateFlow()
    
    // UI状态
    private val _uiState = MutableStateFlow(CameraUiState())
    val uiState: StateFlow<CameraUiState> = _uiState.asStateFlow()
    
    init {
        // 监听相机状态变化
        viewModelScope.launch {
            cameraState.collect { state ->
                _uiState.value = _uiState.value.copy(
                    isPreviewReady = state == CameraState.PREVIEW,
                    isLoading = state == CameraState.INITIALIZING || state == CameraState.OPENING,
                    hasError = state == CameraState.ERROR
                )
            }
        }
    }
    
    /**
     * 拍摄照片
     */
    fun capturePhoto() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isCapturing = true)
            
            try {
                cameraManager.capturePhoto()
                // 这里可以添加拍摄成功的处理逻辑
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(hasError = true)
            } finally {
                _uiState.value = _uiState.value.copy(isCapturing = false)
            }
        }
    }
    
    /**
     * 切换闪光灯模式
     */
    fun toggleFlashMode() {
        val currentMode = _cameraSettings.value.flashMode
        val newMode = when (currentMode) {
            FlashMode.OFF -> FlashMode.AUTO
            FlashMode.AUTO -> FlashMode.ON
            FlashMode.ON -> FlashMode.OFF
            FlashMode.TORCH -> FlashMode.OFF
        }

        _cameraSettings.value = _cameraSettings.value.copy(flashMode = newMode)
        // 应用到相机管理器
        cameraManager.setFlashMode(newMode)
    }
    
    /**
     * 切换画幅比例
     */
    fun setAspectRatio(aspectRatio: AspectRatio) {
        _cameraSettings.value = _cameraSettings.value.copy(aspectRatio = aspectRatio)
    }
    
    /**
     * 切换网格线显示
     */
    fun toggleGridLines() {
        _cameraSettings.value = _cameraSettings.value.copy(
            isGridEnabled = !_cameraSettings.value.isGridEnabled
        )
    }
    
    /**
     * 切换水平仪显示
     */
    fun toggleLevel() {
        _cameraSettings.value = _cameraSettings.value.copy(
            isLevelEnabled = !_cameraSettings.value.isLevelEnabled
        )
    }
    
    /**
     * 设置定时器
     */
    fun setTimer(seconds: Int) {
        _cameraSettings.value = _cameraSettings.value.copy(timerDuration = seconds)
    }
    
    /**
     * 切换RAW格式
     */
    fun toggleRawFormat() {
        val newRawEnabled = !_cameraSettings.value.isRawEnabled
        _cameraSettings.value = _cameraSettings.value.copy(isRawEnabled = newRawEnabled)
        // 应用到相机管理器
        cameraManager.setRawEnabled(newRawEnabled)
    }

    /**
     * 切换HDR模式
     */
    fun toggleHdrMode() {
        val newHdrEnabled = !_cameraSettings.value.isHdrEnabled
        _cameraSettings.value = _cameraSettings.value.copy(isHdrEnabled = newHdrEnabled)
        // 应用到相机管理器
        cameraManager.setHdrEnabled(newHdrEnabled)
    }
    
    /**
     * 获取可用相机列表
     */
    fun getAvailableCameras(): List<String> {
        return cameraManager.getAvailableCameras()
    }
    
    /**
     * 切换相机
     */
    fun switchCamera(cameraId: String) {
        viewModelScope.launch {
            cameraManager.closeCamera()
            cameraManager.openCamera(cameraId)
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        cameraManager.closeCamera()
    }
}

/**
 * 相机UI状态
 */
data class CameraUiState(
    val isPreviewReady: Boolean = false,
    val isCapturing: Boolean = false,
    val isLoading: Boolean = false,
    val hasError: Boolean = false,
    val errorMessage: String? = null
)
