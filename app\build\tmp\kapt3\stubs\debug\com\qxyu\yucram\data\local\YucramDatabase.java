package com.qxyu.yucram.data.local;

/**
 * Yucram应用程序数据库
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&\u00a8\u0006\u0007"}, d2 = {"Lcom/qxyu/yucram/data/local/YucramDatabase;", "Landroidx/room/RoomDatabase;", "()V", "photoDao", "Lcom/qxyu/yucram/data/local/dao/PhotoDao;", "settingsDao", "Lcom/qxyu/yucram/data/local/dao/SettingsDao;", "app_debug"})
@androidx.room.Database(entities = {com.qxyu.yucram.data.local.entity.PhotoEntity.class, com.qxyu.yucram.data.local.entity.SettingsEntity.class}, version = 1, exportSchema = false)
public abstract class YucramDatabase extends androidx.room.RoomDatabase {
    
    public YucramDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.qxyu.yucram.data.local.dao.PhotoDao photoDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.qxyu.yucram.data.local.dao.SettingsDao settingsDao();
}