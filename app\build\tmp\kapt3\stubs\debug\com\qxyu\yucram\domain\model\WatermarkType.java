package com.qxyu.yucram.domain.model;

/**
 * 水印类型
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/qxyu/yucram/domain/model/WatermarkType;", "", "(Ljava/lang/String;I)V", "TEXT", "IMAGE", "LOGO", "SIGNATURE", "TIMESTAMP", "LOCATION", "CAMERA_INFO", "CUSTOM", "app_debug"})
public enum WatermarkType {
    /*public static final*/ TEXT /* = new TEXT() */,
    /*public static final*/ IMAGE /* = new IMAGE() */,
    /*public static final*/ LOGO /* = new LOGO() */,
    /*public static final*/ SIGNATURE /* = new SIGNATURE() */,
    /*public static final*/ TIMESTAMP /* = new TIMESTAMP() */,
    /*public static final*/ LOCATION /* = new LOCATION() */,
    /*public static final*/ CAMERA_INFO /* = new CAMERA_INFO() */,
    /*public static final*/ CUSTOM /* = new CUSTOM() */;
    
    WatermarkType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.WatermarkType> getEntries() {
        return null;
    }
}