package com.qxyu.yucram.di;

/**
 * 滤镜系统依赖注入模块
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0007J\u0012\u0010\u0007\u001a\u00020\b2\b\b\u0001\u0010\t\u001a\u00020\nH\u0007J2\u0010\u000b\u001a\u00020\f2\b\b\u0001\u0010\t\u001a\u00020\n2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\u0007J\u0012\u0010\u0014\u001a\u00020\u00152\b\b\u0001\u0010\t\u001a\u00020\nH\u0007J\u0012\u0010\u0016\u001a\u00020\u000e2\b\b\u0001\u0010\t\u001a\u00020\nH\u0007J\u0010\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0007J\u0012\u0010\u001b\u001a\u00020\u00132\b\b\u0001\u0010\t\u001a\u00020\nH\u0007J\u0012\u0010\u001c\u001a\u00020\u00112\b\b\u0001\u0010\t\u001a\u00020\nH\u0007\u00a8\u0006\u001d"}, d2 = {"Lcom/qxyu/yucram/di/FilterSystemModule;", "", "()V", "provideAlbumRepository", "Lcom/qxyu/yucram/domain/repository/AlbumRepository;", "albumRepositoryImpl", "Lcom/qxyu/yucram/data/repository/AlbumRepositoryImpl;", "provideDeviceCapabilityDetector", "Lcom/qxyu/yucram/filter/DeviceCapabilityDetector;", "context", "Landroid/content/Context;", "provideImageProcessingPipeline", "Lcom/qxyu/yucram/filter/ImageProcessingPipeline;", "lutProcessor", "Lcom/qxyu/yucram/film/LutProcessor;", "deviceCapabilityDetector", "rawToLogProcessor", "Lcom/qxyu/yucram/processing/RawToLogProcessor;", "rawCaptureManager", "Lcom/qxyu/yucram/camera/RawCaptureManager;", "provideLutFileManager", "Lcom/qxyu/yucram/filter/LutFileManager;", "provideLutProcessor", "providePhotoRepository", "Lcom/qxyu/yucram/domain/repository/PhotoRepository;", "photoRepositoryImpl", "Lcom/qxyu/yucram/data/repository/PhotoRepositoryImpl;", "provideRawCaptureManager", "provideRawToLogProcessor", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class FilterSystemModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.qxyu.yucram.di.FilterSystemModule INSTANCE = null;
    
    private FilterSystemModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.filter.DeviceCapabilityDetector provideDeviceCapabilityDetector(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.filter.LutFileManager provideLutFileManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.film.LutProcessor provideLutProcessor(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.processing.RawToLogProcessor provideRawToLogProcessor(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.camera.RawCaptureManager provideRawCaptureManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.repository.PhotoRepository providePhotoRepository(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.repository.PhotoRepositoryImpl photoRepositoryImpl) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.repository.AlbumRepository provideAlbumRepository(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.data.repository.AlbumRepositoryImpl albumRepositoryImpl) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.filter.ImageProcessingPipeline provideImageProcessingPipeline(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.film.LutProcessor lutProcessor, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.filter.DeviceCapabilityDetector deviceCapabilityDetector, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.processing.RawToLogProcessor rawToLogProcessor, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.camera.RawCaptureManager rawCaptureManager) {
        return null;
    }
}