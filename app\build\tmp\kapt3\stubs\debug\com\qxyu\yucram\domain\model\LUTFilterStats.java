package com.qxyu.yucram.domain.model;

/**
 * LUT滤镜统计
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u001d\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BU\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\b\b\u0002\u0010\f\u001a\u00020\u0007\u0012\b\b\u0002\u0010\r\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0007H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\nH\u00c6\u0003J\u0010\u0010\"\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0017J\t\u0010#\u001a\u00020\u0007H\u00c6\u0003J\t\u0010$\u001a\u00020\u0007H\u00c6\u0003J`\u0010%\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010\f\u001a\u00020\u00072\b\b\u0002\u0010\r\u001a\u00020\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010&J\u0013\u0010\'\u001a\u00020(2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020\u0005H\u00d6\u0001J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\r\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0015\u0010\u000b\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u0010\u0018\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0012R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001b\u00a8\u0006,"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTFilterStats;", "", "filterId", "", "usageCount", "", "averageRating", "", "totalRatings", "averageProcessingTime", "", "lastUsed", "popularityScore", "effectivenessScore", "(Ljava/lang/String;IFIJLjava/lang/Long;FF)V", "getAverageProcessingTime", "()J", "getAverageRating", "()F", "getEffectivenessScore", "getFilterId", "()Ljava/lang/String;", "getLastUsed", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getPopularityScore", "getTotalRatings", "()I", "getUsageCount", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Ljava/lang/String;IFIJLjava/lang/Long;FF)Lcom/qxyu/yucram/domain/model/LUTFilterStats;", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class LUTFilterStats {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String filterId = null;
    private final int usageCount = 0;
    private final float averageRating = 0.0F;
    private final int totalRatings = 0;
    private final long averageProcessingTime = 0L;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long lastUsed = null;
    private final float popularityScore = 0.0F;
    private final float effectivenessScore = 0.0F;
    
    public LUTFilterStats(@org.jetbrains.annotations.NotNull()
    java.lang.String filterId, int usageCount, float averageRating, int totalRatings, long averageProcessingTime, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastUsed, float popularityScore, float effectivenessScore) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFilterId() {
        return null;
    }
    
    public final int getUsageCount() {
        return 0;
    }
    
    public final float getAverageRating() {
        return 0.0F;
    }
    
    public final int getTotalRatings() {
        return 0;
    }
    
    public final long getAverageProcessingTime() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getLastUsed() {
        return null;
    }
    
    public final float getPopularityScore() {
        return 0.0F;
    }
    
    public final float getEffectivenessScore() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final long component5() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component6() {
        return null;
    }
    
    public final float component7() {
        return 0.0F;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFilterStats copy(@org.jetbrains.annotations.NotNull()
    java.lang.String filterId, int usageCount, float averageRating, int totalRatings, long averageProcessingTime, @org.jetbrains.annotations.Nullable()
    java.lang.Long lastUsed, float popularityScore, float effectivenessScore) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}