package com.qxyu.yucram.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.qxyu.yucram.domain.model.*

/**
 * 裁剪预设数据库实体
 */
@Entity(tableName = "crop_presets")
data class CropPresetEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val description: String,
    val category: String,
    val isBuiltIn: Boolean,
    val icon: String?,
    val sortOrder: Int,
    
    // 裁剪设置
    val cropLeft: Float,
    val cropTop: Float,
    val cropRight: Float,
    val cropBottom: Float,
    val aspectRatio: String,
    val customAspectRatio: Float?,
    val showGrid: Boolean,
    val gridType: String,
    val cropMode: String,
    val maintainAspectRatio: Boolean,
    val snapToGrid: Boolean,
    val gridOpacity: Float,
    val backgroundColor: Int,
    val enableSmartGuides: Boolean,
    val autoCenter: Boolean
) {
    companion object {
        fun fromDomainModel(preset: CropPreset): CropPresetEntity {
            return CropPresetEntity(
                id = preset.id,
                name = preset.name,
                description = preset.description,
                category = preset.category.name,
                isBuiltIn = preset.isBuiltIn,
                icon = preset.icon,
                sortOrder = preset.sortOrder,
                cropLeft = preset.cropSettings.cropRect.left,
                cropTop = preset.cropSettings.cropRect.top,
                cropRight = preset.cropSettings.cropRect.right,
                cropBottom = preset.cropSettings.cropRect.bottom,
                aspectRatio = preset.cropSettings.aspectRatio.name,
                customAspectRatio = preset.cropSettings.customAspectRatio,
                showGrid = preset.cropSettings.showGrid,
                gridType = preset.cropSettings.gridType.name,
                cropMode = preset.cropSettings.cropMode.name,
                maintainAspectRatio = preset.cropSettings.maintainAspectRatio,
                snapToGrid = preset.cropSettings.snapToGrid,
                gridOpacity = preset.cropSettings.gridOpacity,
                backgroundColor = preset.cropSettings.backgroundColor,
                enableSmartGuides = preset.cropSettings.enableSmartGuides,
                autoCenter = preset.cropSettings.autoCenter
            )
        }
    }
    
    fun toDomainModel(): CropPreset {
        return CropPreset(
            id = id,
            name = name,
            description = description,
            cropSettings = CropSettings(
                cropRect = CropRect(cropLeft, cropTop, cropRight, cropBottom),
                aspectRatio = AspectRatio.valueOf(aspectRatio),
                customAspectRatio = customAspectRatio,
                showGrid = showGrid,
                gridType = GridType.valueOf(gridType),
                cropMode = CropMode.valueOf(cropMode),
                maintainAspectRatio = maintainAspectRatio,
                snapToGrid = snapToGrid,
                gridOpacity = gridOpacity,
                backgroundColor = backgroundColor,
                enableSmartGuides = enableSmartGuides,
                autoCenter = autoCenter
            ),
            category = CropPresetCategory.valueOf(category),
            isBuiltIn = isBuiltIn,
            icon = icon,
            sortOrder = sortOrder
        )
    }
}
