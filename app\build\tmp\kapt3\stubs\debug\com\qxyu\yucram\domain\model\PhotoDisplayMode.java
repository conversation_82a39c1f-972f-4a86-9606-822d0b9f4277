package com.qxyu.yucram.domain.model;

/**
 * 照片显示模式
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/qxyu/yucram/domain/model/PhotoDisplayMode;", "", "(Ljava/lang/String;I)V", "GRID_SMALL", "GRID_MEDIUM", "GRID_LARGE", "LIST", "app_debug"})
public enum PhotoDisplayMode {
    /*public static final*/ GRID_SMALL /* = new GRID_SMALL() */,
    /*public static final*/ GRID_MEDIUM /* = new GRID_MEDIUM() */,
    /*public static final*/ GRID_LARGE /* = new GRID_LARGE() */,
    /*public static final*/ LIST /* = new LIST() */;
    
    PhotoDisplayMode() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.qxyu.yucram.domain.model.PhotoDisplayMode> getEntries() {
        return null;
    }
}