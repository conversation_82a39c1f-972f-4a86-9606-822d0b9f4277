package com.qxyu.yucram.domain.model;

/**
 * 局部调整
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B9\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\tH\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u000bH\u00c6\u0003J\t\u0010 \u001a\u00020\rH\u00c6\u0003JE\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\rH\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001a\u00a8\u0006("}, d2 = {"Lcom/qxyu/yucram/domain/model/LocalAdjustment;", "", "id", "", "type", "Lcom/qxyu/yucram/domain/model/LocalAdjustmentType;", "area", "Lcom/qxyu/yucram/domain/model/AdjustmentArea;", "params", "Lcom/qxyu/yucram/domain/model/PhotoEditParams;", "opacity", "", "blendMode", "Lcom/qxyu/yucram/domain/model/BlendMode;", "(Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LocalAdjustmentType;Lcom/qxyu/yucram/domain/model/AdjustmentArea;Lcom/qxyu/yucram/domain/model/PhotoEditParams;FLcom/qxyu/yucram/domain/model/BlendMode;)V", "getArea", "()Lcom/qxyu/yucram/domain/model/AdjustmentArea;", "getBlendMode", "()Lcom/qxyu/yucram/domain/model/BlendMode;", "getId", "()Ljava/lang/String;", "getOpacity", "()F", "getParams", "()Lcom/qxyu/yucram/domain/model/PhotoEditParams;", "getType", "()Lcom/qxyu/yucram/domain/model/LocalAdjustmentType;", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class LocalAdjustment {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LocalAdjustmentType type = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.AdjustmentArea area = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.PhotoEditParams params = null;
    private final float opacity = 0.0F;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.BlendMode blendMode = null;
    
    public LocalAdjustment(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LocalAdjustmentType type, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.AdjustmentArea area, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams params, float opacity, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BlendMode blendMode) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LocalAdjustmentType getType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.AdjustmentArea getArea() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoEditParams getParams() {
        return null;
    }
    
    public final float getOpacity() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BlendMode getBlendMode() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LocalAdjustmentType component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.AdjustmentArea component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.PhotoEditParams component4() {
        return null;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.BlendMode component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LocalAdjustment copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LocalAdjustmentType type, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.AdjustmentArea area, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.PhotoEditParams params, float opacity, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.BlendMode blendMode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}