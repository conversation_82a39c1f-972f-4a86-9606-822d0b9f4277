package com.qxyu.yucram.presentation.photodetail.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.*
import java.time.format.DateTimeFormatter

/**
 * EXIF信息底部表单
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ExifInfoBottomSheet(
    photo: Photo,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // 标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "照片信息",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold
                )
                
                IconButton(onClick = onDismiss) {
                    Icon(
                        imageVector = Icons.Filled.Close,
                        contentDescription = "关闭"
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // 信息列表
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.fillMaxWidth()
            ) {
                // 基本信息
                item {
                    ExifSection(
                        title = "基本信息",
                        icon = Icons.Filled.Info
                    ) {
                        ExifInfoItem("文件名", photo.fileName)
                        ExifInfoItem("文件大小", formatFileSize(photo.fileSize))
                        ExifInfoItem("尺寸", "${photo.width} × ${photo.height}")
                        ExifInfoItem("格式", photo.mimeType)
                        photo.dateTaken?.let {
                            ExifInfoItem("拍摄时间", it.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                        }
                        ExifInfoItem("添加时间", photo.dateAdded.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    }
                }
                
                // 相机信息
                photo.exifData?.let { exif ->
                    item {
                        ExifSection(
                            title = "相机信息",
                            icon = Icons.Filled.CameraAlt
                        ) {
                            exif.cameraMake?.let { ExifInfoItem("相机品牌", it) }
                            exif.cameraModel?.let { ExifInfoItem("相机型号", it) }
                            exif.lensModel?.let { ExifInfoItem("镜头型号", it) }
                            exif.software?.let { ExifInfoItem("软件", it) }
                        }
                    }
                }
                
                // 拍摄参数
                photo.exifData?.let { exif ->
                    item {
                        ExifSection(
                            title = "拍摄参数",
                            icon = Icons.Filled.Settings
                        ) {
                            exif.focalLength?.let { ExifInfoItem("焦距", "${it}mm") }
                            exif.aperture?.let { ExifInfoItem("光圈", "f/${it}") }
                            exif.shutterSpeed?.let { ExifInfoItem("快门速度", it) }
                            exif.iso?.let { ExifInfoItem("ISO", it.toString()) }
                            exif.flash?.let { ExifInfoItem("闪光灯", if (it) "开启" else "关闭") }
                            exif.whiteBalance?.let { ExifInfoItem("白平衡", it) }
                            exif.exposureMode?.let { ExifInfoItem("曝光模式", it) }
                            exif.meteringMode?.let { ExifInfoItem("测光模式", it) }
                            exif.colorSpace?.let { ExifInfoItem("色彩空间", it) }
                        }
                    }
                }
                
                // 位置信息
                photo.location?.let { location ->
                    item {
                        ExifSection(
                            title = "位置信息",
                            icon = Icons.Filled.LocationOn
                        ) {
                            ExifInfoItem("纬度", String.format("%.6f", location.latitude))
                            ExifInfoItem("经度", String.format("%.6f", location.longitude))
                            location.altitude?.let { ExifInfoItem("海拔", "${it}m") }
                            location.address?.let { ExifInfoItem("地址", it) }
                            location.city?.let { ExifInfoItem("城市", it) }
                            location.country?.let { ExifInfoItem("国家", it) }
                        }
                    }
                }
                
                // Yucram处理信息
                photo.processingInfo?.let { processing ->
                    item {
                        ExifSection(
                            title = "Yucram处理",
                            icon = Icons.Filled.AutoFixHigh
                        ) {
                            ExifInfoItem("源格式", processing.sourceFormat.name)
                            ExifInfoItem("RAW处理", if (processing.isRawProcessed) "是" else "否")
                            ExifInfoItem("LOG处理", if (processing.isLogProcessed) "是" else "否")
                            processing.lutApplied?.let { ExifInfoItem("应用滤镜", it) }
                            processing.processingTime?.let { ExifInfoItem("处理时间", "${it}ms") }
                            processing.processingVersion?.let { ExifInfoItem("处理版本", it) }
                        }
                    }
                }
                
                // 调整参数
                photo.processingInfo?.adjustments?.let { adjustments ->
                    item {
                        ExifSection(
                            title = "调整参数",
                            icon = Icons.Filled.Tune
                        ) {
                            if (adjustments.exposure != 0f) ExifInfoItem("曝光", formatAdjustment(adjustments.exposure))
                            if (adjustments.highlights != 0f) ExifInfoItem("高光", formatAdjustment(adjustments.highlights))
                            if (adjustments.shadows != 0f) ExifInfoItem("阴影", formatAdjustment(adjustments.shadows))
                            if (adjustments.contrast != 0f) ExifInfoItem("对比度", formatAdjustment(adjustments.contrast))
                            if (adjustments.brightness != 0f) ExifInfoItem("亮度", formatAdjustment(adjustments.brightness))
                            if (adjustments.saturation != 0f) ExifInfoItem("饱和度", formatAdjustment(adjustments.saturation))
                            if (adjustments.vibrance != 0f) ExifInfoItem("自然饱和度", formatAdjustment(adjustments.vibrance))
                            if (adjustments.temperature != 0f) ExifInfoItem("色温", formatAdjustment(adjustments.temperature))
                            if (adjustments.tint != 0f) ExifInfoItem("色调", formatAdjustment(adjustments.tint))
                            if (adjustments.sharpness != 0f) ExifInfoItem("锐度", formatAdjustment(adjustments.sharpness))
                            if (adjustments.noise != 0f) ExifInfoItem("降噪", formatAdjustment(adjustments.noise))
                            if (adjustments.vignette != 0f) ExifInfoItem("暗角", formatAdjustment(adjustments.vignette))
                            if (adjustments.grain != 0f) ExifInfoItem("颗粒", formatAdjustment(adjustments.grain))
                        }
                    }
                }
                
                // 标签
                if (photo.tags.isNotEmpty()) {
                    item {
                        ExifSection(
                            title = "标签",
                            icon = Icons.Filled.Label
                        ) {
                            ExifInfoItem("标签", photo.tags.joinToString(", "))
                        }
                    }
                }
                
                // 底部间距
                item {
                    Spacer(modifier = Modifier.height(32.dp))
                }
            }
        }
    }
}

/**
 * EXIF信息分组
 */
@Composable
fun ExifSection(
    title: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 12.dp)
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
            
            content()
        }
    }
}

/**
 * EXIF信息项
 */
@Composable
fun ExifInfoItem(
    label: String,
    value: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.weight(1f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 格式化文件大小
 */
private fun formatFileSize(bytes: Long): String {
    val kb = bytes / 1024.0
    val mb = kb / 1024.0
    val gb = mb / 1024.0
    
    return when {
        gb >= 1 -> String.format("%.1f GB", gb)
        mb >= 1 -> String.format("%.1f MB", mb)
        kb >= 1 -> String.format("%.1f KB", kb)
        else -> "$bytes B"
    }
}

/**
 * 格式化调整参数
 */
private fun formatAdjustment(value: Float): String {
    return when {
        value > 0 -> "+${String.format("%.1f", value)}"
        value < 0 -> String.format("%.1f", value)
        else -> "0"
    }
}
