package com.qxyu.yucram.presentation.photoedit.components

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.borderwater.BorderWatermarkTab
import com.qxyu.yucram.presentation.borderwater.components.BorderWatermarkEditPanel
import com.qxyu.yucram.presentation.lutfilter.components.LUTFilterControlPanel

/**
 * 编辑工具面板
 */
@Composable
fun EditToolPanel(
    tool: EditTool,
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    when (tool) {
        EditTool.EXPOSURE -> {
            ExposureAdjustmentPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.HIGHLIGHTS_SHADOWS -> {
            HighlightsShadowsPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.WHITES_BLACKS -> {
            WhitesBlacksPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.CONTRAST_BRIGHTNESS -> {
            ContrastBrightnessPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.VIBRANCE_SATURATION -> {
            VibranceSaturationPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.TEMPERATURE_TINT -> {
            TemperatureTintPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.HSL -> {
            HSLAdjustmentPanel(
                hslAdjustments = editParams.hslAdjustments,
                onAdjustmentsChanged = { newHsl ->
                    onParamsChanged(editParams.copy(hslAdjustments = newHsl))
                },
                modifier = modifier
            )
        }
        
        EditTool.CURVES -> {
            CurveAdjustmentPanel(
                curveAdjustments = editParams.curves,
                onAdjustmentsChanged = { newCurves ->
                    onParamsChanged(editParams.copy(curves = newCurves))
                },
                modifier = modifier
            )
        }
        
        EditTool.SHARPNESS -> {
            SharpnessPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.NOISE_REDUCTION -> {
            NoiseReductionPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.CLARITY_DEHAZE -> {
            ClarityDehazePanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.VIGNETTE -> {
            VignettePanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.GRAIN -> {
            GrainPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.FILM_EMULATION -> {
            FilmEmulationPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.CROP_ROTATE -> {
            CropRotatePanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.PERSPECTIVE -> {
            PerspectivePanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.RADIAL_FILTER -> {
            RadialFilterPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.GRADUATED_FILTER -> {
            GraduatedFilterPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.MASKING -> {
            MaskingPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.BRUSH -> {
            BrushPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
        
        EditTool.LUT_FILTERS -> {
            LUTFiltersPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }

        EditTool.BORDER_WATERMARK -> {
            BorderWatermarkToolPanel(
                editParams = editParams,
                onParamsChanged = onParamsChanged,
                modifier = modifier
            )
        }
    }
}

/**
 * 曝光调整面板
 */
@Composable
fun ExposureAdjustmentPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    SingleSliderPanel(
        title = "曝光",
        value = editParams.exposure,
        valueRange = -2f..2f,
        onValueChanged = { value ->
            onParamsChanged(editParams.copy(exposure = value))
        },
        formatValue = { value ->
            when {
                value > 0 -> "+${String.format("%.1f", value)}"
                value < 0 -> String.format("%.1f", value)
                else -> "0"
            }
        },
        modifier = modifier
    )
}

/**
 * 高光阴影面板
 */
@Composable
fun HighlightsShadowsPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    DualSliderPanel(
        title = "高光阴影",
        value1 = editParams.highlights,
        label1 = "高光",
        onValue1Changed = { value ->
            onParamsChanged(editParams.copy(highlights = value))
        },
        value2 = editParams.shadows,
        label2 = "阴影",
        onValue2Changed = { value ->
            onParamsChanged(editParams.copy(shadows = value))
        },
        valueRange = -100f..100f,
        modifier = modifier
    )
}

/**
 * 白色黑色面板
 */
@Composable
fun WhitesBlacksPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    DualSliderPanel(
        title = "白色黑色",
        value1 = editParams.whites,
        label1 = "白色",
        onValue1Changed = { value ->
            onParamsChanged(editParams.copy(whites = value))
        },
        value2 = editParams.blacks,
        label2 = "黑色",
        onValue2Changed = { value ->
            onParamsChanged(editParams.copy(blacks = value))
        },
        valueRange = -100f..100f,
        modifier = modifier
    )
}

/**
 * 对比度亮度面板
 */
@Composable
fun ContrastBrightnessPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    DualSliderPanel(
        title = "对比度亮度",
        value1 = editParams.contrast,
        label1 = "对比度",
        onValue1Changed = { value ->
            onParamsChanged(editParams.copy(contrast = value))
        },
        value2 = editParams.brightness,
        label2 = "亮度",
        onValue2Changed = { value ->
            onParamsChanged(editParams.copy(brightness = value))
        },
        valueRange = -100f..100f,
        modifier = modifier
    )
}

/**
 * 自然饱和度饱和度面板
 */
@Composable
fun VibranceSaturationPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    DualSliderPanel(
        title = "饱和度",
        value1 = editParams.vibrance,
        label1 = "自然饱和度",
        onValue1Changed = { value ->
            onParamsChanged(editParams.copy(vibrance = value))
        },
        value2 = editParams.saturation,
        label2 = "饱和度",
        onValue2Changed = { value ->
            onParamsChanged(editParams.copy(saturation = value))
        },
        valueRange = -100f..100f,
        modifier = modifier
    )
}

/**
 * 色温色调面板
 */
@Composable
fun TemperatureTintPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    DualSliderPanel(
        title = "色温色调",
        value1 = editParams.temperature,
        label1 = "色温",
        onValue1Changed = { value ->
            onParamsChanged(editParams.copy(temperature = value))
        },
        value2 = editParams.tint,
        label2 = "色调",
        onValue2Changed = { value ->
            onParamsChanged(editParams.copy(tint = value))
        },
        valueRange = -100f..100f,
        formatValue1 = { value ->
            when {
                value > 0 -> "暖色 +${value.toInt()}"
                value < 0 -> "冷色 ${value.toInt()}"
                else -> "0"
            }
        },
        formatValue2 = { value ->
            when {
                value > 0 -> "洋红 +${value.toInt()}"
                value < 0 -> "绿色 ${value.toInt()}"
                else -> "0"
            }
        },
        modifier = modifier
    )
}

/**
 * 锐度面板
 */
@Composable
fun SharpnessPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    SingleSliderPanel(
        title = "锐度",
        value = editParams.sharpness,
        valueRange = 0f..100f,
        onValueChanged = { value ->
            onParamsChanged(editParams.copy(sharpness = value))
        },
        modifier = modifier
    )
}

/**
 * 降噪面板
 */
@Composable
fun NoiseReductionPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    SingleSliderPanel(
        title = "降噪",
        value = editParams.noiseReduction,
        valueRange = 0f..100f,
        onValueChanged = { value ->
            onParamsChanged(editParams.copy(noiseReduction = value))
        },
        modifier = modifier
    )
}

/**
 * 清晰度去雾面板
 */
@Composable
fun ClarityDehazePanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    DualSliderPanel(
        title = "清晰度去雾",
        value1 = editParams.clarity,
        label1 = "清晰度",
        onValue1Changed = { value ->
            onParamsChanged(editParams.copy(clarity = value))
        },
        value2 = editParams.dehaze,
        label2 = "去雾",
        onValue2Changed = { value ->
            onParamsChanged(editParams.copy(dehaze = value))
        },
        valueRange = -100f..100f,
        modifier = modifier
    )
}

/**
 * 暗角面板
 */
@Composable
fun VignettePanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    SingleSliderPanel(
        title = "暗角",
        value = editParams.vignette,
        valueRange = -100f..100f,
        onValueChanged = { value ->
            onParamsChanged(editParams.copy(vignette = value))
        },
        modifier = modifier
    )
}

/**
 * 颗粒面板
 */
@Composable
fun GrainPanel(
    editParams: PhotoEditParams,
    onParamsChanged: (PhotoEditParams) -> Unit,
    modifier: Modifier = Modifier
) {
    SingleSliderPanel(
        title = "颗粒",
        value = editParams.grain,
        valueRange = 0f..100f,
        onValueChanged = { value ->
            onParamsChanged(editParams.copy(grain = value))
        },
        modifier = modifier
    )
}

// TODO: 实现其他面板组件
@Composable
fun FilmEmulationPanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    // TODO: 实现胶片模拟面板
}

@Composable
fun CropRotatePanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    // TODO: 实现裁剪旋转面板
}

@Composable
fun PerspectivePanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    // TODO: 实现透视调整面板
}

@Composable
fun RadialFilterPanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    // TODO: 实现径向滤镜面板
}

@Composable
fun GraduatedFilterPanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    // TODO: 实现渐变滤镜面板
}

@Composable
fun MaskingPanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    // TODO: 实现蒙版面板
}

@Composable
fun BrushPanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    // TODO: 实现画笔面板
}

@Composable
fun LUTFiltersPanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    val currentSettings = editParams.lutFilterSettings ?: LutFilterSettings()

    LUTFilterControlPanel(
        filter = currentSettings.selectedFilter ?: getDefaultLutFilter(),
        settings = currentSettings,
        onSettingsChanged = { newSettings ->
            onParamsChanged(editParams.copy(lutFilterSettings = newSettings))
        },
        modifier = modifier
    )
}

private fun getDefaultLutFilter(): LutFilter {
    return LutFilter(
        id = "default",
        name = "default",
        displayName = "选择滤镜",
        description = "请选择一个LUT滤镜",
        category = LutCategory.USER_CUSTOM,
        lutFilePath = ""
    )
}

@Composable
fun BorderWatermarkToolPanel(editParams: PhotoEditParams, onParamsChanged: (PhotoEditParams) -> Unit, modifier: Modifier = Modifier) {
    val currentSettings = editParams.borderWatermarkSettings ?: BorderWatermarkSettings()

    BorderWatermarkEditPanel(
        currentTab = BorderWatermarkTab.BOTH,
        settings = currentSettings,
        onSettingsChanged = { newSettings ->
            onParamsChanged(editParams.copy(borderWatermarkSettings = newSettings))
        },
        modifier = modifier
    )
}
