package com.qxyu.yucram.domain.model;

/**
 * LUT滤镜历史记录
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\b\n\u0002\b\u001d\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BW\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u000b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\u0002\u0010\u0011J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0007H\u00c6\u0003J\t\u0010&\u001a\u00020\tH\u00c6\u0003J\t\u0010\'\u001a\u00020\u000bH\u00c6\u0003J\t\u0010(\u001a\u00020\u000bH\u00c6\u0003J\t\u0010)\u001a\u00020\u000eH\u00c6\u0003J\u0010\u0010*\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003\u00a2\u0006\u0002\u0010 Jj\u0010+\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010H\u00c6\u0001\u00a2\u0006\u0002\u0010,J\u0013\u0010-\u001a\u00020.2\b\u0010/\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00100\u001a\u00020\u0010H\u00d6\u0001J\t\u00101\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0015R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\f\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0013R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0015\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\n\n\u0002\u0010!\u001a\u0004\b\u001f\u0010 \u00a8\u00062"}, d2 = {"Lcom/qxyu/yucram/domain/model/LUTFilterHistory;", "", "id", "", "photoId", "filterId", "settings", "Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "processingParams", "Lcom/qxyu/yucram/domain/model/LUTProcessingParams;", "appliedAt", "", "processingTime", "resultQuality", "", "userRating", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;Lcom/qxyu/yucram/domain/model/LUTProcessingParams;JJFLjava/lang/Integer;)V", "getAppliedAt", "()J", "getFilterId", "()Ljava/lang/String;", "getId", "getPhotoId", "getProcessingParams", "()Lcom/qxyu/yucram/domain/model/LUTProcessingParams;", "getProcessingTime", "getResultQuality", "()F", "getSettings", "()Lcom/qxyu/yucram/domain/model/LUTFilterSettings;", "getUserRating", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/qxyu/yucram/domain/model/LUTFilterSettings;Lcom/qxyu/yucram/domain/model/LUTProcessingParams;JJFLjava/lang/Integer;)Lcom/qxyu/yucram/domain/model/LUTFilterHistory;", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class LUTFilterHistory {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String photoId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String filterId = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LUTFilterSettings settings = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.LUTProcessingParams processingParams = null;
    private final long appliedAt = 0L;
    private final long processingTime = 0L;
    private final float resultQuality = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer userRating = null;
    
    public LUTFilterHistory(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    java.lang.String filterId, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings settings, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTProcessingParams processingParams, long appliedAt, long processingTime, float resultQuality, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userRating) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPhotoId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getFilterId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFilterSettings getSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTProcessingParams getProcessingParams() {
        return null;
    }
    
    public final long getAppliedAt() {
        return 0L;
    }
    
    public final long getProcessingTime() {
        return 0L;
    }
    
    public final float getResultQuality() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getUserRating() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFilterSettings component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTProcessingParams component5() {
        return null;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final float component8() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.LUTFilterHistory copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String photoId, @org.jetbrains.annotations.NotNull()
    java.lang.String filterId, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTFilterSettings settings, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.LUTProcessingParams processingParams, long appliedAt, long processingTime, float resultQuality, @org.jetbrains.annotations.Nullable()
    java.lang.Integer userRating) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}