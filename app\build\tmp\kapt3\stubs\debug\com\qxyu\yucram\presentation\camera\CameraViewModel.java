package com.qxyu.yucram.presentation.camera;

/**
 * 相机界面ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u0013\u001a\u00020\u0014J\f\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00170\u0016J\b\u0010\u0018\u001a\u00020\u0014H\u0014J\u000e\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u001a\u001a\u00020\u001bJ\u000e\u0010\u001c\u001a\u00020\u00142\u0006\u0010\u001d\u001a\u00020\u001eJ\u000e\u0010\u001f\u001a\u00020\u00142\u0006\u0010 \u001a\u00020\u0017J\u0006\u0010!\u001a\u00020\u0014J\u0006\u0010\"\u001a\u00020\u0014J\u0006\u0010#\u001a\u00020\u0014J\u0006\u0010$\u001a\u00020\u0014J\u0006\u0010%\u001a\u00020\u0014R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00070\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0017\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\rR\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\t0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\r\u00a8\u0006&"}, d2 = {"Lcom/qxyu/yucram/presentation/camera/CameraViewModel;", "Landroidx/lifecycle/ViewModel;", "cameraManager", "Lcom/qxyu/yucram/camera/CameraManager;", "(Lcom/qxyu/yucram/camera/CameraManager;)V", "_cameraSettings", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/domain/model/CameraSettings;", "_uiState", "Lcom/qxyu/yucram/presentation/camera/CameraUiState;", "cameraSettings", "Lkotlinx/coroutines/flow/StateFlow;", "getCameraSettings", "()Lkotlinx/coroutines/flow/StateFlow;", "cameraState", "Lcom/qxyu/yucram/camera/CameraState;", "getCameraState", "uiState", "getUiState", "capturePhoto", "", "getAvailableCameras", "", "", "onCleared", "setAspectRatio", "aspectRatio", "Lcom/qxyu/yucram/domain/model/AspectRatio;", "setTimer", "seconds", "", "switchCamera", "cameraId", "toggleFlashMode", "toggleGridLines", "toggleHdrMode", "toggleLevel", "toggleRawFormat", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class CameraViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.camera.CameraManager cameraManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.camera.CameraState> cameraState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.domain.model.CameraSettings> _cameraSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.CameraSettings> cameraSettings = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.camera.CameraUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.camera.CameraUiState> uiState = null;
    
    @javax.inject.Inject()
    public CameraViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.camera.CameraManager cameraManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.camera.CameraState> getCameraState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.domain.model.CameraSettings> getCameraSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.camera.CameraUiState> getUiState() {
        return null;
    }
    
    /**
     * 拍摄照片
     */
    public final void capturePhoto() {
    }
    
    /**
     * 切换闪光灯模式
     */
    public final void toggleFlashMode() {
    }
    
    /**
     * 切换画幅比例
     */
    public final void setAspectRatio(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.AspectRatio aspectRatio) {
    }
    
    /**
     * 切换网格线显示
     */
    public final void toggleGridLines() {
    }
    
    /**
     * 切换水平仪显示
     */
    public final void toggleLevel() {
    }
    
    /**
     * 设置定时器
     */
    public final void setTimer(int seconds) {
    }
    
    /**
     * 切换RAW格式
     */
    public final void toggleRawFormat() {
    }
    
    /**
     * 切换HDR模式
     */
    public final void toggleHdrMode() {
    }
    
    /**
     * 获取可用相机列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> getAvailableCameras() {
        return null;
    }
    
    /**
     * 切换相机
     */
    public final void switchCamera(@org.jetbrains.annotations.NotNull()
    java.lang.String cameraId) {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
}