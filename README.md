# Yucram V2.0.0 - 专业胶片相机应用

## 项目简介

Yucram V2.0.0 是一款高级安卓胶片相机应用程序，旨在利用色彩算法为用户提供真实的胶片摄影体验。该应用专注于高质量的胶片模拟效果、应用支持全程 60fps 的流畅拍摄体验。

## 技术栈

- **Kotlin**: 1.9.24
- **Compose BOM**: 2024.06.00
- **Compose编译器**: 1.5.14
- **Hilt**: 2.48
- **Room**: 2.6.1
- **Camera2**: 1.3.4
- **Target SDK**: 34 (Android 14)

## 项目架构

项目采用 **Clean Architecture** 和 **MVVM** 模式：

```
app/
├── src/main/java/com/qxyu/yucram/
│   ├── data/                    # 数据层
│   │   ├── local/              # 本地数据源
│   │   │   ├── dao/            # Room DAO
│   │   │   ├── entity/         # 数据库实体
│   │   │   └── YucramDatabase.kt
│   │   └── repository/         # Repository实现
│   ├── domain/                 # 领域层
│   │   ├── model/              # 领域模型
│   │   ├── repository/         # Repository接口
│   │   └── usecase/            # 用例
│   ├── presentation/           # 表现层
│   │   ├── camera/             # 相机界面
│   │   ├── gallery/            # 图库界面
│   │   └── settings/           # 设置界面
│   ├── ui/theme/               # UI主题
│   └── di/                     # 依赖注入
```

## 核心功能

### 1. 专业相机功能
- 60fps 实时预览
- RAW/LOG 格式拍摄
- 手动对焦和曝光控制
- 多种画幅比例支持 (1:1, 4:3, 3:2, 16:9)
- 网格线和水平仪辅助

### 2. 胶片模拟系统
- LUT/CUBE 文件支持
- 科学色彩算法处理
- 可调节滤镜强度
- 颗粒效果模拟
- 自定义滤镜导入

### 3. 专业图像编辑
- HSL 调整
- 色彩分级
- 色调曲线
- 裁剪和旋转
- 边框和水印

### 4. 现代化UI
- Material 3 设计
- 动态主题系统
- 流畅动画过渡
- 震动反馈支持

## 开发状态

### ✅ 已完成
- [x] 项目基础架构搭建
- [x] 依赖注入配置 (Hilt)
- [x] 数据库设计 (Room)
- [x] Repository 模式实现
- [x] 基础UI主题配置

### 🚧 进行中
- [ ] 权限管理系统
- [ ] 核心相机功能
- [ ] UI框架和导航

### 📋 待开发
- [ ] 胶片模拟系统
- [ ] 图库和编辑功能
- [ ] 设置和主题系统
- [ ] 性能优化和测试

## 如何运行

1. 在 Android Studio 中打开项目
2. 确保已安装 Android SDK 34
3. 连接 Android 14+ 设备或使用模拟器
4. 点击 Run 按钮

## 权限要求

- `CAMERA` - 相机访问
- `RECORD_AUDIO` - 录音权限
- `READ_MEDIA_IMAGES` - 读取图片 (Android 14+)
- `WRITE_EXTERNAL_STORAGE` - 存储写入 (Android 12 及以下)
- `ACCESS_FINE_LOCATION` - GPS 水印功能
- `VIBRATE` - 震动反馈

## 开发者

**QXyu** - Yucram V2.0.0

---

*这是一个正在开发中的项目，更多功能将陆续添加。*
