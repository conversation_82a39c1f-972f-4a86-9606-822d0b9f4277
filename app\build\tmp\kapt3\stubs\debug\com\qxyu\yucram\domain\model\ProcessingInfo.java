package com.qxyu.yucram.domain.model;

/**
 * 处理信息（Yucram专用）
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u001e\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Be\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0010J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u0010\u0010\"\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\u000b\u0010#\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\rH\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\u0005H\u00c6\u0003Jp\u0010\'\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u000e\u001a\u00020\u00052\b\b\u0002\u0010\u000f\u001a\u00020\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010(J\u0013\u0010)\u001a\u00020\u00052\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020,H\u00d6\u0001J\t\u0010-\u001a\u00020\bH\u00d6\u0001R\u0013\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u000e\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u000f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0014R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0004\u0010\u0014R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0015\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0018\u0010\u0019R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0017R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001d\u00a8\u0006."}, d2 = {"Lcom/qxyu/yucram/domain/model/ProcessingInfo;", "", "sourceFormat", "Lcom/qxyu/yucram/domain/model/ImageSourceFormat;", "isRawProcessed", "", "isLogProcessed", "lutApplied", "", "processingTime", "", "processingVersion", "adjustments", "Lcom/qxyu/yucram/domain/model/PhotoAdjustments;", "hasBorder", "hasWatermark", "(Lcom/qxyu/yucram/domain/model/ImageSourceFormat;ZZLjava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lcom/qxyu/yucram/domain/model/PhotoAdjustments;ZZ)V", "getAdjustments", "()Lcom/qxyu/yucram/domain/model/PhotoAdjustments;", "getHasBorder", "()Z", "getHasWatermark", "getLutApplied", "()Ljava/lang/String;", "getProcessingTime", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getProcessingVersion", "getSourceFormat", "()Lcom/qxyu/yucram/domain/model/ImageSourceFormat;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Lcom/qxyu/yucram/domain/model/ImageSourceFormat;ZZLjava/lang/String;Ljava/lang/Long;Ljava/lang/String;Lcom/qxyu/yucram/domain/model/PhotoAdjustments;ZZ)Lcom/qxyu/yucram/domain/model/ProcessingInfo;", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ProcessingInfo {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.domain.model.ImageSourceFormat sourceFormat = null;
    private final boolean isRawProcessed = false;
    private final boolean isLogProcessed = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String lutApplied = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long processingTime = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String processingVersion = null;
    @org.jetbrains.annotations.Nullable()
    private final com.qxyu.yucram.domain.model.PhotoAdjustments adjustments = null;
    private final boolean hasBorder = false;
    private final boolean hasWatermark = false;
    
    public ProcessingInfo(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ImageSourceFormat sourceFormat, boolean isRawProcessed, boolean isLogProcessed, @org.jetbrains.annotations.Nullable()
    java.lang.String lutApplied, @org.jetbrains.annotations.Nullable()
    java.lang.Long processingTime, @org.jetbrains.annotations.Nullable()
    java.lang.String processingVersion, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoAdjustments adjustments, boolean hasBorder, boolean hasWatermark) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ImageSourceFormat getSourceFormat() {
        return null;
    }
    
    public final boolean isRawProcessed() {
        return false;
    }
    
    public final boolean isLogProcessed() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getLutApplied() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getProcessingTime() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getProcessingVersion() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoAdjustments getAdjustments() {
        return null;
    }
    
    public final boolean getHasBorder() {
        return false;
    }
    
    public final boolean getHasWatermark() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ImageSourceFormat component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.qxyu.yucram.domain.model.PhotoAdjustments component7() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.qxyu.yucram.domain.model.ProcessingInfo copy(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.ImageSourceFormat sourceFormat, boolean isRawProcessed, boolean isLogProcessed, @org.jetbrains.annotations.Nullable()
    java.lang.String lutApplied, @org.jetbrains.annotations.Nullable()
    java.lang.Long processingTime, @org.jetbrains.annotations.Nullable()
    java.lang.String processingVersion, @org.jetbrains.annotations.Nullable()
    com.qxyu.yucram.domain.model.PhotoAdjustments adjustments, boolean hasBorder, boolean hasWatermark) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}