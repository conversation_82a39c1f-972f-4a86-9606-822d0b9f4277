package com.qxyu.yucram.presentation.album

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qxyu.yucram.domain.model.*

/**
 * 智能相册创建界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartAlbumScreen(
    onNavigateBack: () -> Unit = {},
    onAlbumCreated: (String) -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: SmartAlbumViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val rules by viewModel.rules.collectAsStateWithLifecycle()
    val previewPhotos by viewModel.previewPhotos.collectAsStateWithLifecycle()
    
    var albumName by remember { mutableStateOf("") }
    var albumDescription by remember { mutableStateOf("") }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部工具栏
        SmartAlbumTopBar(
            onNavigateBack = onNavigateBack,
            onSave = {
                if (albumName.isNotBlank() && rules.isNotEmpty()) {
                    viewModel.createSmartAlbum(albumName, albumDescription, rules)
                }
            },
            canSave = albumName.isNotBlank() && rules.isNotEmpty()
        )
        
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 基本信息
            item {
                SmartAlbumBasicInfo(
                    albumName = albumName,
                    onAlbumNameChanged = { albumName = it },
                    albumDescription = albumDescription,
                    onAlbumDescriptionChanged = { albumDescription = it }
                )
            }
            
            // 规则设置
            item {
                SmartAlbumRulesSection(
                    rules = rules,
                    onAddRule = { viewModel.addRule() },
                    onUpdateRule = { index, rule -> viewModel.updateRule(index, rule) },
                    onRemoveRule = { index -> viewModel.removeRule(index) }
                )
            }
            
            // 预览结果
            item {
                SmartAlbumPreview(
                    photos = previewPhotos,
                    isLoading = uiState.isLoadingPreview,
                    onRefreshPreview = { viewModel.refreshPreview() }
                )
            }
        }
    }
    
    // 处理创建结果
    LaunchedEffect(uiState.isCreated) {
        if (uiState.isCreated) {
            uiState.createdAlbumId?.let { albumId ->
                onAlbumCreated(albumId)
            }
        }
    }
}

/**
 * 智能相册顶部栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartAlbumTopBar(
    onNavigateBack: () -> Unit,
    onSave: () -> Unit,
    canSave: Boolean,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Text(
                text = "创建智能相册",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回"
                )
            }
        },
        actions = {
            TextButton(
                onClick = onSave,
                enabled = canSave
            ) {
                Text("创建")
            }
        },
        modifier = modifier
    )
}

/**
 * 智能相册基本信息
 */
@Composable
fun SmartAlbumBasicInfo(
    albumName: String,
    onAlbumNameChanged: (String) -> Unit,
    albumDescription: String,
    onAlbumDescriptionChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "基本信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            OutlinedTextField(
                value = albumName,
                onValueChange = onAlbumNameChanged,
                label = { Text("相册名称") },
                singleLine = true,
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            OutlinedTextField(
                value = albumDescription,
                onValueChange = onAlbumDescriptionChanged,
                label = { Text("描述（可选）") },
                maxLines = 3,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 智能相册规则区域
 */
@Composable
fun SmartAlbumRulesSection(
    rules: List<SmartAlbumRule>,
    onAddRule: () -> Unit,
    onUpdateRule: (Int, SmartAlbumRule) -> Unit,
    onRemoveRule: (Int) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "筛选规则",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                
                TextButton(onClick = onAddRule) {
                    Icon(
                        imageVector = Icons.Filled.Add,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("添加规则")
                }
            }
            
            if (rules.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Icon(
                            imageVector = Icons.Filled.FilterList,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "还没有规则",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Text(
                            text = "添加规则来自动筛选照片",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            } else {
                Spacer(modifier = Modifier.height(16.dp))
                
                rules.forEachIndexed { index, rule ->
                    SmartAlbumRuleItem(
                        rule = rule,
                        onUpdate = { updatedRule -> onUpdateRule(index, updatedRule) },
                        onRemove = { onRemoveRule(index) },
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }
            }
        }
    }
}

/**
 * 智能相册规则项
 */
@Composable
fun SmartAlbumRuleItem(
    rule: SmartAlbumRule,
    onUpdate: (SmartAlbumRule) -> Unit,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = rule.ruleType.displayName,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                IconButton(
                    onClick = onRemove,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Filled.Close,
                        contentDescription = "删除规则",
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 规则类型选择
            SmartAlbumRuleTypeSelector(
                selectedType = rule.ruleType,
                onTypeSelected = { newType ->
                    onUpdate(rule.copy(ruleType = newType))
                }
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // 规则值输入
            SmartAlbumRuleValueInput(
                ruleType = rule.ruleType,
                value = rule.value,
                onValueChanged = { newValue ->
                    onUpdate(rule.copy(value = newValue))
                }
            )
        }
    }
}

/**
 * 智能相册规则类型选择器
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SmartAlbumRuleTypeSelector(
    selectedType: SmartAlbumRuleType,
    onTypeSelected: (SmartAlbumRuleType) -> Unit,
    modifier: Modifier = Modifier
) {
    var expanded by remember { mutableStateOf(false) }

    ExposedDropdownMenuBox(
        expanded = expanded,
        onExpandedChange = { expanded = !expanded },
        modifier = modifier.fillMaxWidth()
    ) {
        OutlinedTextField(
            value = selectedType.displayName,
            onValueChange = { },
            readOnly = true,
            label = { Text("规则类型") },
            trailingIcon = {
                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
            },
            modifier = Modifier
                .fillMaxWidth()
                .menuAnchor()
        )

        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            SmartAlbumRuleType.values().forEach { ruleType ->
                DropdownMenuItem(
                    text = { Text(ruleType.displayName) },
                    onClick = {
                        onTypeSelected(ruleType)
                        expanded = false
                    }
                )
            }
        }
    }
}

/**
 * 智能相册规则值输入
 */
@Composable
fun SmartAlbumRuleValueInput(
    ruleType: SmartAlbumRuleType,
    value: String,
    onValueChanged: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    when (ruleType) {
        SmartAlbumRuleType.HAS_TAG -> {
            OutlinedTextField(
                value = value,
                onValueChange = onValueChanged,
                label = { Text("标签名称") },
                placeholder = { Text("输入标签名称") },
                modifier = modifier.fillMaxWidth()
            )
        }

        SmartAlbumRuleType.DATE_RANGE -> {
            // TODO: 实现日期范围选择器
            OutlinedTextField(
                value = value,
                onValueChange = onValueChanged,
                label = { Text("日期范围") },
                placeholder = { Text("选择日期范围") },
                modifier = modifier.fillMaxWidth()
            )
        }

        SmartAlbumRuleType.LOCATION -> {
            OutlinedTextField(
                value = value,
                onValueChange = onValueChanged,
                label = { Text("位置") },
                placeholder = { Text("输入位置名称") },
                modifier = modifier.fillMaxWidth()
            )
        }

        SmartAlbumRuleType.CAMERA_MODEL -> {
            OutlinedTextField(
                value = value,
                onValueChange = onValueChanged,
                label = { Text("相机型号") },
                placeholder = { Text("输入相机型号") },
                modifier = modifier.fillMaxWidth()
            )
        }

        SmartAlbumRuleType.IS_FAVORITE -> {
            // 收藏状态不需要输入值
            Text(
                text = "自动筛选收藏的照片",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        SmartAlbumRuleType.IS_YUCRAM_PHOTO -> {
            // Yucram照片不需要输入值
            Text(
                text = "自动筛选Yucram拍摄的照片",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        SmartAlbumRuleType.HAS_FILTER -> {
            OutlinedTextField(
                value = value,
                onValueChange = onValueChanged,
                label = { Text("滤镜名称") },
                placeholder = { Text("输入滤镜名称，留空表示任意滤镜") },
                modifier = modifier.fillMaxWidth()
            )
        }

        SmartAlbumRuleType.FILE_SIZE -> {
            OutlinedTextField(
                value = value,
                onValueChange = onValueChanged,
                label = { Text("文件大小") },
                placeholder = { Text("例如: >10MB, <5MB") },
                modifier = modifier.fillMaxWidth()
            )
        }

        // 其他规则类型的默认处理
        else -> {
            Text(
                text = "该规则类型暂未实现",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 智能相册预览
 */
@Composable
fun SmartAlbumPreview(
    photos: List<Photo>,
    isLoading: Boolean,
    onRefreshPreview: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "预览结果",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
                
                TextButton(onClick = onRefreshPreview) {
                    Icon(
                        imageVector = Icons.Filled.Refresh,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("刷新")
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            when {
                isLoading -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                photos.isEmpty() -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Icon(
                                imageVector = Icons.Filled.SearchOff,
                                contentDescription = null,
                                modifier = Modifier.size(32.dp),
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            
                            Spacer(modifier = Modifier.height(8.dp))
                            
                            Text(
                                text = "没有匹配的照片",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                else -> {
                    Text(
                        text = "找到 ${photos.size} 张匹配的照片",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // TODO: 显示照片预览网格
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(100.dp)
                            .background(
                                MaterialTheme.colorScheme.surfaceVariant,
                                RoundedCornerShape(8.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "照片预览",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}
