package com.qxyu.yucram.domain.model

/**
 * 滤镜数据模型
 */
data class Filter(
    val id: String,
    val name: String,
    val displayName: String,
    val description: String,
    val thumbnailPath: String,
    val lutFilePath: String? = null,
    val cubeFilePath: String? = null,
    val isBuiltIn: Boolean = true,
    val intensity: Float = 1.0f,
    val grainIntensity: Float = 0.0f,
    val vignetteIntensity: Float = 0.0f,
    val colorTemperatureAdjustment: Int = 0, // -1500 to +1500K
    val parameters: FilterParameters = FilterParameters()
)

/**
 * 滤镜参数
 */
data class FilterParameters(
    val exposure: Float = 0.0f,
    val contrast: Float = 0.0f,
    val brightness: Float = 0.0f,
    val saturation: Float = 0.0f,
    val highlights: Float = 0.0f,
    val shadows: Float = 0.0f,
    val clarity: Float = 0.0f,
    val vibrance: Float = 0.0f,
    val warmth: Float = 0.0f,
    val tint: Float = 0.0f
)
