package com.qxyu.yucram.presentation.filter;

/**
 * 滤镜选择界面的ViewModel
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u0007\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0017\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u0017\u001a\u00020\u0018J\u0006\u0010\u0019\u001a\u00020\u0018J\u000e\u0010\u001a\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001cJ\u0016\u0010\u001d\u001a\u00020\u00182\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001e\u001a\u00020\u001fJ \u0010 \u001a\u00020\u00182\u0006\u0010!\u001a\u00020\"2\b\u0010#\u001a\u0004\u0018\u00010\"2\u0006\u0010$\u001a\u00020\u001cJ\b\u0010%\u001a\u00020\u0018H\u0002J\u0006\u0010&\u001a\u00020\u0018J\u0006\u0010\'\u001a\u00020\u0018J\u000e\u0010(\u001a\u00020\u00182\u0006\u0010)\u001a\u00020\fJ\u0016\u0010*\u001a\u00020\u00182\u0006\u0010+\u001a\u00020\u001c2\u0006\u0010,\u001a\u00020-R\u0016\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u001d\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012\u00a8\u0006."}, d2 = {"Lcom/qxyu/yucram/presentation/filter/FilterSelectionViewModel;", "Landroidx/lifecycle/ViewModel;", "lutFileManager", "Lcom/qxyu/yucram/filter/LutFileManager;", "deviceCapabilityDetector", "Lcom/qxyu/yucram/filter/DeviceCapabilityDetector;", "(Lcom/qxyu/yucram/filter/LutFileManager;Lcom/qxyu/yucram/filter/DeviceCapabilityDetector;)V", "_deviceInfo", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/qxyu/yucram/filter/DeviceInfo;", "_filters", "", "Lcom/qxyu/yucram/domain/model/FilterPreset;", "_uiState", "Lcom/qxyu/yucram/presentation/filter/FilterSelectionUiState;", "deviceInfo", "Lkotlinx/coroutines/flow/StateFlow;", "getDeviceInfo", "()Lkotlinx/coroutines/flow/StateFlow;", "filters", "getFilters", "uiState", "getUiState", "clearError", "", "clearExportedPath", "deleteFilter", "filterId", "", "exportFilter", "outputDir", "Ljava/io/File;", "importLut", "lutFileUri", "Landroid/net/Uri;", "iconUri", "filterName", "loadDeviceInfo", "loadFilters", "resetPostProcessingParams", "selectFilter", "filter", "updatePostProcessingParam", "paramName", "value", "", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class FilterSelectionViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.filter.LutFileManager lutFileManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.qxyu.yucram.filter.DeviceCapabilityDetector deviceCapabilityDetector = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.presentation.filter.FilterSelectionUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.filter.FilterSelectionUiState> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.qxyu.yucram.domain.model.FilterPreset>> _filters = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.FilterPreset>> filters = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.qxyu.yucram.filter.DeviceInfo> _deviceInfo = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.filter.DeviceInfo> deviceInfo = null;
    
    @javax.inject.Inject()
    public FilterSelectionViewModel(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.filter.LutFileManager lutFileManager, @org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.filter.DeviceCapabilityDetector deviceCapabilityDetector) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.presentation.filter.FilterSelectionUiState> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.qxyu.yucram.domain.model.FilterPreset>> getFilters() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.qxyu.yucram.filter.DeviceInfo> getDeviceInfo() {
        return null;
    }
    
    /**
     * 加载滤镜列表
     */
    public final void loadFilters() {
    }
    
    /**
     * 选择滤镜
     */
    public final void selectFilter(@org.jetbrains.annotations.NotNull()
    com.qxyu.yucram.domain.model.FilterPreset filter) {
    }
    
    /**
     * 更新后处理参数
     */
    public final void updatePostProcessingParam(@org.jetbrains.annotations.NotNull()
    java.lang.String paramName, float value) {
    }
    
    /**
     * 导入LUT文件
     */
    public final void importLut(@org.jetbrains.annotations.NotNull()
    android.net.Uri lutFileUri, @org.jetbrains.annotations.Nullable()
    android.net.Uri iconUri, @org.jetbrains.annotations.NotNull()
    java.lang.String filterName) {
    }
    
    /**
     * 删除用户滤镜
     */
    public final void deleteFilter(@org.jetbrains.annotations.NotNull()
    java.lang.String filterId) {
    }
    
    /**
     * 导出滤镜
     */
    public final void exportFilter(@org.jetbrains.annotations.NotNull()
    java.lang.String filterId, @org.jetbrains.annotations.NotNull()
    java.io.File outputDir) {
    }
    
    /**
     * 重置参数
     */
    public final void resetPostProcessingParams() {
    }
    
    /**
     * 清除错误
     */
    public final void clearError() {
    }
    
    /**
     * 清除导出路径
     */
    public final void clearExportedPath() {
    }
    
    /**
     * 加载设备信息
     */
    private final void loadDeviceInfo() {
    }
}