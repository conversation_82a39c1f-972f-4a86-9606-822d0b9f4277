package com.qxyu.yucram

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.qxyu.yucram.camera.CameraManager
import com.qxyu.yucram.presentation.YucramApp
import com.qxyu.yucram.ui.theme.YucramTheme
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Yucram应用程序主Activity
 * 使用Compose构建UI
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var cameraManager: CameraManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 启用边到边显示
        enableEdgeToEdge()

        setContent {
            YucramTheme {
                Yu<PERSON>ramApp(cameraManager = cameraManager)
            }
        }
    }
}
