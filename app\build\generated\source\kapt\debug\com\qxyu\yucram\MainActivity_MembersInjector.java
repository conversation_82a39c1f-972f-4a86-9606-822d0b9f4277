// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.qxyu.yucram;

import com.qxyu.yucram.camera.CameraManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainActivity_MembersInjector implements MembersInjector<MainActivity> {
  private final Provider<CameraManager> cameraManagerProvider;

  public MainActivity_MembersInjector(Provider<CameraManager> cameraManagerProvider) {
    this.cameraManagerProvider = cameraManagerProvider;
  }

  public static MembersInjector<MainActivity> create(
      Provider<CameraManager> cameraManagerProvider) {
    return new MainActivity_MembersInjector(cameraManagerProvider);
  }

  @Override
  public void injectMembers(MainActivity instance) {
    injectCameraManager(instance, cameraManagerProvider.get());
  }

  @InjectedFieldSignature("com.qxyu.yucram.MainActivity.cameraManager")
  public static void injectCameraManager(MainActivity instance, CameraManager cameraManager) {
    instance.cameraManager = cameraManager;
  }
}
