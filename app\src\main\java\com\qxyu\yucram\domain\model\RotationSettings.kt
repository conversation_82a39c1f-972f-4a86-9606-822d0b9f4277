package com.qxyu.yucram.domain.model

import android.graphics.Matrix
import androidx.compose.runtime.Immutable
import kotlin.math.*

/**
 * 旋转设置数据模型
 */
@Immutable
data class RotationSettings(
    val rotation: Float = 0f, // 旋转角度，单位：度
    val flipHorizontal: Boolean = false, // 水平翻转
    val flipVertical: Boolean = false, // 垂直翻转
    val rotationMode: RotationMode = RotationMode.FREE,
    val snapAngle: Float = 15f, // 吸附角度
    val enableSnap: Boolean = true, // 启用角度吸附
    val showAngleIndicator: Boolean = true, // 显示角度指示器
    val autoStraighten: Boolean = false, // 自动拉直
    val straightenSensitivity: Float = 0.5f // 拉直敏感度
) {
    /**
     * 获取标准化的旋转角度（0-360度）
     */
    fun getNormalizedRotation(): Float {
        var angle = rotation % 360f
        if (angle < 0) angle += 360f
        return angle
    }
    
    /**
     * 获取最近的吸附角度
     */
    fun getSnappedRotation(): Float {
        if (!enableSnap) return rotation
        val normalized = getNormalizedRotation()
        val snapCount = (normalized / snapAngle).roundToInt()
        return snapCount * snapAngle
    }
    
    /**
     * 检查是否接近水平
     */
    fun isNearHorizontal(tolerance: Float = 2f): Boolean {
        val normalized = getNormalizedRotation()
        return normalized <= tolerance || 
               normalized >= (360f - tolerance) ||
               abs(normalized - 180f) <= tolerance
    }
    
    /**
     * 检查是否接近垂直
     */
    fun isNearVertical(tolerance: Float = 2f): Boolean {
        val normalized = getNormalizedRotation()
        return abs(normalized - 90f) <= tolerance ||
               abs(normalized - 270f) <= tolerance
    }
    
    /**
     * 获取变换矩阵
     */
    fun getTransformMatrix(centerX: Float, centerY: Float): Matrix {
        val matrix = Matrix()
        
        // 应用翻转
        val scaleX = if (flipHorizontal) -1f else 1f
        val scaleY = if (flipVertical) -1f else 1f
        
        if (scaleX != 1f || scaleY != 1f) {
            matrix.postScale(scaleX, scaleY, centerX, centerY)
        }
        
        // 应用旋转
        if (rotation != 0f) {
            matrix.postRotate(rotation, centerX, centerY)
        }
        
        return matrix
    }
    
    /**
     * 重置所有变换
     */
    fun reset(): RotationSettings {
        return copy(
            rotation = 0f,
            flipHorizontal = false,
            flipVertical = false
        )
    }
    
    /**
     * 检查是否有任何变换
     */
    fun hasTransformation(): Boolean {
        return rotation != 0f || flipHorizontal || flipVertical
    }
}

/**
 * 旋转模式
 */
enum class RotationMode(
    val displayName: String,
    val description: String,
    val allowedAngles: List<Float>? = null
) {
    FREE("自由旋转", "任意角度旋转", null),
    STEP_90("90度步进", "只能以90度为单位旋转", listOf(0f, 90f, 180f, 270f)),
    STEP_45("45度步进", "以45度为单位旋转", listOf(0f, 45f, 90f, 135f, 180f, 225f, 270f, 315f)),
    STEP_15("15度步进", "以15度为单位旋转", (0..23).map { it * 15f }),
    STEP_5("5度步进", "以5度为单位旋转", (0..71).map { it * 5f }),
    STRAIGHTEN("拉直模式", "小角度微调，用于拉直照片", null);
    
    /**
     * 获取最近的允许角度
     */
    fun getNearestAllowedAngle(angle: Float): Float {
        val allowedAngles = this.allowedAngles ?: return angle
        val normalized = ((angle % 360f) + 360f) % 360f
        
        return allowedAngles.minByOrNull { 
            val diff = abs(normalized - it)
            min(diff, 360f - diff)
        } ?: angle
    }
    
    /**
     * 检查角度是否被允许
     */
    fun isAngleAllowed(angle: Float, tolerance: Float = 1f): Boolean {
        val allowedAngles = this.allowedAngles ?: return true
        val normalized = ((angle % 360f) + 360f) % 360f
        
        return allowedAngles.any { 
            val diff = abs(normalized - it)
            min(diff, 360f - diff) <= tolerance
        }
    }
}

/**
 * 透视校正设置
 */
@Immutable
data class PerspectiveSettings(
    val topLeft: PerspectivePoint = PerspectivePoint(0f, 0f),
    val topRight: PerspectivePoint = PerspectivePoint(1f, 0f),
    val bottomLeft: PerspectivePoint = PerspectivePoint(0f, 1f),
    val bottomRight: PerspectivePoint = PerspectivePoint(1f, 1f),
    val isEnabled: Boolean = false,
    val showGrid: Boolean = true,
    val autoDetect: Boolean = false,
    val correctionStrength: Float = 1f // 校正强度 0-1
) {
    /**
     * 获取所有控制点
     */
    fun getAllPoints(): List<PerspectivePoint> {
        return listOf(topLeft, topRight, bottomLeft, bottomRight)
    }
    
    /**
     * 检查是否为矩形（无透视变形）
     */
    fun isRectangular(tolerance: Float = 0.01f): Boolean {
        return abs(topLeft.x - bottomLeft.x) <= tolerance &&
               abs(topRight.x - bottomRight.x) <= tolerance &&
               abs(topLeft.y - topRight.y) <= tolerance &&
               abs(bottomLeft.y - bottomRight.y) <= tolerance
    }
    
    /**
     * 重置为矩形
     */
    fun resetToRectangle(): PerspectiveSettings {
        return copy(
            topLeft = PerspectivePoint(0f, 0f),
            topRight = PerspectivePoint(1f, 0f),
            bottomLeft = PerspectivePoint(0f, 1f),
            bottomRight = PerspectivePoint(1f, 1f)
        )
    }
    
    /**
     * 获取透视变换矩阵
     */
    fun getPerspectiveMatrix(width: Float, height: Float): Matrix {
        val matrix = Matrix()
        
        if (!isEnabled || isRectangular()) {
            return matrix
        }
        
        // 源点（原始矩形）
        val src = floatArrayOf(
            0f, 0f,           // 左上
            width, 0f,        // 右上
            width, height,    // 右下
            0f, height        // 左下
        )
        
        // 目标点（变形后的四边形）
        val dst = floatArrayOf(
            topLeft.x * width, topLeft.y * height,
            topRight.x * width, topRight.y * height,
            bottomRight.x * width, bottomRight.y * height,
            bottomLeft.x * width, bottomLeft.y * height
        )
        
        matrix.setPolyToPoly(src, 0, dst, 0, 4)
        return matrix
    }
}

/**
 * 透视控制点
 */
@Immutable
data class PerspectivePoint(
    val x: Float, // 0-1 相对坐标
    val y: Float  // 0-1 相对坐标
) {
    /**
     * 限制在有效范围内
     */
    fun constrain(): PerspectivePoint {
        return copy(
            x = x.coerceIn(0f, 1f),
            y = y.coerceIn(0f, 1f)
        )
    }
    
    /**
     * 计算到另一点的距离
     */
    fun distanceTo(other: PerspectivePoint): Float {
        val dx = x - other.x
        val dy = y - other.y
        return sqrt(dx * dx + dy * dy)
    }
}

/**
 * 旋转操作历史
 */
@Immutable
data class RotationHistory(
    val id: String,
    val timestamp: Long,
    val rotationSettings: RotationSettings,
    val perspectiveSettings: PerspectiveSettings? = null,
    val description: String,
    val canUndo: Boolean = true
)

/**
 * 旋转预设
 */
@Immutable
data class RotationPreset(
    val id: String,
    val name: String,
    val description: String,
    val rotation: Float,
    val flipHorizontal: Boolean = false,
    val flipVertical: Boolean = false,
    val category: RotationPresetCategory = RotationPresetCategory.BASIC,
    val icon: String? = null,
    val isBuiltIn: Boolean = true
)

/**
 * 旋转预设分类
 */
enum class RotationPresetCategory(
    val displayName: String,
    val description: String
) {
    BASIC("基础", "基础旋转操作"),
    ORIENTATION("方向", "改变照片方向"),
    ARTISTIC("艺术", "艺术效果旋转"),
    CORRECTION("校正", "角度校正预设"),
    CUSTOM("自定义", "用户自定义预设")
}

/**
 * 自动拉直结果
 */
@Immutable
data class AutoStraightenResult(
    val suggestedAngle: Float,
    val confidence: Float, // 0-1，建议的置信度
    val detectedLines: List<DetectedLine> = emptyList(),
    val method: StraightenMethod
)

/**
 * 检测到的直线
 */
@Immutable
data class DetectedLine(
    val startX: Float,
    val startY: Float,
    val endX: Float,
    val endY: Float,
    val angle: Float,
    val strength: Float, // 线条强度
    val type: LineType
)

/**
 * 直线类型
 */
enum class LineType(
    val displayName: String
) {
    HORIZON("地平线"),
    VERTICAL("垂直线"),
    EDGE("边缘线"),
    BUILDING("建筑线"),
    OTHER("其他")
}

/**
 * 拉直方法
 */
enum class StraightenMethod(
    val displayName: String,
    val description: String
) {
    HORIZON_DETECTION("地平线检测", "基于地平线的自动拉直"),
    EDGE_DETECTION("边缘检测", "基于边缘线的拉直"),
    CONTENT_ANALYSIS("内容分析", "基于图像内容的智能拉直"),
    MANUAL("手动", "用户手动调整")
}
