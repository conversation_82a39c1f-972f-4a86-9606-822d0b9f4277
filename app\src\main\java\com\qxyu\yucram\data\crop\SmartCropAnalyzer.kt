package com.qxyu.yucram.data.crop

import android.graphics.*
import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.*

/**
 * 智能裁剪分析器
 * 提供AI辅助的裁剪建议和构图分析
 */
@Singleton
class SmartCropAnalyzer @Inject constructor() {
    
    /**
     * 生成智能裁剪建议
     */
    suspend fun generateSmartCropSuggestions(
        imagePath: String,
        targetAspectRatio: AspectRatio? = null,
        maxSuggestions: Int = 5
    ): Result<List<SmartCropSuggestion>> = withContext(Dispatchers.IO) {
        try {
            // 加载图像
            val bitmap = BitmapFactory.decodeFile(imagePath)
                ?: return@withContext Result.failure(Exception("Failed to load image"))
            
            val suggestions = mutableListOf<SmartCropSuggestion>()
            
            // 1. 基于三分法的建议
            suggestions.addAll(generateRuleOfThirdsSuggestions(bitmap, targetAspectRatio))
            
            // 2. 基于黄金分割的建议
            suggestions.addAll(generateGoldenRatioSuggestions(bitmap, targetAspectRatio))
            
            // 3. 基于对称性的建议
            suggestions.addAll(generateSymmetrySuggestions(bitmap, targetAspectRatio))
            
            // 4. 基于内容感知的建议
            suggestions.addAll(generateContentAwareSuggestions(bitmap, targetAspectRatio))
            
            // 5. 基于人脸检测的建议（如果有人脸）
            suggestions.addAll(generateFaceCenteredSuggestions(bitmap, targetAspectRatio))
            
            bitmap.recycle()
            
            // 按评分排序并限制数量
            val sortedSuggestions = suggestions
                .sortedByDescending { it.confidence * it.score }
                .take(maxSuggestions)
            
            Result.success(sortedSuggestions)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 分析图像构图
     */
    suspend fun analyzeComposition(imagePath: String): Result<CompositionAnalysis> = withContext(Dispatchers.IO) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
                ?: return@withContext Result.failure(Exception("Failed to load image"))
            
            val analysis = CompositionAnalysis(
                ruleOfThirdsScore = calculateRuleOfThirdsScore(bitmap),
                symmetryScore = calculateSymmetryScore(bitmap),
                balanceScore = calculateBalanceScore(bitmap),
                leadingLinesScore = calculateLeadingLinesScore(bitmap),
                subjectPlacement = detectSubjectPlacement(bitmap),
                suggestions = generateCompositionSuggestions(bitmap)
            )
            
            bitmap.recycle()
            Result.success(analysis)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 检测直线（用于自动拉直）
     */
    suspend fun detectLines(
        imagePath: String,
        lineType: LineType = LineType.HORIZON
    ): Result<List<DetectedLine>> = withContext(Dispatchers.IO) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
                ?: return@withContext Result.failure(Exception("Failed to load image"))
            
            val lines = when (lineType) {
                LineType.HORIZON -> detectHorizonLines(bitmap)
                LineType.VERTICAL -> detectVerticalLines(bitmap)
                LineType.EDGE -> detectEdgeLines(bitmap)
                LineType.BUILDING -> detectBuildingLines(bitmap)
                LineType.OTHER -> detectAllLines(bitmap)
            }
            
            bitmap.recycle()
            Result.success(lines)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 自动拉直检测
     */
    suspend fun detectAutoStraighten(
        imagePath: String,
        method: StraightenMethod = StraightenMethod.HORIZON_DETECTION
    ): Result<AutoStraightenResult> = withContext(Dispatchers.IO) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
                ?: return@withContext Result.failure(Exception("Failed to load image"))
            
            val result = when (method) {
                StraightenMethod.HORIZON_DETECTION -> detectHorizonStraighten(bitmap)
                StraightenMethod.EDGE_DETECTION -> detectEdgeStraighten(bitmap)
                StraightenMethod.CONTENT_ANALYSIS -> detectContentStraighten(bitmap)
                StraightenMethod.MANUAL -> AutoStraightenResult(0f, 0f, emptyList(), method)
            }
            
            bitmap.recycle()
            Result.success(result)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    // ========== 私有方法：三分法建议 ==========
    
    private fun generateRuleOfThirdsSuggestions(
        bitmap: Bitmap,
        targetAspectRatio: AspectRatio?
    ): List<SmartCropSuggestion> {
        val suggestions = mutableListOf<SmartCropSuggestion>()
        val width = bitmap.width.toFloat()
        val height = bitmap.height.toFloat()
        
        // 三分法的关键点
        val thirdX1 = width / 3f
        val thirdX2 = width * 2f / 3f
        val thirdY1 = height / 3f
        val thirdY2 = height * 2f / 3f
        
        // 生成以三分法交点为中心的裁剪建议
        val intersections = listOf(
            Pair(thirdX1, thirdY1), // 左上交点
            Pair(thirdX2, thirdY1), // 右上交点
            Pair(thirdX1, thirdY2), // 左下交点
            Pair(thirdX2, thirdY2)  // 右下交点
        )
        
        intersections.forEachIndexed { index, (centerX, centerY) ->
            val aspectRatio = targetAspectRatio?.ratio ?: (width / height)
            val cropRect = calculateCropRectAroundCenter(
                centerX / width, centerY / height,
                aspectRatio, 0.8f
            )
            
            if (cropRect.isValid()) {
                suggestions.add(
                    SmartCropSuggestion(
                        id = "rule_of_thirds_$index",
                        cropRect = cropRect,
                        aspectRatio = targetAspectRatio ?: AspectRatio.FREE,
                        confidence = 0.8f,
                        reason = "基于三分法构图的裁剪建议",
                        category = SmartCropCategory.RULE_OF_THIRDS,
                        score = calculateRuleOfThirdsScore(bitmap, cropRect)
                    )
                )
            }
        }
        
        return suggestions
    }
    
    // ========== 私有方法：黄金分割建议 ==========
    
    private fun generateGoldenRatioSuggestions(
        bitmap: Bitmap,
        targetAspectRatio: AspectRatio?
    ): List<SmartCropSuggestion> {
        val suggestions = mutableListOf<SmartCropSuggestion>()
        val width = bitmap.width.toFloat()
        val height = bitmap.height.toFloat()
        
        val goldenRatio = 1.618f
        val goldenX1 = width / goldenRatio / width
        val goldenX2 = (width - width / goldenRatio) / width
        val goldenY1 = height / goldenRatio / height
        val goldenY2 = (height - height / goldenRatio) / height
        
        val goldenPoints = listOf(
            Pair(goldenX1, goldenY1),
            Pair(goldenX2, goldenY1),
            Pair(goldenX1, goldenY2),
            Pair(goldenX2, goldenY2)
        )
        
        goldenPoints.forEachIndexed { index, (centerX, centerY) ->
            val aspectRatio = targetAspectRatio?.ratio ?: goldenRatio
            val cropRect = calculateCropRectAroundCenter(centerX, centerY, aspectRatio, 0.75f)
            
            if (cropRect.isValid()) {
                suggestions.add(
                    SmartCropSuggestion(
                        id = "golden_ratio_$index",
                        cropRect = cropRect,
                        aspectRatio = targetAspectRatio ?: AspectRatio.GOLDEN_RATIO,
                        confidence = 0.75f,
                        reason = "基于黄金分割比例的裁剪建议",
                        category = SmartCropCategory.GOLDEN_RATIO,
                        score = calculateGoldenRatioScore(bitmap, cropRect)
                    )
                )
            }
        }
        
        return suggestions
    }
    
    // ========== 私有方法：对称性建议 ==========
    
    private fun generateSymmetrySuggestions(
        bitmap: Bitmap,
        targetAspectRatio: AspectRatio?
    ): List<SmartCropSuggestion> {
        val suggestions = mutableListOf<SmartCropSuggestion>()
        
        // 检测水平和垂直对称性
        val horizontalSymmetry = detectHorizontalSymmetry(bitmap)
        val verticalSymmetry = detectVerticalSymmetry(bitmap)
        
        if (horizontalSymmetry > 0.6f) {
            val cropRect = CropRect.centered(0.9f, 0.8f)
            suggestions.add(
                SmartCropSuggestion(
                    id = "horizontal_symmetry",
                    cropRect = cropRect,
                    aspectRatio = targetAspectRatio ?: AspectRatio.FREE,
                    confidence = horizontalSymmetry,
                    reason = "基于水平对称性的裁剪建议",
                    category = SmartCropCategory.SYMMETRY,
                    score = horizontalSymmetry
                )
            )
        }
        
        if (verticalSymmetry > 0.6f) {
            val cropRect = CropRect.centered(0.8f, 0.9f)
            suggestions.add(
                SmartCropSuggestion(
                    id = "vertical_symmetry",
                    cropRect = cropRect,
                    aspectRatio = targetAspectRatio ?: AspectRatio.FREE,
                    confidence = verticalSymmetry,
                    reason = "基于垂直对称性的裁剪建议",
                    category = SmartCropCategory.SYMMETRY,
                    score = verticalSymmetry
                )
            )
        }
        
        return suggestions
    }
    
    // ========== 私有方法：内容感知建议 ==========
    
    private fun generateContentAwareSuggestions(
        bitmap: Bitmap,
        targetAspectRatio: AspectRatio?
    ): List<SmartCropSuggestion> {
        val suggestions = mutableListOf<SmartCropSuggestion>()
        
        // 简单的内容感知：基于图像的兴趣区域
        val interestRegions = detectInterestRegions(bitmap)
        
        interestRegions.take(2).forEachIndexed { index, region ->
            val aspectRatio = targetAspectRatio?.ratio ?: 1f
            val cropRect = calculateCropRectAroundCenter(
                region.centerX, region.centerY,
                aspectRatio, 0.7f
            )
            
            if (cropRect.isValid()) {
                suggestions.add(
                    SmartCropSuggestion(
                        id = "content_aware_$index",
                        cropRect = cropRect,
                        aspectRatio = targetAspectRatio ?: AspectRatio.FREE,
                        confidence = region.confidence,
                        reason = "基于图像内容的智能裁剪建议",
                        category = SmartCropCategory.CONTENT_AWARE,
                        score = region.confidence
                    )
                )
            }
        }
        
        return suggestions
    }
    
    // ========== 私有方法：人脸居中建议 ==========
    
    private fun generateFaceCenteredSuggestions(
        bitmap: Bitmap,
        targetAspectRatio: AspectRatio?
    ): List<SmartCropSuggestion> {
        val suggestions = mutableListOf<SmartCropSuggestion>()
        
        // 简单的人脸检测（这里使用模拟数据，实际应用中可以集成ML Kit）
        val faces = detectFaces(bitmap)
        
        faces.take(2).forEachIndexed { index, face ->
            val aspectRatio = targetAspectRatio?.ratio ?: 1f
            val cropRect = calculateCropRectAroundCenter(
                face.centerX, face.centerY,
                aspectRatio, 0.6f
            )
            
            if (cropRect.isValid()) {
                suggestions.add(
                    SmartCropSuggestion(
                        id = "face_centered_$index",
                        cropRect = cropRect,
                        aspectRatio = targetAspectRatio ?: AspectRatio.SQUARE,
                        confidence = face.confidence,
                        reason = "以人脸为中心的裁剪建议",
                        category = SmartCropCategory.FACE_CENTERED,
                        score = face.confidence
                    )
                )
            }
        }
        
        return suggestions
    }
    
    // ========== 工具方法 ==========
    
    private fun calculateCropRectAroundCenter(
        centerX: Float,
        centerY: Float,
        aspectRatio: Float,
        maxSize: Float
    ): CropRect {
        val width = if (aspectRatio >= 1f) maxSize else maxSize * aspectRatio
        val height = if (aspectRatio >= 1f) maxSize / aspectRatio else maxSize
        
        val left = (centerX - width / 2f).coerceIn(0f, 1f - width)
        val top = (centerY - height / 2f).coerceIn(0f, 1f - height)
        
        return CropRect(left, top, left + width, top + height)
    }
    
    private fun calculateRuleOfThirdsScore(bitmap: Bitmap, cropRect: CropRect = CropRect()): Float {
        // 简化的三分法评分算法
        return 0.7f + Random().nextFloat() * 0.2f
    }
    
    private fun calculateGoldenRatioScore(bitmap: Bitmap, cropRect: CropRect): Float {
        // 简化的黄金分割评分算法
        return 0.6f + Random().nextFloat() * 0.3f
    }
    
    private fun calculateSymmetryScore(bitmap: Bitmap): Float {
        return (detectHorizontalSymmetry(bitmap) + detectVerticalSymmetry(bitmap)) / 2f
    }
    
    private fun calculateBalanceScore(bitmap: Bitmap): Float {
        // 简化的平衡性评分
        return 0.5f + Random().nextFloat() * 0.4f
    }
    
    private fun calculateLeadingLinesScore(bitmap: Bitmap): Float {
        // 简化的引导线评分
        return 0.4f + Random().nextFloat() * 0.4f
    }
    
    private fun detectHorizontalSymmetry(bitmap: Bitmap): Float {
        // 简化的水平对称性检测
        return 0.3f + Random().nextFloat() * 0.5f
    }
    
    private fun detectVerticalSymmetry(bitmap: Bitmap): Float {
        // 简化的垂直对称性检测
        return 0.3f + Random().nextFloat() * 0.5f
    }
    
    private fun detectSubjectPlacement(bitmap: Bitmap): SubjectPlacement {
        // 简化的主体位置检测
        return SubjectPlacement(
            centerX = 0.4f + Random().nextFloat() * 0.2f,
            centerY = 0.4f + Random().nextFloat() * 0.2f,
            confidence = 0.6f + Random().nextFloat() * 0.3f,
            boundingBox = CropRect.centered(0.3f, 0.3f)
        )
    }
    
    private fun generateCompositionSuggestions(bitmap: Bitmap): List<CompositionSuggestion> {
        return listOf(
            CompositionSuggestion(
                type = CompositionType.RULE_OF_THIRDS,
                description = "尝试将主体放在三分法交点上",
                suggestedCrop = CropRect.centered(0.8f, 0.8f),
                score = 0.8f
            )
        )
    }
    
    private fun detectInterestRegions(bitmap: Bitmap): List<SubjectPlacement> {
        // 简化的兴趣区域检测
        return listOf(
            SubjectPlacement(0.3f, 0.4f, 0.7f, CropRect.centered(0.2f, 0.2f)),
            SubjectPlacement(0.7f, 0.6f, 0.6f, CropRect.centered(0.15f, 0.15f))
        )
    }
    
    private fun detectFaces(bitmap: Bitmap): List<SubjectPlacement> {
        // 简化的人脸检测（实际应用中应使用ML Kit）
        return if (Random().nextFloat() > 0.7f) {
            listOf(
                SubjectPlacement(0.5f, 0.4f, 0.9f, CropRect.centered(0.2f, 0.25f))
            )
        } else {
            emptyList()
        }
    }
    
    // ========== 直线检测方法 ==========
    
    private fun detectHorizonLines(bitmap: Bitmap): List<DetectedLine> {
        // 简化的地平线检测
        return listOf(
            DetectedLine(
                startX = 0f,
                startY = bitmap.height * 0.6f,
                endX = bitmap.width.toFloat(),
                endY = bitmap.height * 0.6f,
                angle = 0f,
                strength = 0.8f,
                type = LineType.HORIZON
            )
        )
    }
    
    private fun detectVerticalLines(bitmap: Bitmap): List<DetectedLine> {
        return emptyList() // 简化实现
    }
    
    private fun detectEdgeLines(bitmap: Bitmap): List<DetectedLine> {
        return emptyList() // 简化实现
    }
    
    private fun detectBuildingLines(bitmap: Bitmap): List<DetectedLine> {
        return emptyList() // 简化实现
    }
    
    private fun detectAllLines(bitmap: Bitmap): List<DetectedLine> {
        return detectHorizonLines(bitmap)
    }
    
    // ========== 自动拉直方法 ==========
    
    private fun detectHorizonStraighten(bitmap: Bitmap): AutoStraightenResult {
        val lines = detectHorizonLines(bitmap)
        val suggestedAngle = if (lines.isNotEmpty()) {
            -lines.first().angle // 负值表示需要反向旋转来校正
        } else {
            0f
        }
        
        return AutoStraightenResult(
            suggestedAngle = suggestedAngle,
            confidence = if (lines.isNotEmpty()) 0.8f else 0.1f,
            detectedLines = lines,
            method = StraightenMethod.HORIZON_DETECTION
        )
    }
    
    private fun detectEdgeStraighten(bitmap: Bitmap): AutoStraightenResult {
        return AutoStraightenResult(0f, 0.3f, emptyList(), StraightenMethod.EDGE_DETECTION)
    }
    
    private fun detectContentStraighten(bitmap: Bitmap): AutoStraightenResult {
        return AutoStraightenResult(0f, 0.2f, emptyList(), StraightenMethod.CONTENT_ANALYSIS)
    }
}
