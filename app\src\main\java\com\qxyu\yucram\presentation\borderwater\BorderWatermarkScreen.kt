package com.qxyu.yucram.presentation.borderwater

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.qxyu.yucram.domain.model.*
import com.qxyu.yucram.presentation.borderwater.components.*

/**
 * 边框和水印编辑界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BorderWatermarkScreen(
    photoId: String,
    onNavigateBack: () -> Unit = {},
    onSaveComplete: () -> Unit = {},
    modifier: Modifier = Modifier,
    viewModel: BorderWatermarkViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val photo by viewModel.photo.collectAsStateWithLifecycle()
    val settings by viewModel.settings.collectAsStateWithLifecycle()
    val currentTab by viewModel.currentTab.collectAsStateWithLifecycle()
    
    var showSaveDialog by remember { mutableStateOf(false) }
    var showPresetDialog by remember { mutableStateOf(false) }
    
    LaunchedEffect(photoId) {
        viewModel.loadPhoto(photoId)
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // 顶部工具栏
        BorderWatermarkTopBar(
            photo = photo,
            hasChanges = viewModel.hasChanges(),
            onNavigateBack = {
                if (viewModel.hasChanges()) {
                    showSaveDialog = true
                } else {
                    onNavigateBack()
                }
            },
            onReset = { viewModel.resetSettings() },
            onSave = { viewModel.saveSettings() },
            onPresets = { showPresetDialog = true }
        )
        
        // 主要预览区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            when {
                uiState.isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                photo != null -> {
                    // 照片预览区域
                    BorderWatermarkPreview(
                        photo = photo!!,
                        settings = settings,
                        modifier = Modifier.fillMaxSize()
                    )
                }
                
                uiState.error != null -> {
                    BorderWatermarkError(
                        error = uiState.error!!,
                        onRetry = { viewModel.loadPhoto(photoId) },
                        onNavigateBack = onNavigateBack,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }
        
        // 功能选项卡
        BorderWatermarkTabs(
            currentTab = currentTab,
            onTabSelected = { tab ->
                viewModel.selectTab(tab)
            },
            modifier = Modifier.fillMaxWidth()
        )
        
        // 编辑面板
        BorderWatermarkEditPanel(
            currentTab = currentTab,
            settings = settings,
            onSettingsChanged = { newSettings ->
                viewModel.updateSettings(newSettings)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(280.dp)
        )
    }
    
    // 保存确认对话框
    if (showSaveDialog) {
        SaveConfirmationDialog(
            onSave = {
                viewModel.saveSettings()
                showSaveDialog = false
                onSaveComplete()
            },
            onDiscard = {
                showSaveDialog = false
                onNavigateBack()
            },
            onCancel = {
                showSaveDialog = false
            }
        )
    }
    
    // 预设选择对话框
    if (showPresetDialog) {
        PresetSelectionDialog(
            currentTab = currentTab,
            onPresetSelected = { preset ->
                viewModel.applyPreset(preset)
                showPresetDialog = false
            },
            onDismiss = {
                showPresetDialog = false
            }
        )
    }
    
    // 处理保存完成
    LaunchedEffect(uiState.isSaved) {
        if (uiState.isSaved) {
            onSaveComplete()
        }
    }
}

/**
 * 边框水印顶部工具栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BorderWatermarkTopBar(
    photo: Photo?,
    hasChanges: Boolean,
    onNavigateBack: () -> Unit,
    onReset: () -> Unit,
    onSave: () -> Unit,
    onPresets: () -> Unit,
    modifier: Modifier = Modifier
) {
    TopAppBar(
        title = {
            Column {
                Text(
                    text = photo?.fileName ?: "边框水印",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White
                )
                if (hasChanges) {
                    Text(
                        text = "已修改",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = Color.White
                )
            }
        },
        actions = {
            // 预设
            IconButton(onClick = onPresets) {
                Icon(
                    imageVector = Icons.Filled.Palette,
                    contentDescription = "预设",
                    tint = Color.White
                )
            }
            
            // 重置
            IconButton(
                onClick = onReset,
                enabled = hasChanges
            ) {
                Icon(
                    imageVector = Icons.Filled.Refresh,
                    contentDescription = "重置",
                    tint = if (hasChanges) Color.White else Color.White.copy(alpha = 0.3f)
                )
            }
            
            // 保存
            IconButton(
                onClick = onSave,
                enabled = hasChanges
            ) {
                Icon(
                    imageVector = Icons.Filled.Check,
                    contentDescription = "保存",
                    tint = if (hasChanges) MaterialTheme.colorScheme.primary else Color.White.copy(alpha = 0.3f)
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Black.copy(alpha = 0.8f)
        ),
        modifier = modifier
    )
}

/**
 * 功能选项卡
 */
@Composable
fun BorderWatermarkTabs(
    currentTab: BorderWatermarkTab,
    onTabSelected: (BorderWatermarkTab) -> Unit,
    modifier: Modifier = Modifier
) {
    val tabs = listOf(
        BorderWatermarkTab.BORDER to "边框",
        BorderWatermarkTab.WATERMARK to "水印",
        BorderWatermarkTab.BOTH to "组合"
    )
    
    LazyRow(
        modifier = modifier
            .background(Color.Black.copy(alpha = 0.8f))
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(tabs) { (tab, name) ->
            BorderWatermarkTabButton(
                tab = tab,
                name = name,
                isSelected = currentTab == tab,
                onClick = { onTabSelected(tab) }
            )
        }
    }
}

/**
 * 选项卡按钮
 */
@Composable
fun BorderWatermarkTabButton(
    tab: BorderWatermarkTab,
    name: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primary
            } else {
                Color.Transparent
            },
            contentColor = if (isSelected) {
                MaterialTheme.colorScheme.onPrimary
            } else {
                Color.White
            }
        ),
        modifier = modifier
    ) {
        Icon(
            imageVector = when (tab) {
                BorderWatermarkTab.BORDER -> Icons.Filled.CropFree
                BorderWatermarkTab.WATERMARK -> Icons.Filled.TextFields
                BorderWatermarkTab.BOTH -> Icons.Filled.Layers
            },
            contentDescription = null,
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = name,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal
        )
    }
}

/**
 * 保存确认对话框
 */
@Composable
fun SaveConfirmationDialog(
    onSave: () -> Unit,
    onDiscard: () -> Unit,
    onCancel: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onCancel,
        title = {
            Text("保存更改？")
        },
        text = {
            Text("您有未保存的更改，是否要保存？")
        },
        confirmButton = {
            TextButton(onClick = onSave) {
                Text("保存")
            }
        },
        dismissButton = {
            Row {
                TextButton(onClick = onDiscard) {
                    Text("放弃")
                }
                TextButton(onClick = onCancel) {
                    Text("取消")
                }
            }
        }
    )
}

/**
 * 预设选择对话框
 */
@Composable
fun PresetSelectionDialog(
    currentTab: BorderWatermarkTab,
    onPresetSelected: (Any) -> Unit, // BorderPreset 或 WatermarkPreset
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                when (currentTab) {
                    BorderWatermarkTab.BORDER -> "边框预设"
                    BorderWatermarkTab.WATERMARK -> "水印预设"
                    BorderWatermarkTab.BOTH -> "组合预设"
                }
            )
        },
        text = {
            // TODO: 实现预设选择界面
            Text("预设选择功能开发中...")
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("确定")
            }
        }
    )
}

/**
 * 边框水印错误状态
 */
@Composable
fun BorderWatermarkError(
    error: String,
    onRetry: () -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Filled.Error,
            contentDescription = null,
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "加载失败",
            style = MaterialTheme.typography.headlineSmall,
            color = Color.White
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.7f)
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedButton(
                onClick = onNavigateBack,
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = Color.White
                )
            ) {
                Text("返回")
            }
            
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}

/**
 * 边框水印选项卡
 */
enum class BorderWatermarkTab {
    BORDER,         // 边框
    WATERMARK,      // 水印
    BOTH            // 组合
}
