package com.qxyu.yucram.navigation

import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import com.qxyu.yucram.presentation.animation.NavigationAnimations
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.qxyu.yucram.camera.CameraManager
import com.qxyu.yucram.presentation.camera.CameraScreen
import com.qxyu.yucram.presentation.gallery.GalleryScreen
import com.qxyu.yucram.presentation.permission.PermissionScreen
import com.qxyu.yucram.presentation.permission.PermissionViewModel
import com.qxyu.yucram.presentation.settings.SettingsScreen

/**
 * Yucram应用导航图
 */
@Composable
fun YucramNavigation(
    navController: NavHostController = rememberNavController(),
    cameraManager: CameraManager
) {
    val permissionViewModel: PermissionViewModel = hiltViewModel()
    val permissionState by permissionViewModel.permissionState.collectAsState()
    
    // 根据权限状态决定起始页面，默认相机页面
    val startDestination = if (permissionState.allCorePermissionsGranted) {
        YucramDestinations.CAMERA_ROUTE
    } else {
        YucramDestinations.PERMISSION_ROUTE
    }
    
    NavHost(
        navController = navController,
        startDestination = startDestination,
        enterTransition = {
            NavigationAnimations.slideInHorizontally() + NavigationAnimations.fadeIn()
        },
        exitTransition = {
            NavigationAnimations.slideOutHorizontally() + NavigationAnimations.fadeOut()
        },
        popEnterTransition = {
            NavigationAnimations.slideInHorizontally(initialOffsetX = { -it }) + NavigationAnimations.fadeIn()
        },
        popExitTransition = {
            NavigationAnimations.slideOutHorizontally(targetOffsetX = { it }) + NavigationAnimations.fadeOut()
        }
    ) {
        // 权限页面
        composable(
            route = YucramDestinations.PERMISSION_ROUTE,
            enterTransition = { fadeIn(animationSpec = tween(300)) },
            exitTransition = { fadeOut(animationSpec = tween(300)) }
        ) {
            PermissionScreen(
                onPermissionsGranted = {
                    navController.navigate(YucramDestinations.CAMERA_ROUTE) {
                        popUpTo(YucramDestinations.PERMISSION_ROUTE) {
                            inclusive = true
                        }
                    }
                }
            )
        }
        
        // 相机页面
        composable(
            route = YucramDestinations.CAMERA_ROUTE,
            enterTransition = {
                if (initialState.destination.route == YucramDestinations.PERMISSION_ROUTE) {
                    fadeIn(animationSpec = tween(500))
                } else {
                    slideInVertically(
                        initialOffsetY = { it },
                        animationSpec = tween(300)
                    )
                }
            },
            exitTransition = {
                slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(300)
                )
            }
        ) {
            CameraScreen()
        }
        
        // 图库页面
        composable(
            route = YucramDestinations.GALLERY_ROUTE,
            enterTransition = {
                slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300)
                )
            },
            exitTransition = {
                slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(300)
                )
            }
        ) {
            GalleryScreen(
                onNavigateToPhotoDetail = { photoId ->
                    navController.navigate("photo_detail/$photoId")
                },
                onNavigateToCamera = {
                    navController.navigate(YucramDestinations.CAMERA_ROUTE)
                }
            )
        }
        
        // 设置页面
        composable(
            route = YucramDestinations.SETTINGS_ROUTE,
            enterTransition = {
                slideInVertically(
                    initialOffsetY = { it },
                    animationSpec = tween(300)
                )
            },
            exitTransition = {
                slideOutVertically(
                    targetOffsetY = { it },
                    animationSpec = tween(300)
                )
            }
        ) {
            SettingsScreen(
                onNavigateToThemeSettings = {
                    navController.navigate(YucramDestinations.THEME_SETTINGS_ROUTE)
                },
                onNavigateToAbout = {
                    navController.navigate(YucramDestinations.ABOUT_ROUTE)
                }
            )
        }
        
        // 照片详情页面
        composable(
            route = YucramDestinations.PHOTO_DETAIL_WITH_ID,
            enterTransition = {
                scaleIn(
                    initialScale = 0.8f,
                    animationSpec = tween(300)
                ) + fadeIn(animationSpec = tween(300))
            },
            exitTransition = {
                scaleOut(
                    targetScale = 0.8f,
                    animationSpec = tween(300)
                ) + fadeOut(animationSpec = tween(300))
            }
        ) { backStackEntry ->
            val photoId = backStackEntry.arguments?.getString(YucramDestinations.PHOTO_ID_KEY)
            // PhotoDetailScreen(photoId = photoId)
        }
        
        // 照片编辑页面
        composable(
            route = YucramDestinations.PHOTO_EDIT_WITH_ID,
            enterTransition = {
                slideInHorizontally(
                    initialOffsetX = { it },
                    animationSpec = tween(400)
                )
            },
            exitTransition = {
                slideOutHorizontally(
                    targetOffsetX = { it },
                    animationSpec = tween(400)
                )
            }
        ) { backStackEntry ->
            val photoId = backStackEntry.arguments?.getString(YucramDestinations.PHOTO_ID_KEY)
            // PhotoEditScreen(photoId = photoId)
        }
    }
}
