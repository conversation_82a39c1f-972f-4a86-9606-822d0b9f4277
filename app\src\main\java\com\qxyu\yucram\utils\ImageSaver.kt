package com.qxyu.yucram.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.ImageFormat
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.DngCreator
import android.hardware.camera2.TotalCaptureResult
import android.media.Image
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * 图像保存工具类
 * 支持JPEG和RAW(DNG)格式保存
 */
class ImageSaver(private val context: Context) {
    
    companion object {
        private const val TAG = "ImageSaver"
        private const val JPEG_QUALITY = 100
    }
    
    /**
     * 保存JPEG图像到相册
     */
    suspend fun saveJpegImage(
        imageBytes: ByteArray,
        filename: String? = null
    ): Result<Uri> = withContext(Dispatchers.IO) {
        try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val displayName = filename ?: "YUCRAM_$timestamp.jpg"
            
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, displayName)
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_DCIM + "/Yucram")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }
            }
            
            val resolver = context.contentResolver
            val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                ?: return@withContext Result.failure(IOException("Failed to create MediaStore entry"))
            
            resolver.openOutputStream(uri)?.use { outputStream ->
                outputStream.write(imageBytes)
                outputStream.flush()
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                contentValues.clear()
                contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                resolver.update(uri, contentValues, null, null)
            }
            
            Log.d(TAG, "JPEG image saved: $uri")
            Result.success(uri)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save JPEG image", e)
            Result.failure(e)
        }
    }
    
    /**
     * 保存RAW图像为DNG格式到相册
     */
    suspend fun saveRawImage(
        image: Image,
        characteristics: CameraCharacteristics,
        captureResult: TotalCaptureResult,
        filename: String? = null
    ): Result<Uri> = withContext(Dispatchers.IO) {
        try {
            if (image.format != ImageFormat.RAW_SENSOR) {
                return@withContext Result.failure(IllegalArgumentException("Image must be RAW_SENSOR format"))
            }
            
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val displayName = filename ?: "YUCRAM_RAW_$timestamp.dng"
            
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, displayName)
                put(MediaStore.Images.Media.MIME_TYPE, "image/x-adobe-dng")
                put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_DCIM + "/Yucram")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }
            }
            
            val resolver = context.contentResolver
            val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                ?: return@withContext Result.failure(IOException("Failed to create MediaStore entry"))
            
            resolver.openOutputStream(uri)?.use { outputStream ->
                // 使用DngCreator创建标准DNG文件
                val dngCreator = DngCreator(characteristics, captureResult)
                dngCreator.setOrientation(android.media.ExifInterface.ORIENTATION_NORMAL)
                dngCreator.writeImage(outputStream, image)
                dngCreator.close()
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                contentValues.clear()
                contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                resolver.update(uri, contentValues, null, null)
            }
            
            Log.d(TAG, "RAW DNG image saved: $uri")
            Result.success(uri)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save RAW image", e)
            Result.failure(e)
        }
    }
    
    /**
     * 保存HDR图像到相册
     */
    suspend fun saveHdrImage(
        imageBytes: ByteArray,
        filename: String? = null
    ): Result<Uri> = withContext(Dispatchers.IO) {
        try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val displayName = filename ?: "YUCRAM_HDR_$timestamp.jpg"
            
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, displayName)
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_DCIM + "/Yucram")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }
            }
            
            val resolver = context.contentResolver
            val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)
                ?: return@withContext Result.failure(IOException("Failed to create MediaStore entry"))
            
            resolver.openOutputStream(uri)?.use { outputStream ->
                outputStream.write(imageBytes)
                outputStream.flush()
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                contentValues.clear()
                contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                resolver.update(uri, contentValues, null, null)
            }
            
            Log.d(TAG, "HDR image saved: $uri")
            Result.success(uri)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save HDR image", e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查设备是否支持RAW格式
     */
    fun isRawSupported(characteristics: CameraCharacteristics): Boolean {
        val map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
        val rawSizes = map?.getOutputSizes(ImageFormat.RAW_SENSOR)
        return rawSizes != null && rawSizes.isNotEmpty()
    }
    
    /**
     * 检查设备是否支持HDR
     */
    fun isHdrSupported(characteristics: CameraCharacteristics): Boolean {
        val availableCapabilities = characteristics.get(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES)
        return availableCapabilities?.contains(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_BURST_CAPTURE) == true
    }
}
