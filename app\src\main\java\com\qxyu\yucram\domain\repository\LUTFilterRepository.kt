package com.qxyu.yucram.domain.repository

import com.qxyu.yucram.domain.model.*
import kotlinx.coroutines.flow.Flow

/**
 * LUT滤镜Repository接口
 */
interface LutFilterRepository {
    
    // ========== 滤镜管理 ==========
    
    /**
     * 获取所有LUT滤镜
     */
    suspend fun getAllFilters(): Flow<List<LutFilter>>
    
    /**
     * 根据ID获取LUT滤镜
     */
    suspend fun getFilterById(id: String): LUTFilter?
    
    /**
     * 根据分类获取LUT滤镜
     */
    suspend fun getFiltersByCategory(category: LUTCategory): Flow<List<LUTFilter>>
    
    /**
     * 搜索LUT滤镜
     */
    suspend fun searchFilters(criteria: LUTFilterSearchCriteria): Flow<List<LUTFilter>>
    
    /**
     * 获取内置滤镜
     */
    suspend fun getBuiltInFilters(): Flow<List<LUTFilter>>
    
    /**
     * 获取用户自定义滤镜
     */
    suspend fun getUserCustomFilters(): Flow<List<LUTFilter>>
    
    /**
     * 获取付费滤镜
     */
    suspend fun getPremiumFilters(): Flow<List<LUTFilter>>
    
    /**
     * 添加LUT滤镜
     */
    suspend fun addFilter(filter: LUTFilter): Result<String>
    
    /**
     * 更新LUT滤镜
     */
    suspend fun updateFilter(filter: LUTFilter): Result<Unit>
    
    /**
     * 删除LUT滤镜
     */
    suspend fun deleteFilter(id: String): Result<Unit>
    
    /**
     * 导入LUT文件
     */
    suspend fun importLUTFile(
        filePath: String,
        name: String,
        category: LUTCategory,
        description: String? = null
    ): Result<LUTFilter>
    
    /**
     * 导出LUT滤镜
     */
    suspend fun exportFilter(id: String, outputPath: String): Result<String>
    
    // ========== 滤镜包管理 ==========
    
    /**
     * 获取所有滤镜包
     */
    suspend fun getAllFilterPacks(): Flow<List<LUTFilterPack>>
    
    /**
     * 根据ID获取滤镜包
     */
    suspend fun getFilterPackById(id: String): LUTFilterPack?
    
    /**
     * 下载滤镜包
     */
    suspend fun downloadFilterPack(
        pack: LUTFilterPack,
        onProgress: (Float) -> Unit = {}
    ): Result<Unit>
    
    /**
     * 安装滤镜包
     */
    suspend fun installFilterPack(packId: String): Result<List<LUTFilter>>
    
    /**
     * 卸载滤镜包
     */
    suspend fun uninstallFilterPack(packId: String): Result<Unit>
    
    // ========== 滤镜应用 ==========
    
    /**
     * 应用LUT滤镜到照片
     */
    suspend fun applyFilterToPhoto(
        photoPath: String,
        filter: LUTFilter,
        settings: LUTFilterSettings,
        processingParams: LUTProcessingParams,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String>
    
    /**
     * 批量应用LUT滤镜
     */
    suspend fun applyFilterBatch(
        photoPaths: List<String>,
        filter: LUTFilter,
        settings: LUTFilterSettings,
        processingParams: LUTProcessingParams,
        outputDir: String,
        onProgress: (Float) -> Unit = {}
    ): Result<List<String>>
    
    /**
     * 预览LUT滤镜效果
     */
    suspend fun previewFilter(
        photoPath: String,
        filter: LUTFilter,
        settings: LUTFilterSettings,
        processingParams: LUTProcessingParams
    ): Result<String>
    
    /**
     * 生成滤镜对比图
     */
    suspend fun generateBeforeAfterComparison(
        photoPath: String,
        filter: LUTFilter,
        settings: LUTFilterSettings,
        processingParams: LUTProcessingParams
    ): Result<String>
    
    // ========== LUT分析 ==========
    
    /**
     * 分析LUT文件
     */
    suspend fun analyzeLUTFile(filePath: String): Result<LUTAnalysisResult>
    
    /**
     * 验证LUT文件
     */
    suspend fun validateLUTFile(filePath: String): Result<Boolean>
    
    /**
     * 获取LUT文件信息
     */
    suspend fun getLUTFileInfo(filePath: String): Result<Map<String, Any>>
    
    /**
     * 生成LUT缩略图
     */
    suspend fun generateLUTThumbnail(
        filter: LUTFilter,
        sampleImagePath: String? = null
    ): Result<String>
    
    // ========== 滤镜预设 ==========
    
    /**
     * 获取所有滤镜预设
     */
    suspend fun getAllPresets(): Flow<List<LUTFilterPreset>>
    
    /**
     * 根据ID获取滤镜预设
     */
    suspend fun getPresetById(id: String): LUTFilterPreset?
    
    /**
     * 创建滤镜预设
     */
    suspend fun createPreset(preset: LUTFilterPreset): Result<String>
    
    /**
     * 更新滤镜预设
     */
    suspend fun updatePreset(preset: LUTFilterPreset): Result<Unit>
    
    /**
     * 删除滤镜预设
     */
    suspend fun deletePreset(id: String): Result<Unit>
    
    /**
     * 应用滤镜预设
     */
    suspend fun applyPreset(
        photoPath: String,
        preset: LUTFilterPreset,
        outputPath: String,
        onProgress: (Float) -> Unit = {}
    ): Result<String>
    
    // ========== 历史记录 ==========
    
    /**
     * 获取滤镜使用历史
     */
    suspend fun getFilterHistory(photoId: String): Flow<List<LUTFilterHistory>>
    
    /**
     * 添加滤镜历史记录
     */
    suspend fun addFilterHistory(history: LUTFilterHistory): Result<Unit>
    
    /**
     * 删除滤镜历史记录
     */
    suspend fun deleteFilterHistory(id: String): Result<Unit>
    
    /**
     * 清空滤镜历史记录
     */
    suspend fun clearFilterHistory(): Result<Unit>
    
    // ========== 统计信息 ==========
    
    /**
     * 获取滤镜统计信息
     */
    suspend fun getFilterStats(filterId: String): LUTFilterStats?
    
    /**
     * 更新滤镜统计信息
     */
    suspend fun updateFilterStats(stats: LUTFilterStats): Result<Unit>
    
    /**
     * 获取热门滤镜
     */
    suspend fun getPopularFilters(limit: Int = 10): Flow<List<LUTFilter>>
    
    /**
     * 获取最近使用的滤镜
     */
    suspend fun getRecentlyUsedFilters(limit: Int = 10): Flow<List<LUTFilter>>
    
    /**
     * 获取推荐滤镜
     */
    suspend fun getRecommendedFilters(
        photoPath: String? = null,
        limit: Int = 10
    ): Flow<List<LUTFilter>>
    
    // ========== 用户评分 ==========
    
    /**
     * 对滤镜进行评分
     */
    suspend fun rateFilter(filterId: String, rating: Int): Result<Unit>
    
    /**
     * 获取滤镜评分
     */
    suspend fun getFilterRating(filterId: String): Float?
    
    /**
     * 获取用户对滤镜的评分
     */
    suspend fun getUserFilterRating(filterId: String): Int?
    
    // ========== 缓存管理 ==========
    
    /**
     * 清理滤镜缓存
     */
    suspend fun clearFilterCache(): Result<Unit>
    
    /**
     * 获取缓存大小
     */
    suspend fun getCacheSize(): Long
    
    /**
     * 预加载常用滤镜
     */
    suspend fun preloadPopularFilters(): Result<Unit>
    
    // ========== 在线功能 ==========
    
    /**
     * 同步在线滤镜库
     */
    suspend fun syncOnlineFilters(): Result<Unit>
    
    /**
     * 检查滤镜更新
     */
    suspend fun checkFilterUpdates(): Result<List<LUTFilter>>
    
    /**
     * 上传用户滤镜
     */
    suspend fun uploadUserFilter(
        filter: LUTFilter,
        onProgress: (Float) -> Unit = {}
    ): Result<String>
    
    /**
     * 分享滤镜
     */
    suspend fun shareFilter(filterId: String): Result<String>
}
